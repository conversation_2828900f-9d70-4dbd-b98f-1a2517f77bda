# Payment Gateway Implementation Guide

## Overview

This document provides a comprehensive guide to the payment gateway implementation in the Marvel package, with a focus on how different payment gateways handle amount formatting and currency conversion.

## Table of Contents

1. [Supported Payment Gateways](#supported-payment-gateways)
2. [Common Implementation Pattern](#common-implementation-pattern)
3. [Amount Handling Across Gateways](#amount-handling-across-gateways)
4. [Stripe-Specific Implementation](#stripe-specific-implementation)
5. [Testing Payment Gateways](#testing-payment-gateways)
6. [Troubleshooting Common Issues](#troubleshooting-common-issues)

## Supported Payment Gateways

The Marvel package supports multiple payment gateways:

- Stripe
- PayPal
- Mollie
- Razorpay
- Paystack
- Xendit
- Iyzico
- Bkash
- Paymongo
- Flutterwave

Each gateway has its own implementation in the `packages/marvel/src/Payment/` directory.

## Common Implementation Pattern

All payment gateways follow a common implementation pattern:

1. Each gateway extends the `Base` class and implements the `PaymentInterface`
2. Required methods include:
   - `getIntent()`: Creates a payment intent/order
   - `verify()`: Verifies a payment
   - `handleWebHooks()`: Processes webhook events from the gateway

```php
class ExampleGateway extends Base implements PaymentInterface
{
    public function getIntent($data): array
    {
        // Gateway-specific implementation
    }
    
    public function verify($id): bool
    {
        // Gateway-specific implementation
    }
    
    public function handleWebHooks($request): void
    {
        // Gateway-specific implementation
    }
}
```

## Amount Handling Across Gateways

Different payment gateways have different requirements for how amounts should be formatted:

### Stripe and Paystack

Require amounts in the smallest currency unit (cents, pence, etc.):

```php
// Convert from dollars to cents
'amount' => round($amount, 2) * 100
```

### Mollie

Requires amounts as decimal strings:

```php
// Format as decimal string
"amount" => [
  "currency" => $this->currency,
  "value" => number_format($amount, 2)
]
```

### PayPal, Razorpay, and Others

Generally accept amounts in the main currency unit (dollars, pounds, etc.):

```php
// No conversion needed
'amount' => round($amount, 2)
```

## Stripe-Specific Implementation

### Current Implementation

In `packages/marvel/src/Payment/Stripe.php`, the `getIntent()` method handles amount conversion:

```php
public function getIntent($data): array
{
  try {
    extract($data);
    $intent_array = [];
    $intent_array = [
      'amount' => round($amount, 2) * 100, // Convert to cents
      'currency' => $this->currency,
      'description' => 'Marvel Payment',
      // ...
    ];
    
    // Rest of the method...
  }
}
```

### The Issue

If the frontend sends an amount of 100 (intending $100.00), but the amount is already in cents (10000), the backend will multiply by 100 again, resulting in 1000000 cents ($10,000.00).

### Solution

Decide on a consistent approach:

1. **Frontend sends main currency unit (dollars)**:
   - Frontend: `amount: 100.00` for $100.00
   - Backend: Keep the multiplication by 100

2. **Frontend sends smallest currency unit (cents)**:
   - Frontend: `amount: 10000` for $100.00
   - Backend: Remove the multiplication by 100

## Payment Flow Implementation

### 1. Create Payment Intent

```php
// In OrderController.php or similar
public function createPayment(Request $request)
{
    $data = [
        'amount' => $request->amount, // Make sure format is consistent
        'order_tracking_number' => $request->order_id,
    ];
    
    try {
        $paymentGateway = new \Marvel\Payments\Stripe(); // Or other gateway
        $intent = $paymentGateway->getIntent($data);
        
        // Store the payment intent
        PaymentIntent::create([
            'order_id' => $request->order_id,
            'tracking_number' => $request->order_id,
            'payment_gateway' => 'STRIPE', // Or other gateway
            'payment_intent_info' => $intent,
        ]);
        
        // Return necessary data to frontend
        return response()->json([
            'client_secret' => $intent['client_secret'] ?? null,
            'redirect_url' => $intent['redirect_url'] ?? null,
            'is_redirect' => $intent['is_redirect'] ?? false,
            'payment_id' => $intent['payment_id'] ?? null,
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
        ], 400);
    }
}
```

### 2. Process Webhook Events

```php
// In WebHookController.php
public function stripe(Request $request)
{
    return Payment::handleWebHooks($request);
}
```

## Testing Payment Gateways

### Test Environment Setup

1. Create test accounts for each payment gateway
2. Configure test API keys in your environment
3. Use test card numbers provided by each gateway

### Testing Checklist

- [ ] Test successful payment flow
- [ ] Test failed payment (insufficient funds)
- [ ] Test payment with decimal amounts
- [ ] Test webhook handling
- [ ] Test refund process

## Troubleshooting Common Issues

### Amount Conversion Issues

- **Symptom**: Customers charged incorrect amounts
- **Check**: Verify how amounts are being sent from frontend and processed in backend
- **Solution**: Ensure consistent formatting (main currency unit or smallest currency unit)

### Webhook Processing Failures

- **Symptom**: Order status not updating after payment
- **Check**: Verify webhook URL is correctly configured and accessible
- **Solution**: Check webhook logs in payment gateway dashboard

### Payment Intent Creation Failures

- **Symptom**: Error when creating payment intent
- **Check**: Verify API keys and required parameters
- **Solution**: Check payment gateway documentation for required fields

## Conclusion

Implementing payment gateways requires careful attention to how each gateway handles amounts and currencies. By establishing a consistent approach to amount formatting between frontend and backend, you can avoid common issues like the 100x multiplication problem in Stripe.

Document your chosen approach clearly for all developers working on the project to ensure consistency.
