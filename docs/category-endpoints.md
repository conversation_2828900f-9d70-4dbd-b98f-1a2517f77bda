# Category Endpoints Documentation

This document provides information about the category-related endpoints available in the API.

## Base URL

All endpoints are relative to the base API URL.

## Available Endpoints

### 1. Get All Categories

```
GET /categories
```

Returns a paginated list of all categories.

#### Query Parameters

- `page` (optional): Page number for pagination. Default is 1.
- `limit` (optional): Number of categories to return per page. Default is 15.
- `language` (optional): Language code for the categories. Default is the system's default language.
- `parent` (optional): Filter categories by parent. Use 'null' to get only top-level categories.
- `self` (optional): Exclude a specific category ID from the results.

#### Response

Returns a paginated list of categories with their details.

### 2. Get a Specific Category

```
GET /categories/{id_or_slug}
```

Returns details of a specific category identified by its ID or slug.

#### Response

Returns the category details including its type, parent category, and children.

### 3. Get Featured Categories

```
GET /featured-categories
```

Returns a list of featured categories.

#### Response

Returns a list of featured categories with their associated products.

### 4. Get Recommended Categories for User

```
GET /recommended-categories
```

Returns a list of categories recommended for the current user based on their order history or popular categories if the user is not logged in.

#### Query Parameters

- `page` (optional): Page number for pagination. Default is 1.
- `limit` (optional): Number of categories to return. Default is 5.
- `language` (optional): Language code for the categories. Default is the system's default language.

#### Response

For logged-in users, returns categories based on their order history, supplemented with popular categories if needed.
For non-logged-in users, returns popular categories.

### 5. Get Trending Categories

```
GET /trending-categories
```

Returns the most trending categories based on recent orders.

#### Query Parameters

- `page` (optional): Page number for pagination. Default is 1.
- `limit` (optional): Number of categories to return. Default is 3.
- `language` (optional): Language code for the categories. Default is the system's default language.
- `days` (optional): Number of days to consider for trending calculation. Default is 30.

#### Response

Returns a list of trending categories sorted by the number of recent orders.

### 6. Get Popular Categories

```
GET /popular-categories
```

Returns the most popular categories based on the number of products they contain.

#### Query Parameters

- `page` (optional): Page number for pagination. Default is 1.
- `limit` (optional): Number of categories to return. Default is 10.
- `language` (optional): Language code for the categories. Default is the system's default language.

#### Response

Returns a list of popular categories sorted by the number of products they contain.

## Authentication

- Endpoints 1, 2, 3, 5, and 6 are publicly accessible.
- Endpoint 4 (recommended categories) works for both authenticated and non-authenticated users, but provides personalized recommendations for authenticated users.

## Error Responses

All endpoints may return the following error responses:

- `404 Not Found`: The requested resource was not found.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Example Usage

### Get Recommended Categories

```javascript
// Example using fetch API
fetch('/api/recommended-categories?page=1&limit=5')
  .then(response => response.json())
  .then(data => {
    console.log(data);
  })
  .catch(error => {
    console.error('Error:', error);
  });
```

### Get Trending Categories

```javascript
// Example using fetch API
fetch('/api/trending-categories?page=1&limit=3&days=7')
  .then(response => response.json())
  .then(data => {
    console.log(data);
  })
  .catch(error => {
    console.error('Error:', error);
  });
```

### Get Popular Categories

```javascript
// Example using fetch API
fetch('/api/popular-categories?page=1&limit=10')
  .then(response => response.json())
  .then(data => {
    console.log(data);
  })
  .catch(error => {
    console.error('Error:', error);
  });
```
