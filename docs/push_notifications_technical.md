# Push Notification System: Technical Documentation

This document provides a technical overview of the push notification system implemented in the Marvel package, focusing on the delivery system feature.

## System Architecture

The push notification system follows a layered architecture:

1. **Data Layer**:
   - `DeviceToken` model for storing device tokens
   - `DeviceTokenRepository` for database operations

2. **API Layer**:
   - `DeviceTokenController` for handling API requests
   - `DeviceTokenRequest` for request validation

3. **Notification Layer**:
   - `FcmChannel` for sending notifications via Firebase Cloud Messaging
   - Notification classes for formatting notifications

4. **Event Layer**:
   - Event classes for triggering notifications
   - Listener classes for handling events

## Database Schema

### Device Tokens Table

The `device_tokens` table stores user device tokens for push notifications:

```sql
CREATE TABLE `device_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `token` varchar(255) NOT NULL,
  `device_type` varchar(255) DEFAULT NULL,
  `device_id` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `device_tokens_token_unique` (`token`),
  KEY `device_tokens_user_id_foreign` (`user_id`),
  CONSTRAINT `device_tokens_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
);
```

## Class Diagrams

### Data Layer

```
+-------------------+       +------------------------+
| DeviceToken       |       | DeviceTokenRepository  |
+-------------------+       +------------------------+
| - user_id         |       | + model()              |
| - token           |       | + boot()               |
| - device_type     |       | + fieldSearchable      |
| - device_id       |       +------------------------+
| - is_active       |
+-------------------+
| + user()          |
+-------------------+
```

### Notification Layer

```
+-------------------+       +------------------------+       +------------------------+
| FcmChannel        |       | DeliveryNotification   |       | DeviceToken           |
+-------------------+       +------------------------+       +------------------------+
| + send()          |------>| + via()                |------>| - user_id             |
| + sendNotification|       | + toFcm()              |       | - token               |
+-------------------+       | + toMail()             |       | - device_type         |
                            | + toArray()            |       | - device_id           |
                            +------------------------+       | - is_active           |
                                                             +------------------------+
```

### Event Layer

```
+-------------------+       +------------------------+       +------------------------+
| DeliveryEvent     |       | EventListener          |       | Notification           |
+-------------------+       +------------------------+       +------------------------+
| - delivery        |------>| + handle()             |------>| + via()                |
| - user            |       |                        |       | + toFcm()              |
+-------------------+       +------------------------+       +------------------------+
```

## Sequence Diagrams

### Device Token Registration

```
+--------+    +-------------------+    +------------------------+    +------------+
| Client |    | DeviceTokenCtrl   |    | DeviceTokenRepository  |    | Database   |
+--------+    +-------------------+    +------------------------+    +------------+
    |                 |                           |                        |
    | POST /device-tokens                         |                        |
    |---------------->|                           |                        |
    |                 | validate()                |                        |
    |                 |---------------------------|                        |
    |                 |                           |                        |
    |                 | create()                  |                        |
    |                 |-------------------------->|                        |
    |                 |                           | save()                 |
    |                 |                           |----------------------->|
    |                 |                           |                        |
    |                 |                           |        saved           |
    |                 |                           |<-----------------------|
    |                 |         created           |                        |
    |                 |<--------------------------|                        |
    |     response    |                           |                        |
    |<----------------|                           |                        |
    |                 |                           |                        |
```

### Push Notification Flow

```
+-------------------+    +-------------------+    +-------------------+    +------------+    +-------+
| DeliveryRepository|    | Event             |    | Listener          |    | FcmChannel |    | FCM   |
+-------------------+    +-------------------+    +-------------------+    +------------+    +-------+
         |                        |                        |                     |               |
         | event()                |                        |                     |               |
         |----------------------->|                        |                     |               |
         |                        | handle()               |                     |               |
         |                        |----------------------->|                     |               |
         |                        |                        | notify()            |               |
         |                        |                        |-------------------->|               |
         |                        |                        |                     | send()        |
         |                        |                        |                     |-------------->|
         |                        |                        |                     |               |
         |                        |                        |                     |    response   |
         |                        |                        |                     |<--------------|
         |                        |                        |                     |               |
```

## Code Examples

### Device Token Model

```php
namespace Marvel\Database\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeviceToken extends Model
{
    protected $table = 'device_tokens';

    protected $fillable = [
        'user_id',
        'token',
        'device_type',
        'device_id',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
```

### FCM Channel

```php
namespace Marvel\Notifications\Channels;

use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Marvel\Database\Models\DeviceToken;

class FcmChannel
{
    /**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return void
     */
    public function send($notifiable, Notification $notification)
    {
        if (!method_exists($notification, 'toFcm')) {
            return;
        }

        $message = $notification->toFcm($notifiable);

        if (empty($message)) {
            return;
        }

        // Get device tokens for the user
        $tokens = DeviceToken::where('user_id', $notifiable->id)
            ->where('is_active', true)
            ->pluck('token')
            ->toArray();

        if (empty($tokens)) {
            return;
        }

        $this->sendNotification($tokens, $message);
    }

    /**
     * Send notification to FCM
     *
     * @param array $tokens
     * @param array $message
     * @return void
     */
    protected function sendNotification(array $tokens, array $message)
    {
        $fcmServerKey = config('services.fcm.server_key');

        if (empty($fcmServerKey)) {
            Log::warning('FCM server key is not configured.');
            return;
        }

        $data = [
            'registration_ids' => $tokens,
            'notification' => [
                'title' => $message['title'] ?? '',
                'body' => $message['body'] ?? '',
                'sound' => 'default',
                'badge' => '1',
            ],
            'data' => $message['data'] ?? [],
            'priority' => 'high',
        ];

        try {
            $response = Http::withHeaders([
                'Authorization' => 'key=' . $fcmServerKey,
                'Content-Type' => 'application/json',
            ])->post('https://fcm.googleapis.com/fcm/send', $data);

            if (!$response->successful()) {
                Log::error('FCM notification failed: ' . $response->body());
            }
        } catch (\Exception $e) {
            Log::error('FCM notification exception: ' . $e->getMessage());
        }
    }
}
```

### Notification Class

```php
namespace Marvel\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Marvel\Database\Models\Delivery;

class DeliveryAssignedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The delivery instance.
     *
     * @var \Marvel\Database\Models\Delivery
     */
    protected $delivery;

    /**
     * Create a new notification instance.
     *
     * @param  \Marvel\Database\Models\Delivery  $delivery
     * @return void
     */
    public function __construct(Delivery $delivery)
    {
        $this->delivery = $delivery;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        $channels = ['mail', 'database'];
        
        // Add FCM channel if it's configured
        if (config('services.fcm.server_key')) {
            $channels[] = \Marvel\Notifications\Channels\FcmChannel::class;
        }
        
        return $channels;
    }

    /**
     * Get the FCM representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toFcm($notifiable)
    {
        $order = $this->delivery->order;
        $message = 'You have been assigned a new delivery for order #' . $order->tracking_number;
        
        return [
            'title' => 'New Delivery Assignment',
            'body' => $message,
            'data' => [
                'delivery_id' => $this->delivery->id,
                'order_id' => $order->id,
                'order_tracking' => $order->tracking_number,
                'type' => 'delivery_assigned',
                'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                'sound' => 'default',
            ],
        ];
    }

    // Other methods (toMail, toArray, etc.)
}
```

### Event Listener

```php
namespace Marvel\Listeners;

use Marvel\Events\DeliveryAssigned;
use Marvel\Notifications\DeliveryAssignedNotification;
use Marvel\Database\Models\User;
use Marvel\Database\Repositories\NotifyLogRepository;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendDeliveryAssignedNotification implements ShouldQueue
{
    /**
     * The notify log repository instance.
     *
     * @var \Marvel\Database\Repositories\NotifyLogRepository
     */
    protected $notifyLogRepository;

    /**
     * Create the event listener.
     *
     * @param  \Marvel\Database\Repositories\NotifyLogRepository  $notifyLogRepository
     * @return void
     */
    public function __construct(NotifyLogRepository $notifyLogRepository)
    {
        $this->notifyLogRepository = $notifyLogRepository;
    }

    /**
     * Handle the event.
     *
     * @param  \Marvel\Events\DeliveryAssigned  $event
     * @return void
     */
    public function handle(DeliveryAssigned $event)
    {
        // Get the delivery agent
        $agent = User::find($event->delivery->delivery_agent_user_id);
        
        if (!$agent) {
            return;
        }
        
        // Send notification to the delivery agent
        $agent->notify(new DeliveryAssignedNotification($event->delivery));
        
        // Log the notification
        $this->notifyLogRepository->storeNotification([
            'user_id' => $agent->id,
            'notify_type' => 'delivery_assigned',
            'notify_id' => $event->delivery->id,
            'sender' => $event->admin->id,
            'receiver' => $agent->id,
            'options' => [
                'delivery_id' => $event->delivery->id,
                'order_id' => $event->delivery->order_id,
                'message' => 'You have been assigned a new delivery for order #' . $event->delivery->order->tracking_number,
            ],
        ]);
    }
}
```

## API Endpoints

### Device Token API

| Method | Endpoint                    | Description                   | Authentication |
|--------|----------------------------|-------------------------------|----------------|
| POST   | /api/device-tokens         | Register a device token       | Required       |
| GET    | /api/device-tokens         | List user's device tokens     | Required       |
| DELETE | /api/device-tokens/{id}    | Delete a device token         | Required       |
| POST   | /api/device-tokens/deactivate | Deactivate a device token  | Required       |

## Event Dispatching

Events are dispatched in the following repository methods:

### DeliveryRepository::assignDelivery

```php
// Dispatch event
event(new DeliveryAssigned($delivery, $assignedBy));
```

### DeliveryRepository::updateDeliveryStatus

```php
// Dispatch event for status change
event(new DeliveryStatusChanged($delivery, $previousStatus, $user));

// If status is CANCELLED, also dispatch the cancellation event
if ($status === 'CANCELLED') {
    event(new DeliveryCancelled($delivery, $user, $data['reason'] ?? null));
}
```

## Configuration

### FCM Configuration

The FCM configuration is stored in `config/services.php`:

```php
'fcm' => [
    'server_key' => env('FCM_SERVER_KEY'),
    'sender_id' => env('FCM_SENDER_ID'),
],
```

### Environment Variables

The following environment variables are used for FCM configuration:

```
FCM_SERVER_KEY=your_server_key_here
FCM_SENDER_ID=your_sender_id_here
```

## Testing

### Unit Testing

Unit tests for the push notification system should cover:

1. **Device Token Repository**:
   - Test creating, updating, and deleting device tokens
   - Test finding device tokens by user ID

2. **FCM Channel**:
   - Test sending notifications via FCM
   - Test handling missing or invalid tokens

3. **Notification Classes**:
   - Test formatting notifications for FCM
   - Test conditional channel selection

### Integration Testing

Integration tests should cover:

1. **API Endpoints**:
   - Test registering a device token
   - Test listing user's device tokens
   - Test deleting a device token
   - Test deactivating a device token

2. **Event Dispatching**:
   - Test that events are dispatched when expected
   - Test that listeners handle events correctly

3. **End-to-End Flow**:
   - Test the complete flow from event dispatching to notification sending

## Performance Considerations

1. **Database Indexing**:
   - The `user_id` column in the `device_tokens` table should be indexed for faster lookups.
   - The `token` column should have a unique index to prevent duplicate tokens.

2. **Batch Processing**:
   - When sending notifications to multiple users, consider using batch processing to reduce API calls.

3. **Queue Processing**:
   - Notification sending should be queued to prevent blocking the main application thread.
   - Listeners should implement the `ShouldQueue` interface.

4. **Token Cleanup**:
   - Implement a scheduled task to clean up expired or invalid tokens.

## Security Considerations

1. **Token Storage**:
   - Device tokens should be stored securely in the database.
   - Access to the device tokens table should be restricted.

2. **API Authentication**:
   - All device token API endpoints should require authentication.
   - Users should only be able to access their own device tokens.

3. **FCM Server Key**:
   - The FCM server key should be stored securely in the environment variables.
   - The server key should not be exposed to the client.

4. **Data Validation**:
   - All input data should be validated before processing.
   - Device tokens should be validated for format and length.

## Troubleshooting

### Common Issues

1. **FCM Server Key Not Configured**:
   - Check if the FCM server key is set in the `.env` file.
   - Verify that the server key is valid and not expired.

2. **Invalid Device Tokens**:
   - Check if the device tokens are valid and not expired.
   - Verify that the tokens are correctly formatted.

3. **FCM API Errors**:
   - Check the Laravel logs for FCM API errors.
   - Verify that the FCM API is accessible from the server.

### Debugging

1. **Enable Debug Logging**:
   - Set the log level to `debug` in `config/logging.php`.
   - Add debug logging statements to the FCM channel.

2. **Test FCM API Directly**:
   - Use a tool like Postman to test the FCM API directly.
   - Verify that the API key and payload are correct.

3. **Check Device Token Registration**:
   - Verify that device tokens are being correctly registered.
   - Check the database for valid device tokens.

## Conclusion

The push notification system provides a robust way to send real-time notifications to users' mobile devices. By following this technical documentation, developers should be able to understand, maintain, and extend the system as needed.
