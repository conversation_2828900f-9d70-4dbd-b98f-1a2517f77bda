# Stripe Amount Handling Guide

## Problem Summary

We've identified an issue in our Stripe payment integration where customers are being charged 100 times the intended amount. When the frontend sends an amount of 100 (intending $100.00), the actual charge processed by <PERSON><PERSON> is 10000 (which is $10,000.00).

## Root Cause

The issue stems from a currency unit conversion mismatch between the frontend and backend:

1. **Stripe's Requirement**: Stripe requires amounts to be in the smallest currency unit (cents for USD, pence for GBP, etc.)
2. **Current Implementation**: In `packages/marvel/src/Payment/Stripe.php`, the `getIntent()` method multiplies the incoming amount by 100:

```php
$intent_array = [
  'amount' => round($amount, 2) * 100,
  'currency' => $this->currency,
  // ...
];
```

3. **The Mismatch**: If the frontend is already sending amounts in cents (the smallest unit), the backend multiplication creates a 100x discrepancy.

## Quick Reference

| Currency | Main Unit | Smallest Unit | Conversion |
|----------|-----------|---------------|------------|
| USD      | Dollar    | Cent          | 1 USD = 100 cents |
| EUR      | Euro      | Cent          | 1 EUR = 100 cents |
| GBP      | Pound     | Pence         | 1 GBP = 100 pence |
| JPY      | Yen       | Yen           | 1 JPY = 1 yen (no conversion) |

## Solution Options

### Option 1: Frontend Sends Main Currency Unit (Dollars)

**Frontend Implementation**:
```javascript
// For a $100.00 charge
const amount = 100.00;
// Send to backend
```

**Backend Implementation** (current):
```php
// Backend correctly converts to cents
$intent_array = [
  'amount' => round($amount, 2) * 100, // 100 * 100 = 10000 cents ($100.00)
  // ...
];
```

### Option 2: Frontend Sends Smallest Currency Unit (Cents)

**Frontend Implementation**:
```javascript
// For a $100.00 charge
const amount = 10000; // Already in cents
// Send to backend
```

**Backend Implementation** (needs change):
```php
// Backend should NOT multiply by 100
$intent_array = [
  'amount' => (int)$amount, // Already in cents
  // ...
];
```

## Implementation Checklist

### If Choosing Option 1 (Frontend Change):

- [ ] Update all frontend payment forms to send amounts in main currency unit (dollars)
- [ ] Verify that decimal amounts (e.g., $10.50) are correctly formatted
- [ ] No backend changes needed

### If Choosing Option 2 (Backend Change):

- [ ] Modify `getIntent()` method in `packages/marvel/src/Payment/Stripe.php` to remove the multiplication by 100
- [ ] Update all frontend code to ensure amounts are sent in cents
- [ ] Add validation to ensure amounts are integers

## Testing Procedure

1. **Test with Small Amount**:
   - Process a payment of $1.00
   - Verify in Stripe dashboard that charge is exactly $1.00

2. **Test with Decimal Amount**:
   - Process a payment of $10.50
   - Verify in Stripe dashboard that charge is exactly $10.50

3. **Test with Large Amount**:
   - Process a payment of $100.00
   - Verify in Stripe dashboard that charge is exactly $100.00

## Code Examples

### Backend Fix (if choosing Option 2):

```php
/**
 * getIntent
 *
 * @param  array $data
 * @return array
 */
public function getIntent($data): array
{
  try {
    extract($data);
    $intent_array = [];
    $intent_array = [
      // Remove multiplication by 100 if frontend is sending cents
      'amount' => (int)$amount, // Ensure it's an integer
      'currency' => $this->currency,
      'description' => 'Marvel Payment',
      // ...
    ];

    // Rest of the method remains the same
    // ...
  }
}
```

### Frontend Example (if choosing Option 1):

```javascript
// React/Vue component example
function handlePayment() {
  const orderTotal = 100.00; // $100.00 in dollars
  
  // Send to backend
  api.createPaymentIntent({
    amount: orderTotal,
    // other payment details
  })
  .then(response => {
    // Handle payment with Stripe.js using the client secret
    const { client_secret } = response.data;
    stripe.confirmCardPayment(client_secret, {
      payment_method: {
        card: cardElement,
        // billing details, etc.
      }
    });
  });
}
```

## Conclusion

The amount handling issue in our Stripe integration can be resolved by ensuring consistent formatting between the frontend and backend. We recommend choosing Option 1 (frontend sends main currency unit) as it aligns with how most developers think about currency and requires no backend changes.

Document the chosen approach clearly in your API documentation to prevent future confusion.

## Additional Resources

- [Stripe API Documentation on Currencies](https://stripe.com/docs/currencies)
- [Stripe Payment Intents API](https://stripe.com/docs/api/payment_intents)
- [Zero-Decimal Currencies](https://stripe.com/docs/currencies#zero-decimal)
