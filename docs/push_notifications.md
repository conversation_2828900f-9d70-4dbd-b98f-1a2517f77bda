# Push Notification System Documentation

This document provides a comprehensive guide to the push notification system implemented in the Marvel package, with a focus on the delivery system feature.

## Overview

The push notification system enables real-time notifications to be sent to users' mobile devices, even when they're not actively using the application. This is particularly useful for the delivery system, where agents need to be notified of new assignments and customers need to be updated on delivery status changes.

The system uses Firebase Cloud Messaging (FCM) to send push notifications to Android and iOS devices.

## Architecture

The push notification system consists of the following components:

1. **FCM Channel**: A custom notification channel that sends notifications via Firebase Cloud Messaging.
2. **Device Token Management**: A system for storing and managing user device tokens.
3. **Notification Classes**: Classes that format notifications for different channels, including FCM.
4. **Events and Listeners**: An event-driven architecture that triggers notifications when specific actions occur.

## Configuration

### Server-Side Configuration

1. **FCM Server Key**: You need to obtain a Firebase Cloud Messaging server key from the [Firebase Console](https://console.firebase.google.com/).

2. **Environment Variables**: Add your FCM server key to the `.env` file:
   ```
   FCM_SERVER_KEY=your_server_key_here
   FCM_SENDER_ID=your_sender_id_here
   ```

3. **Services Configuration**: The FCM configuration is already set up in `config/services.php`:
   ```php
   'fcm' => [
       'server_key' => env('FCM_SERVER_KEY'),
       'sender_id' => env('FCM_SENDER_ID'),
   ],
   ```

### Client-Side Configuration

1. **Firebase Integration**: Integrate Firebase in your mobile app following the [Firebase documentation](https://firebase.google.com/docs/cloud-messaging/android/client).

2. **Token Registration**: When a user logs in, register their device token with the backend using the API endpoints provided.

## API Endpoints

The following API endpoints are available for device token management:

### Register a Device Token

```
POST /api/device-tokens
```

**Request Body:**
```json
{
  "token": "firebase_device_token",
  "device_type": "android|ios|web",
  "device_id": "unique_device_identifier"
}
```

**Response:**
```json
{
  "id": 1,
  "user_id": 123,
  "token": "firebase_device_token",
  "device_type": "android",
  "device_id": "unique_device_identifier",
  "is_active": true,
  "created_at": "2023-07-15T12:00:00.000000Z",
  "updated_at": "2023-07-15T12:00:00.000000Z"
}
```

### List User's Device Tokens

```
GET /api/device-tokens
```

**Response:**
```json
[
  {
    "id": 1,
    "user_id": 123,
    "token": "firebase_device_token",
    "device_type": "android",
    "device_id": "unique_device_identifier",
    "is_active": true,
    "created_at": "2023-07-15T12:00:00.000000Z",
    "updated_at": "2023-07-15T12:00:00.000000Z"
  }
]
```

### Delete a Device Token

```
DELETE /api/device-tokens/{id}
```

**Response:**
```json
{
  "success": true,
  "message": "Token deleted successfully"
}
```

### Deactivate a Device Token

```
POST /api/device-tokens/deactivate
```

**Request Body:**
```json
{
  "token": "firebase_device_token"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Token deactivated successfully"
}
```

## Notification Types

The system supports the following notification types for the delivery system:

### Delivery Assigned

Sent to delivery agents when they are assigned a new delivery.

**FCM Payload:**
```json
{
  "title": "New Delivery Assignment",
  "body": "You have been assigned a new delivery for order #12345",
  "data": {
    "delivery_id": 1,
    "order_id": 123,
    "order_tracking": "12345",
    "type": "delivery_assigned",
    "click_action": "FLUTTER_NOTIFICATION_CLICK",
    "sound": "default"
  }
}
```

### Delivery Status Changed

Sent to customers when the status of their delivery changes.

**FCM Payload:**
```json
{
  "title": "Delivery Update: PICKED_UP",
  "body": "Your order #12345 has been picked up by the delivery agent.",
  "data": {
    "delivery_id": 1,
    "order_id": 123,
    "order_tracking": "12345",
    "type": "delivery_status_changed",
    "previous_status": "ASSIGNED",
    "current_status": "PICKED_UP",
    "click_action": "FLUTTER_NOTIFICATION_CLICK",
    "sound": "default"
  }
}
```

### Delivery Cancelled

Sent to both delivery agents and customers when a delivery is cancelled.

**FCM Payload:**
```json
{
  "title": "Delivery Cancelled",
  "body": "Delivery for order #12345 has been cancelled: Customer requested cancellation.",
  "data": {
    "delivery_id": 1,
    "order_id": 123,
    "order_tracking": "12345",
    "type": "delivery_cancelled",
    "reason": "Customer requested cancellation",
    "click_action": "FLUTTER_NOTIFICATION_CLICK",
    "sound": "default"
  }
}
```

## Implementation Details

### FCM Channel

The `FcmChannel` class handles sending notifications via Firebase Cloud Messaging:

```php
namespace Marvel\Notifications\Channels;

use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Marvel\Database\Models\DeviceToken;

class FcmChannel
{
    public function send($notifiable, Notification $notification)
    {
        // Get the FCM message from the notification
        $message = $notification->toFcm($notifiable);
        
        // Get the user's device tokens
        $tokens = DeviceToken::where('user_id', $notifiable->id)
            ->where('is_active', true)
            ->pluck('token')
            ->toArray();
            
        // Send the notification to FCM
        $this->sendNotification($tokens, $message);
    }
    
    protected function sendNotification(array $tokens, array $message)
    {
        // Send the notification to FCM
    }
}
```

### Notification Classes

Notification classes format notifications for different channels, including FCM:

```php
namespace Marvel\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Marvel\Database\Models\Delivery;

class DeliveryAssignedNotification extends Notification implements ShouldQueue
{
    // ...
    
    public function via($notifiable)
    {
        $channels = ['mail', 'database'];
        
        // Add FCM channel if it's configured
        if (config('services.fcm.server_key')) {
            $channels[] = \Marvel\Notifications\Channels\FcmChannel::class;
        }
        
        return $channels;
    }
    
    public function toFcm($notifiable)
    {
        // Format the notification for FCM
        return [
            'title' => 'New Delivery Assignment',
            'body' => 'You have been assigned a new delivery for order #' . $this->delivery->order->tracking_number,
            'data' => [
                'delivery_id' => $this->delivery->id,
                'order_id' => $this->delivery->order->id,
                'order_tracking' => $this->delivery->order->tracking_number,
                'type' => 'delivery_assigned',
                'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                'sound' => 'default',
            ],
        ];
    }
    
    // ...
}
```

### Events and Listeners

Events are dispatched when specific actions occur, and listeners handle sending notifications:

```php
// In DeliveryRepository::assignDelivery
event(new DeliveryAssigned($delivery, $assignedBy));

// In DeliveryRepository::updateDeliveryStatus
event(new DeliveryStatusChanged($delivery, $previousStatus, $user));

// In SendDeliveryAssignedNotification::handle
$agent->notify(new DeliveryAssignedNotification($event->delivery));
```

## Mobile App Integration

To integrate push notifications in your mobile app:

1. **Set up Firebase**: Follow the [Firebase documentation](https://firebase.google.com/docs/cloud-messaging/android/client) to set up Firebase in your app.

2. **Get Device Token**: Get the device token from Firebase:
   ```dart
   // Flutter example
   FirebaseMessaging.instance.getToken().then((token) {
     // Send token to backend
     registerDeviceToken(token);
   });
   ```

3. **Register Device Token**: Send the device token to the backend:
   ```dart
   // Flutter example
   Future<void> registerDeviceToken(String token) async {
     final response = await http.post(
       Uri.parse('https://your-api.com/api/device-tokens'),
       headers: {
         'Content-Type': 'application/json',
         'Authorization': 'Bearer $authToken',
       },
       body: jsonEncode({
         'token': token,
         'device_type': Platform.isAndroid ? 'android' : 'ios',
         'device_id': await getDeviceId(),
       }),
     );
     
     if (response.statusCode == 200) {
       print('Device token registered successfully');
     } else {
       print('Failed to register device token');
     }
   }
   ```

4. **Handle Notifications**: Set up notification handlers in your app:
   ```dart
   // Flutter example
   FirebaseMessaging.onMessage.listen((RemoteMessage message) {
     // Handle foreground notification
     showNotification(message);
   });
   
   FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
     // Handle notification tap when app is in background
     navigateToScreen(message);
   });
   ```

## Troubleshooting

### Common Issues

1. **Notifications not being sent**:
   - Check if the FCM server key is correctly configured in the `.env` file.
   - Verify that the device token is correctly registered with the backend.
   - Check the Laravel logs for any FCM-related errors.

2. **Notifications not being received**:
   - Ensure that the device has an internet connection.
   - Check if the app has notification permissions.
   - Verify that the device token is valid and not expired.

3. **Incorrect notification data**:
   - Check the `toFcm()` method in the notification class to ensure it's returning the correct data.
   - Verify that the event is being dispatched with the correct parameters.

### Debugging

1. **Server-Side Debugging**:
   - Enable debug logging for FCM requests:
     ```php
     Log::debug('FCM request', [
         'tokens' => $tokens,
         'message' => $message,
     ]);
     ```
   - Check the Laravel logs for any errors.

2. **Client-Side Debugging**:
   - Enable Firebase debug logging:
     ```dart
     // Flutter example
     FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
       alert: true,
       badge: true,
       sound: true,
     );
     ```
   - Print the device token to verify it's being generated correctly.

## Conclusion

The push notification system provides a robust way to send real-time notifications to users' mobile devices. By following this documentation, you should be able to configure and use the system effectively for the delivery feature.

For any further questions or issues, please refer to the Laravel and Firebase documentation or contact the development team.
