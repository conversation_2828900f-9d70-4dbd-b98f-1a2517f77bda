#!/bin/bash

# Update package lists
sudo apt-get update

# Install PHP 8.1 and required extensions
sudo apt-get install -y software-properties-common
sudo add-apt-repository ppa:ondrej/php -y
sudo apt-get update
sudo apt-get install -y php8.1 php8.1-cli php8.1-fpm php8.1-mysql php8.1-xml php8.1-curl php8.1-gd php8.1-mbstring php8.1-zip php8.1-bcmath php8.1-intl php8.1-sqlite3 php8.1-dom php8.1-fileinfo

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
sudo chmod +x /usr/local/bin/composer

# Add composer to PATH
echo 'export PATH="$HOME/.composer/vendor/bin:$PATH"' >> $HOME/.profile

# Install Node.js and npm
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Navigate to workspace and install dependencies
cd /mnt/persist/workspace

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    cp .env.example .env
fi

# Install PHP dependencies
composer install --no-interaction --prefer-dist --optimize-autoloader

# Generate application key
php artisan key:generate

# Install Node.js dependencies
npm install

# Build assets
npm run dev

# Set proper permissions
chmod -R 755 storage bootstrap/cache
chmod -R 777 storage/logs storage/framework

# Install Marvel package dependencies
cd packages/marvel
composer install --no-interaction --prefer-dist --optimize-autoloader
cd /mnt/persist/workspace

# Clear and cache config
php artisan config:clear
php artisan config:cache
php artisan route:clear
php artisan view:clear

# Run package discovery
php artisan package:discover

# Source the profile to update PATH
source $HOME/.profile