# Negotiated Pricing for Vendors - Implementation Plan

This document outlines the revised approach for allowing vendors (STORE_OWNERs) to manually create orders and apply a negotiated total price, ensuring data consistency across the system.

## Problem Statement

Currently, only Super Admins can manually create orders, and the system automatically calculates the total price based on product prices, taxes, and shipping. There is no mechanism for vendors to set a custom, negotiated total price for an order, which is crucial for scenarios like bulk purchases or special agreements with customers. A simple override of the order total would lead to inconsistencies if the order's price is re-evaluated based on individual product prices.

## Goals

1.  **Grant Vendors Access to the `/users` Endpoint:** Allow `STORE_OWNER` users to access the `/users` API endpoint.
2.  **Allow Vendors to Manually Create Orders:** Enable `STORE_OWNER` users to create orders on behalf of specific customers by providing a `customer_id`.
3.  **Allow Vendors to Modify the Total Price of an Order (Revised Approach):** Implement a robust mechanism for `STORE_OWNER`s to set a custom total price for an order, ensuring consistency by proportionally adjusting individual product prices within the order.

## Revised Implementation Plan for Goal 3: Allowing Vendors to Modify the Total Price

To address the concern of data inconsistency, the approach for modifying the total price will involve proportionally adjusting the `unit_price` and `subtotal` for each product within the `order_product` pivot table.

### 1. New Request Field: `manual_total_override`

*   **Location:** `packages/marvel/src/Http/Requests/OrderCreateRequest.php`
*   **Definition:** Add a new field `manual_total_override` to the validation rules.
    *   It will be `nullable|numeric`.
    *   **Authorization:** This field will only be processed if the authenticated user is a `SUPER_ADMIN` or `STORE_OWNER`. This can be enforced either in the `authorize` method of the `FormRequest` or within the `OrderRepository` logic.

### 2. Detailed Override Logic in `OrderRepository@storeOrder` and `processProducts`

The core logic for handling the `manual_total_override` will reside in `packages/marvel/src/Database/Repositories/OrderRepository.php`, specifically within the `storeOrder` method and its interaction with the `processProducts` method.

**Steps:**

1.  **Initial Calculation:** When an order creation request is received, the system will first perform its standard calculations:
    *   Calculate the `initial_calculated_amount` (sum of `subtotal` for all products based on their original `price` or `sale_price`).
    *   Calculate `sales_tax` and `delivery_fee`.
    *   Apply any `coupon_discount`.
    *   This will result in an `initial_calculated_total`.

2.  **Conditional Override:** Check if `manual_total_override` is present in the request and if the requesting user has `SUPER_ADMIN` or `STORE_OWNER` permissions.

3.  **If `manual_total_override` is applied:**
    *   **Calculate `negotiation_discount`:**
        ```php
        $negotiation_discount = $initial_calculated_total - $request->manual_total_override;
        ```
    *   **Adjust Order-Level Discount:** Add this `negotiation_discount` to any existing `discount` from coupons.
        ```php
        $request['discount'] = ($request['discount'] ?? 0) + $negotiation_discount;
        ```
    *   **Set Final Order Totals:** Set the `paid_total` and `total` fields of the order to the `manual_total_override` value.
        ```php
        $request['paid_total'] = $request->manual_total_override;
        $request['total'] = $request->manual_total_override;
        ```
    *   **Proportional Product Price Adjustment (within `processProducts` or before calling it):**
        *   Before the products are attached to the order in the `order_product` pivot table, iterate through each product in the `$request['products']` array.
        *   For each product, calculate its `proportional_discount_per_product`:
            ```php
            $product_original_subtotal = $product['unit_price'] * $product['order_quantity']; // Or retrieve from product model
            $proportional_discount_per_product = ($product_original_subtotal / $initial_calculated_amount) * $negotiation_discount;
            ```
        *   Adjust the `subtotal` of that individual product:
            ```php
            $product['subtotal'] = $product_original_subtotal - $proportional_discount_per_product;
            ```
        *   Adjust the `unit_price` of that product:
            ```php
            $product['unit_price'] = $product['subtotal'] / $product['order_quantity'];
            ```
        *   These adjusted `unit_price` and `subtotal` values will then be used when storing entries in the `order_product` pivot table.

### Benefits of this Approach:

*   **Data Consistency:** The `unit_price` and `subtotal` stored in the `order_product` table will accurately reflect the negotiated price, ensuring that any future re-evaluation of the order based on its line items remains consistent with the final paid total.
*   **Transparency:** The `discount` field on the `orders` table will clearly show the total amount reduced due to the negotiation, providing a clear audit trail.
*   **Flexibility:** Allows vendors to set a custom final price while maintaining the integrity of individual product pricing within the order.

This revised plan ensures that the system remains in a consistent state while providing the necessary flexibility for vendors to manage negotiated orders.
