# Stripe Payment Integration Documentation

## Overview

This document provides a comprehensive guide to the Stripe payment integration in the Marvel package, with a specific focus on how amounts are handled between the frontend and backend. It addresses a critical issue where amounts sent from the frontend may be incorrectly processed, resulting in charges that are 100 times larger than intended.

## Table of Contents

1. [Payment Flow](#payment-flow)
2. [Amount Handling](#amount-handling)
3. [Current Implementation](#current-implementation)
4. [The Issue](#the-issue)
5. [Solutions](#solutions)
6. [Best Practices](#best-practices)
7. [Implementation Guide](#implementation-guide)
8. [Testing](#testing)

## Payment Flow

The Stripe payment flow in the Marvel package works as follows:

1. **Order Creation**:
   - User creates an order with products
   - System calculates the total amount

2. **Payment Intent Creation**:
   - Backend creates a payment intent via the `getIntent()` method
   - This generates a Stripe payment intent with the specified amount and currency
   - The method returns a client secret that the frontend uses to complete the payment

3. **Frontend Payment Processing**:
   - The frontend uses Stripe.js or Stripe Elements to collect payment details
   - The payment is confirmed using the client secret from the payment intent

4. **Payment Verification**:
   - Stripe sends webhook events to the configured webhook URL
   - The system processes these events and updates the order status accordingly

## Amount Handling

### Stripe's Amount Format

Stripe requires amounts to be specified in the **smallest currency unit** rather than the main currency unit:

- For USD: cents (1 USD = 100 cents)
- For EUR: cents (1 EUR = 100 cents)
- For GBP: pence (1 GBP = 100 pence)
- For JPY: yen (1 JPY = 1 yen, no conversion needed)

This means that to charge $10.00, you must specify the amount as 1000 (cents) in the Stripe API.

### Current Implementation

In the current implementation, the amount conversion happens in `packages/marvel/src/Payment/Stripe.php` in the `getIntent()` method:

```php
$intent_array = [
  'amount' => round($amount, 2) * 100,
  'currency' => $this->currency,
  'description' => 'Marvel Payment',
  // ...
];
```

The backend is multiplying the amount by 100 to convert from dollars to cents before sending it to Stripe.

## The Issue

**Problem**: If the frontend sends an amount of 100 (intending $100.00), the backend multiplies it by 100, resulting in 10000 cents ($100.00), which is correct. However, if the frontend is already sending the amount in cents (10000 for $100.00), the backend will still multiply by 100, resulting in 1000000 cents ($10,000.00).

This discrepancy can lead to customers being charged 100 times more than intended.

## Solutions

There are two potential solutions to this issue:

### Solution 1: Frontend Sends Main Currency Unit

The frontend should send amounts in the main currency unit (dollars, pounds, etc.):

```javascript
// Frontend code
const amount = 100.00; // $100.00
// Send to backend
```

The backend then correctly converts to cents:

```php
// Backend code
$intent_array = [
  'amount' => round($amount, 2) * 100, // 100 * 100 = 10000 cents ($100.00)
  // ...
];
```

### Solution 2: Frontend Sends Smallest Currency Unit

The frontend sends amounts already in the smallest currency unit (cents, pence, etc.):

```javascript
// Frontend code
const amount = 10000; // 10000 cents = $100.00
// Send to backend
```

The backend should NOT multiply by 100:

```php
// Backend code
$intent_array = [
  'amount' => $amount, // Already in cents, no multiplication needed
  // ...
];
```

## Best Practices

1. **Consistent Currency Format**: Decide on one approach (main currency unit or smallest currency unit) and use it consistently across the entire application.

2. **Clear Documentation**: Document the expected format for amounts in all API endpoints.

3. **Validation**: Implement validation to ensure amounts are in the expected format.

4. **Configuration Option**: Consider adding a configuration option to specify whether amounts are already in the smallest currency unit.

5. **Logging**: Add logging for payment amounts before and after conversion to help debug issues.

## Implementation Guide

### Option 1: Update Frontend (Recommended)

If you choose to have the frontend send amounts in the main currency unit:

1. Ensure all frontend code sends amounts as decimal values (e.g., 100.00 for $100.00)
2. Keep the backend multiplication by 100

### Option 2: Update Backend

If you choose to have the frontend send amounts in the smallest currency unit:

1. Modify the `getIntent()` method in `packages/marvel/src/Payment/Stripe.php`:

```php
// Before
$intent_array = [
  'amount' => round($amount, 2) * 100,
  // ...
];

// After
$intent_array = [
  'amount' => (int)$amount, // Assuming amount is already in cents
  // ...
];
```

2. Update all frontend code to ensure amounts are sent in cents (e.g., 10000 for $100.00)

## Testing

To verify the fix:

1. **Test Small Amounts**: Process a payment with a small amount (e.g., $1.00) to verify the correct amount is charged.

2. **Test Decimal Amounts**: Process a payment with a decimal amount (e.g., $10.50) to verify the correct amount is charged.

3. **Check Stripe Dashboard**: After processing test payments, check the Stripe dashboard to confirm the correct amounts are being charged.

4. **Webhook Testing**: Test the webhook handling to ensure payment statuses are correctly updated.

5. **Refund Testing**: Test the refund process to ensure refunds are processed for the correct amount.

## Conclusion

The issue with Stripe payment amounts being 100 times larger than intended is due to a mismatch in how amounts are formatted between the frontend and backend. By ensuring consistent formatting and proper conversion, this issue can be resolved.

Choose either to have the frontend send amounts in the main currency unit and let the backend convert to cents, or have the frontend send amounts already in cents and remove the backend conversion. Document the chosen approach clearly for all developers working on the project.
