# Product Filtering and Categories Documentation

This document provides information about the product filtering and category-related endpoints available in the API.

## Base URL

All endpoints are relative to the base API URL.

## Product Filtering and Sorting

### Filter Products

```
GET /filter-products
```

This endpoint allows you to filter and sort products with advanced options.

#### Query Parameters

- `page` (optional): Page number for pagination. Default is 1.
- `limit` (optional): Number of products to return per page. Default is 15.
- `language` (optional): Language code for the products. Default is the system's default language.
- `category` (optional): Filter products by category ID(s). Can be a single ID or an array of IDs.
- `brand` (optional): Filter products by brand/manufacturer ID(s). Can be a single ID or an array of IDs.
- `size` (optional): Filter products by size. Can be a single size or an array of sizes.
- `min_price` (optional): Filter products with price greater than or equal to this value.
- `max_price` (optional): Filter products with price less than or equal to this value.
- `search` (optional): Search term to filter products by name, description, or SKU.
- `shop_id` (optional): Filter products by shop ID.
- `product_type` (optional): Filter products by product type.
- `status` (optional): Filter products by status.
- `sort_by` (optional): Field to sort by. Options include:
  - `popularity`: Sort by number of orders (most ordered first)
  - `price_low_high`: Sort by price (lowest first)
  - `price_high_low`: Sort by price (highest first)
  - `newest`: Sort by creation date (newest first)
  - `rating`: Sort by average rating (average review score)
  - `relevance`: Default sorting for search results
  - Any valid product field name
- `sort_order` (optional): Sort order. Options are `asc` (ascending) or `desc` (descending). Default is `desc`.

#### Response

Returns a paginated list of products matching the filter criteria and sorted according to the specified parameters.

## Category Endpoints

### Get Parent Categories

```
GET /parent-categories
```

This endpoint returns a paginated list of all top-level (parent) categories.

#### Query Parameters

- `page` (optional): Page number for pagination. Default is 1.
- `limit` (optional): Number of categories to return per page. Default is 15.
- `language` (optional): Language code for the categories. Default is the system's default language.

#### Response

Returns a paginated list of parent categories with their details, including their child categories and product count.

### Get Child Categories

```
GET /child-categories/{parentId}
```

This endpoint returns a paginated list of child categories for a specific parent category.

#### Path Parameters

- `parentId`: ID of the parent category.

#### Query Parameters

- `page` (optional): Page number for pagination. Default is 1.
- `limit` (optional): Number of categories to return per page. Default is 15.
- `language` (optional): Language code for the categories. Default is the system's default language.

#### Response

Returns a paginated list of child categories for the specified parent category, including their details and product count.

## Authentication

All endpoints are publicly accessible.

## Error Responses

All endpoints may return the following error responses:

- `404 Not Found`: The requested resource was not found.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Example Usage

### Filter Products

```javascript
// Example using fetch API
fetch('/api/filter-products?category=5&min_price=10&max_price=100&sort_by=price_low_high&page=1&limit=20')
  .then(response => response.json())
  .then(data => {
    console.log(data);
  })
  .catch(error => {
    console.error('Error:', error);
  });
```

### Get Parent Categories

```javascript
// Example using fetch API
fetch('/api/parent-categories?page=1&limit=10')
  .then(response => response.json())
  .then(data => {
    console.log(data);
  })
  .catch(error => {
    console.error('Error:', error);
  });
```

### Get Child Categories

```javascript
// Example using fetch API
fetch('/api/child-categories/5?page=1&limit=10')
  .then(response => response.json())
  .then(data => {
    console.log(data);
  })
  .catch(error => {
    console.error('Error:', error);
  });
```
