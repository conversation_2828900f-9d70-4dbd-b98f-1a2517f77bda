<?php

if (!defined('APP_NOTICE_DOMAIN')) { define('APP_NOTICE_DOMAIN', config('shop.app_notice_domain')); }
if (!defined('DEFAULT_LANGUAGE')) { define('DEFAULT_LANGUAGE', config('shop.default_language')); }
if (!defined('TRANSLATION_ENABLED')) { define('TRANSLATION_ENABLED', config('shop.translation_enabled')); }
if (!defined('DEFAULT_CURRENCY')) { define('DEFAULT_CURRENCY', config('shop.default_currency')); }
if (!defined('DEFAULT_CURRENCY_FORMATION')) { define('DEFAULT_CURRENCY_FORMATION', 'en-US'); }
if (!defined('ACTIVE_PAYMENT_GATEWAY')) { define('ACTIVE_PAYMENT_GATEWAY', config('shop.active_payment_gateway')); }
if (!defined('NOT_FOUND')) { define('NOT_FOUND', APP_NOTICE_DOMAIN . 'ERROR.NOT_FOUND'); }
if (!defined('COUPON_NOT_FOUND')) { define('COUPON_NOT_FOUND', APP_NOTICE_DOMAIN . 'ERROR.COUPON_NOT_FOUND'); }
if (!defined('INVALID_COUPON_CODE')) { define('INVALID_COUPON_CODE', APP_NOTICE_DOMAIN . 'ERROR.INVALID_COUPON_CODE'); }
if (!defined('COUPON_CODE_IS_NOT_APPLICABLE')) { define('COUPON_CODE_IS_NOT_APPLICABLE', APP_NOTICE_DOMAIN . 'ERROR.COUPON_CODE_IS_NOT_APPLICABLE'); }
if (!defined('ALREADY_FREE_SHIPPING_ACTIVATED')) { define('ALREADY_FREE_SHIPPING_ACTIVATED', APP_NOTICE_DOMAIN . 'ERROR.ALREADY_FREE_SHIPPING_ACTIVATED'); }
if (!defined('CART_ITEMS_NOT_FOUND')) { define('CART_ITEMS_NOT_FOUND', APP_NOTICE_DOMAIN . 'ERROR.CART_ITEMS_NOT_FOUND'); }
if (!defined('NOT_A_RENTAL_PRODUCT')) { define('NOT_A_RENTAL_PRODUCT', APP_NOTICE_DOMAIN . 'ERROR.NOT_A_RENTAL_PRODUCT'); }
if (!defined('NOT_AUTHORIZED')) { define('NOT_AUTHORIZED', APP_NOTICE_DOMAIN . 'ERROR.NOT_AUTHORIZED'); }
if (!defined('SOMETHING_WENT_WRONG')) { define('SOMETHING_WENT_WRONG', APP_NOTICE_DOMAIN . 'ERROR.SOMETHING_WENT_WRONG'); }
if (!defined('PAYMENT_FAILED')) { define('PAYMENT_FAILED', APP_NOTICE_DOMAIN . 'ERROR.PAYMENT_FAILED'); }
if (!defined('SHOP_NOT_APPROVED')) { define('SHOP_NOT_APPROVED', APP_NOTICE_DOMAIN . 'ERROR.SHOP_NOT_APPROVED'); }
if (!defined('INSUFFICIENT_BALANCE')) { define('INSUFFICIENT_BALANCE', APP_NOTICE_DOMAIN . 'ERROR.INSUFFICIENT_BALANCE'); }
if (!defined('INVALID_CREDENTIALS')) { define('INVALID_CREDENTIALS', APP_NOTICE_DOMAIN . 'ERROR.INVALID_CREDENTIALS'); }
if (!defined('EMAIL_SENT_SUCCESSFUL')) { define('EMAIL_SENT_SUCCESSFUL', APP_NOTICE_DOMAIN . 'MESSAGE.EMAIL_SENT_SUCCESSFUL'); }
if (!defined('PASSWORD_RESET_SUCCESSFUL')) { define('PASSWORD_RESET_SUCCESSFUL', APP_NOTICE_DOMAIN . 'MESSAGE.PASSWORD_RESET_SUCCESSFUL'); }
if (!defined('INVALID_TOKEN')) { define('INVALID_TOKEN', APP_NOTICE_DOMAIN . 'MESSAGE.INVALID_TOKEN'); }
if (!defined('TOKEN_IS_VALID')) { define('TOKEN_IS_VALID', APP_NOTICE_DOMAIN . 'MESSAGE.TOKEN_IS_VALID'); }
if (!defined('CHECK_INBOX_FOR_PASSWORD_RESET_EMAIL')) { define('CHECK_INBOX_FOR_PASSWORD_RESET_EMAIL', APP_NOTICE_DOMAIN . 'MESSAGE.CHECK_INBOX_FOR_PASSWORD_RESET_EMAIL'); }
if (!defined('ACTION_NOT_VALID')) { define('ACTION_NOT_VALID', APP_NOTICE_DOMAIN . 'ERROR.ACTION_NOT_VALID'); }
if (!defined('PLEASE_LOGIN_USING_FACEBOOK_OR_GOOGLE')) { define('PLEASE_LOGIN_USING_FACEBOOK_OR_GOOGLE', APP_NOTICE_DOMAIN . 'ERROR.PLEASE_LOGIN_USING_FACEBOOK_OR_GOOGLE'); }
if (!defined('WITHDRAW_MUST_BE_ATTACHED_TO_SHOP')) { define('WITHDRAW_MUST_BE_ATTACHED_TO_SHOP', APP_NOTICE_DOMAIN . 'ERROR.WITHDRAW_MUST_BE_ATTACHED_TO_SHOP'); }
if (!defined('OLD_PASSWORD_INCORRECT')) { define('OLD_PASSWORD_INCORRECT', APP_NOTICE_DOMAIN . 'MESSAGE.OLD_PASSWORD_INCORRECT'); }
if (!defined('OTP_SEND_FAIL')) { define('OTP_SEND_FAIL', APP_NOTICE_DOMAIN . 'ERROR.OTP_SEND_FAIL'); }
if (!defined('OTP_SEND_SUCCESSFUL')) { define('OTP_SEND_SUCCESSFUL', APP_NOTICE_DOMAIN . 'MESSAGE.OTP_SEND_SUCCESSFUL'); }
if (!defined('REQUIRED_INFO_MISSING')) { define('REQUIRED_INFO_MISSING', APP_NOTICE_DOMAIN . 'MESSAGE.REQUIRED_INFO_MISSING'); }
if (!defined('CONTACT_UPDATE_SUCCESSFUL')) { define('CONTACT_UPDATE_SUCCESSFUL', APP_NOTICE_DOMAIN . 'MESSAGE.CONTACT_UPDATE_SUCCESSFUL'); }
if (!defined('INVALID_GATEWAY')) { define('INVALID_GATEWAY', APP_NOTICE_DOMAIN . 'ERROR.INVALID_GATEWAY'); }
if (!defined('OTP_VERIFICATION_FAILED')) { define('OTP_VERIFICATION_FAILED', APP_NOTICE_DOMAIN . 'ERROR.OTP_VERIFICATION_FAILED'); }
if (!defined('CONTACT_UPDATE_FAILED')) { define('CONTACT_UPDATE_FAILED', APP_NOTICE_DOMAIN . 'ERROR.CONTACT_UPDATE_FAILED'); }
if (!defined('ALREADY_REFUNDED')) { define('ALREADY_REFUNDED', APP_NOTICE_DOMAIN . 'ERROR.ALREADY_REFUNDED'); }
if (!defined('ORDER_ALREADY_HAS_REFUND_REQUEST')) { define('ORDER_ALREADY_HAS_REFUND_REQUEST', APP_NOTICE_DOMAIN . 'ERROR.ORDER_ALREADY_HAS_REFUND_REQUEST'); }
if (!defined('REFUND_ONLY_ALLOWED_FOR_MAIN_ORDER')) { define('REFUND_ONLY_ALLOWED_FOR_MAIN_ORDER', APP_NOTICE_DOMAIN . 'ERROR.REFUND_ONLY_ALLOWED_FOR_MAIN_ORDER'); }
if (!defined('WRONG_REFUND')) { define('WRONG_REFUND', APP_NOTICE_DOMAIN . 'ERROR.WRONG_REFUND'); }
if (!defined('CSV_NOT_FOUND')) { define('CSV_NOT_FOUND', APP_NOTICE_DOMAIN . 'ERROR.CSV_NOT_FOUND'); }
if (!defined('ALREADY_GIVEN_REVIEW_FOR_THIS_PRODUCT')) { define('ALREADY_GIVEN_REVIEW_FOR_THIS_PRODUCT', APP_NOTICE_DOMAIN . 'ERROR.ALREADY_GIVEN_REVIEW_FOR_THIS_PRODUCT'); }
if (!defined('USER_NOT_FOUND')) { define('USER_NOT_FOUND', APP_NOTICE_DOMAIN . 'ERROR.USER_NOT_FOUND'); }
if (!defined('TOKEN_NOT_FOUND')) { define('TOKEN_NOT_FOUND', APP_NOTICE_DOMAIN . 'ERROR.TOKEN_NOT_FOUND'); }
if (!defined('NOT_AVAILABLE_FOR_BOOKING')) { define('NOT_AVAILABLE_FOR_BOOKING', APP_NOTICE_DOMAIN . 'ERROR.NOT_AVAILABLE_FOR_BOOKING'); }
if (!defined('YOU_HAVE_ALREADY_GIVEN_ABUSIVE_REPORT_FOR_THIS')) { define('YOU_HAVE_ALREADY_GIVEN_ABUSIVE_REPORT_FOR_THIS', APP_NOTICE_DOMAIN . 'ERROR.YOU_HAVE_ALREADY_GIVEN_ABUSIVE_REPORT_FOR_THIS'); }
if (!defined('MAXIMUM_QUESTION_LIMIT_EXCEEDED')) { define('MAXIMUM_QUESTION_LIMIT_EXCEEDED', APP_NOTICE_DOMAIN . 'ERROR.MAXIMUM_QUESTION_LIMIT_EXCEEDED'); }
if (!defined('INVALID_AMOUNT')) { define('INVALID_AMOUNT', APP_NOTICE_DOMAIN . 'ERROR.INVALID_AMOUNT'); }
if (!defined('INVALID_CARD')) { define('INVALID_CARD', APP_NOTICE_DOMAIN . 'ERROR.INVALID_CARD'); }
if (!defined('TOO_MANY_REQUEST')) { define('TOO_MANY_REQUEST', APP_NOTICE_DOMAIN . 'ERROR.TOO_MANY_REQUEST'); }
if (!defined('INVALID_REQUEST')) { define('INVALID_REQUEST', APP_NOTICE_DOMAIN . 'ERROR.INVALID_REQUEST'); }
if (!defined('AUTHENTICATION_FAILED')) { define('AUTHENTICATION_FAILED', APP_NOTICE_DOMAIN . 'ERROR.AUTHENTICATION_FAILED'); }
if (!defined('API_CONNECTION_FAILED')) { define('API_CONNECTION_FAILED', APP_NOTICE_DOMAIN . 'ERROR.API_CONNECTION_FAILED'); }
if (!defined('SOMETHING_WENT_WRONG_WITH_PAYMENT')) { define('SOMETHING_WENT_WRONG_WITH_PAYMENT', APP_NOTICE_DOMAIN . 'ERROR.SOMETHING_WENT_WRONG_WITH_PAYMENT'); }
if (!defined('INVALID_PAYMENT_ID')) { define('INVALID_PAYMENT_ID', APP_NOTICE_DOMAIN . 'ERROR.INVALID_PAYMENT_ID'); }
if (!defined('INVALID_PAYMENT_INTENT_ID')) { define('INVALID_PAYMENT_INTENT_ID', APP_NOTICE_DOMAIN . 'ERROR.INVALID_PAYMENT_INTENT_ID'); }
if (!defined('EMAIL_NOT_VERIFIED')) { define('EMAIL_NOT_VERIFIED', 'EMAIL_NOT_VERIFIED'); }
if (!defined('INVALID_LICENSE_KEY')) { define('INVALID_LICENSE_KEY', 'INVALID_LICENSE_KEY'); }
if (!defined('EMAIL_UPDATED_SUCCESSFULLY')) { define('EMAIL_UPDATED_SUCCESSFULLY', APP_NOTICE_DOMAIN . 'SUCCESS.EMAIL_UPDATED_SUCCESSFULLY'); }
if (!defined('YOU_CAN_NOT_SEND_MESSAGE_TO_YOUR_OWN_SHOP')) { define('YOU_CAN_NOT_SEND_MESSAGE_TO_YOUR_OWN_SHOP', APP_NOTICE_DOMAIN . 'ERROR.YOU_CAN_NOT_SEND_MESSAGE_TO_YOUR_OWN_SHOP'); }
if (!defined('COULD_NOT_CREATE_THE_RESOURCE')) { define('COULD_NOT_CREATE_THE_RESOURCE', APP_NOTICE_DOMAIN . 'ERROR.COULD_NOT_CREATE_THE_RESOURCE'); }
if (!defined('COULD_NOT_UPDATE_THE_RESOURCE')) { define('COULD_NOT_UPDATE_THE_RESOURCE', APP_NOTICE_DOMAIN . 'ERROR.COULD_NOT_UPDATE_THE_RESOURCE'); }
if (!defined('COULD_NOT_DELETE_THE_RESOURCE')) { define('COULD_NOT_DELETE_THE_RESOURCE', APP_NOTICE_DOMAIN . 'ERROR.COULD_NOT_DELETE_THE_RESOURCE'); }
if (!defined('PLEASE_ENABLE_OPENAI_FROM_THE_SETTINGS')) { define('PLEASE_ENABLE_OPENAI_FROM_THE_SETTINGS', APP_NOTICE_DOMAIN . 'ERROR.PLEASE_ENABLE_OPENAI_FROM_THE_SETTINGS'); }
if (!defined('DUMMY_DATA_PATH')) { define('DUMMY_DATA_PATH', config('shop.dummy_data_path')); }
if (!defined('THIS_COUPON_CODE_IS_ONLY_FOR_VERIFIED_USERS')) { define('THIS_COUPON_CODE_IS_ONLY_FOR_VERIFIED_USERS', APP_NOTICE_DOMAIN . 'ERROR.THIS_COUPON_CODE_IS_ONLY_FOR_VERIFIED_USERS'); }
if (!defined('THIS_COUPON_CODE_IS_NOT_APPROVED')) { define('THIS_COUPON_CODE_IS_NOT_APPROVED', APP_NOTICE_DOMAIN . 'ERROR.THIS_COUPON_CODE_IS_NOT_APPROVED'); }
if (!defined('COUPON_CODE_IS_NOT_APPLICABLE_IN_THIS_SHOP_PRODUCT')) { define('COUPON_CODE_IS_NOT_APPLICABLE_IN_THIS_SHOP_PRODUCT', APP_NOTICE_DOMAIN . 'ERROR.COUPON_CODE_IS_NOT_APPLICABLE_IN_THIS_SHOP_PRODUCT'); }
if (!defined('PLEASE_ENABLE_PAYMENT_OPTION_FROM_THE_SETTINGS')) { define('PLEASE_ENABLE_PAYMENT_OPTION_FROM_THE_SETTINGS', APP_NOTICE_DOMAIN . 'ERROR.PLEASE_ENABLE_PAYMENT_OPTION_FROM_THE_SETTINGS'); }
if (!defined('COULD_NOT_SETTLE_THE_TRANSITION')) { define('COULD_NOT_SETTLE_THE_TRANSITION', APP_NOTICE_DOMAIN . 'ERROR.COULD_NOT_SETTLE_THE_TRANSITION'); }