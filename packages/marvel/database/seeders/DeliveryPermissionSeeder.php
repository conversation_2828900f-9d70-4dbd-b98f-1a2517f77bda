<?php

namespace Marvel\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Marvel\Enums\Permission;
use Marvel\Enums\Role;
use Spatie\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role as RoleModel;

class DeliveryPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Add the delivery agent role
        $deliveryAgentRole = RoleModel::firstOrCreate(['name' => Role::DELIVERY_AGENT, 'guard_name' => 'api']);

        // Add delivery agent permission
        $permissions = [
            Permission::DELIVERY_AGENT,
            Permission::CUSTOMER, // Delivery agents should also have customer permissions
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }

        // Assign permissions to the delivery agent role
        foreach ($permissions as $permission) {
            $permissionModel = PermissionModel::where('name', $permission)->first();
            if ($permissionModel) {
                $deliveryAgentRole->givePermissionTo($permissionModel);
            }
        }
    }
}
