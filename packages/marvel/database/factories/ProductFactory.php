<?php

namespace Marvel\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Marvel\Database\Models\Author;
use Marvel\Database\Models\Manufacturer;
use Marvel\Database\Models\Product;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\Shipping;
use Marvel\Database\Models\Type;
use Marvel\Enums\ProductStatus;
use Marvel\Enums\ProductType;
use Marvel\Enums\ProductVisibilityStatus;

class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->word,
            'slug' => $this->faker->slug,
            'description' => $this->faker->paragraph,
            'type_id' => Type::factory(),
            'shop_id' => Shop::factory(),
            'author_id' => Author::factory(),
            'manufacturer_id' => Manufacturer::factory(),
            'shipping_class_id' => Shipping::factory(),
            'price' => $this->faker->randomFloat(2, 10, 1000),
            'sale_price' => $this->faker->randomFloat(2, 5, 900),
            'min_price' => $this->faker->randomFloat(2, 5, 900),
            'sku' => $this->faker->unique()->ean13,
            'quantity' => $this->faker->numberBetween(1, 100),
            'in_stock' => $this->faker->boolean,
            'is_taxable' => $this->faker->boolean,
            'product_type' => ProductType::SIMPLE,
            'status' => ProductStatus::PUBLISH,
            'unit' => $this->faker->word,
            'height' => $this->faker->randomFloat(2, 1, 10),
            'width' => $this->faker->randomFloat(2, 1, 10),
            'length' => $this->faker->randomFloat(2, 1, 10),
            'image' => json_encode(['original' => $this->faker->imageUrl(), 'thumbnail' => $this->faker->imageUrl()]),
            'gallery' => json_encode([
                ['original' => $this->faker->imageUrl(), 'thumbnail' => $this->faker->imageUrl()],
                ['original' => $this->faker->imageUrl(), 'thumbnail' => $this->faker->imageUrl()],
            ]),
            'language' => 'en',
            'sold_quantity' => $this->faker->numberBetween(0, 50),
            'in_flash_sale' => $this->faker->boolean,
            'visibility' => $this->faker->randomElement(ProductVisibilityStatus::getValues()),
        ];
    }
}