<?php

namespace Marvel\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\User;

class OrderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Order::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $customer = User::factory()->create();
        $shop = Shop::factory()->create();
        
        return [
            'tracking_number' => $this->faker->unique()->numerify('ORDER-#####'),
            'customer_id' => $customer->id,
            'shop_id' => $shop->id,
            'order_status' => $this->faker->randomElement(['order-pending', 'order-processing', 'order-completed']),
            'amount' => $this->faker->randomFloat(2, 10, 500),
            'payment_status' => $this->faker->randomElement(['payment-pending', 'payment-processing', 'payment-success']),
            'payment_method' => $this->faker->randomElement(['CASH_ON_DELIVERY', 'CASH', 'STRIPE']),
            'shipping_address' => [
                'address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->country,
            ],
            'billing_address' => [
                'address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->country,
            ],
            'requires_delivery' => true,
            'delivery_id' => null,
        ];
    }
}
