<?php

namespace Marvel\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Marvel\Database\Models\DeliveryAgentProfile;
use Marvel\Database\Models\User;

class DeliveryAgentProfileFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = DeliveryAgentProfile::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'user_id' => User::factory(),
            'phone_number' => $this->faker->phoneNumber,
            'availability_status' => 'ONLINE', // Default to online for easier testing of nearest agents
            'current_location' => null, // Default to null, forcing tests to explicitly set location for nearest agent tests
            'kyc_status' => 'APPROVED', // Default to approved for easier testing of nearest agents
            'kyc_documents' => [], // Default to empty array, as it's cast to json
            'kyc_rejection_reason' => null,
            'active_vehicle_id' => null, // Assuming it can be null initially or linked later
            'use_wallet_for_earnings' => $this->faker->boolean,
            'wallet_points_conversion_rate' => $this->faker->randomFloat(2, 0.5, 2.0),
            'performance_rating' => $this->faker->randomFloat(1, 3, 5),
            'total_deliveries_completed' => $this->faker->numberBetween(0, 500),
            'last_check_in_at' => $this->faker->dateTimeThisYear(),
        ];
    }

    /**
     * Indicate that the agent is active.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'availability_status' => 'ONLINE',
                'kyc_status' => 'APPROVED',
            ];
        });
    }

    /**
     * Indicate that the agent is inactive.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'availability_status' => 'OFFLINE',
                'kyc_status' => 'PENDING',
            ];
        });
    }
}