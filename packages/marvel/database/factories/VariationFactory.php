<?php

namespace Marvel\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Marvel\Database\Models\Variation;
use Marvel\Database\Models\Product;

class VariationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Variation::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    protected $validAttributes = [
        'title',
        'product_id',
        'price',
        'sale_price',
        'quantity',
        'sold_quantity',
        'is_disable',
        'sku',
        'options',
        'image',
        'is_digital',
        'digital_file_tracker',
        'language'
    ];

    public function definition()
    {
        $data = [
            'title' => $this->faker->words(2, true),
            'product_id' => Product::factory(),
            'price' => strval($this->faker->randomFloat(2, 10, 1000)),
            'sale_price' => strval($this->faker->randomFloat(2, 5, 900)),
            'quantity' => $this->faker->numberBetween(1, 100),
            'sold_quantity' => 0,
            'is_disable' => $this->faker->boolean(10),
            'sku' => $this->faker->unique()->ean13,
            'options' => ['color' => $this->faker->colorName(), 'size' => $this->faker->randomElement(['S', 'M', 'L', 'XL'])],
            'image' => null,
            'is_digital' => false,
            'digital_file_tracker' => null,
            'language' => DEFAULT_LANGUAGE
        ];
        
        // Only return attributes that exist in the database
        return collect($data)->only($this->validAttributes)->toArray();
    }
}