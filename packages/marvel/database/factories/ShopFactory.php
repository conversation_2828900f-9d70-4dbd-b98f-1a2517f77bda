<?php

namespace Marvel\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\User;

class ShopFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Shop::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $owner = User::factory()->create();
        
        return [
            'name' => $this->faker->company,
            'slug' => $this->faker->slug,
            'description' => $this->faker->paragraph,
            'owner_id' => $owner->id,
            'is_active' => true,
            'address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->country,
            ],
            'settings' => [
                'contact' => $this->faker->phoneNumber,
                'website' => $this->faker->url,
                'location' => [
                    'lat' => $this->faker->latitude,
                    'lng' => $this->faker->longitude,
                ],
            ],
        ];
    }
}
