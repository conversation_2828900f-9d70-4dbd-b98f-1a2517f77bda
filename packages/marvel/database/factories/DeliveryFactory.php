<?php

namespace Marvel\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\User;
use Marvel\Database\Models\Order;

class DeliveryFactory extends Factory
{
    protected $model = Delivery::class;

    public function definition()
    {
        return [
            'order_id' => Order::factory(),
            'delivery_agent_user_id' => User::factory(),
            'assigned_by_user_id' => User::factory(),
            'status' => $this->faker->randomElement(['ASSIGNED', 'ACCEPTED', 'PICKED_UP', 'IN_TRANSIT', 'DELIVERED']),
            'pickup_addresses' => [
                [
                    'address' => $this->faker->streetAddress,
                    'city' => $this->faker->city,
                    'state' => $this->faker->state,
                    'zip' => $this->faker->postcode,
                    'lat' => $this->faker->latitude,
                    'lng' => $this->faker->longitude,
                ]
            ],
            'delivery_address' => [
                'address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'lat' => $this->faker->latitude,
                'lng' => $this->faker->longitude,
            ],
            'delivery_fee' => $this->faker->randomFloat(2, 5, 50),
            'estimated_delivery_time' => $this->faker->dateTimeBetween('now', '+2 hours'),
            'actual_delivery_time' => null,
            'delivery_notes' => $this->faker->optional()->sentence,
            'proof_of_delivery' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
