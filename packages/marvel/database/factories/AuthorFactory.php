<?php

namespace Marvel\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Marvel\Database\Models\Author;

class AuthorFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Author::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => $this->faker->name,
            'slug' => $this->faker->slug,
            'bio' => $this->faker->paragraph,
            'quote' => $this->faker->sentence,
            'born' => $this->faker->country,
            'death' => $this->faker->country,
            'socials' => [],
            'image' => [],
            'cover_image' => [],
        ];
    }
}