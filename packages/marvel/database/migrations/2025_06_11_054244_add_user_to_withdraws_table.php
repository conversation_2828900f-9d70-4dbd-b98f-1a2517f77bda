<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('withdraws', function (Blueprint $table) {
            if (config('database.default') !== 'sqlite') {
                $table->dropForeign(['shop_id']);
            }
            
            $table->unsignedBigInteger('user_id')->nullable()->after('shop_id');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            
            $table->unsignedBigInteger('shop_id')->nullable()->change();
            
            if (config('database.default') !== 'sqlite') {
                $table->foreign('shop_id')->references('id')->on('shops')->onDelete('cascade');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('withdraws', function (Blueprint $table) {
            if (config('database.default') !== 'sqlite') {
                $table->dropForeign(['shop_id']);
            }

            $table->unsignedBigInteger('shop_id')->nullable(false)->change();

            if (config('database.default') !== 'sqlite') {
                $table->foreign('shop_id')->references('id')->on('shops')->onDelete('cascade');
                $table->dropForeign(['user_id']);
            }

            $table->dropColumn('user_id');
        });
    }
};
