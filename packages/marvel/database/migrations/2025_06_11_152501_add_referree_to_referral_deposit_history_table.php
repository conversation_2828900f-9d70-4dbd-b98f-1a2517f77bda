<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('referral_deposit_history', function (Blueprint $table) {
            $table->unsignedBigInteger('referree_id')->nullable();
            
             $table->foreign('referree_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('referral_deposit_history', function (Blueprint $table) {
            $table->dropForeign(['referree_id']);

            $table->dropColumn('referree_id');
        });
    }
};
