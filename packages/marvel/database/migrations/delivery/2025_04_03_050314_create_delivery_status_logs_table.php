<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_status_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('delivery_id');
            $table->enum('status', [
                'PENDING_ASSIGNMENT',
                'ASSIGNED',
                'REJECTED_BY_AGENT',
                'ACCEPTED_BY_AGENT',
                'PICKED_UP',
                'IN_TRANSIT',
                'REACHED_DESTINATION',
                'DELIVERED',
                'COMPLETED',
                'CANCELLED',
                'FAILED_DELIVERY'
            ]);
            $table->unsignedBigInteger('user_id')->nullable();
            $table->text('notes')->nullable();
            $table->json('location')->nullable();
            $table->timestamp('created_at');

            $table->foreign('delivery_id')->references('id')->on('deliveries')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');

            // Note: Spatial index removed due to compatibility issues
            // For location-based queries, we'll use regular JSON queries instead
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_status_logs');
    }
};
