<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, add the columns without the foreign key constraint
        Schema::table('orders', function (Blueprint $table) {
            $table->unsignedBigInteger('delivery_id')->nullable()->after('id');
            $table->boolean('requires_delivery')->default(true)->after('order_status');

            // Add index for finding orders needing delivery assignment
            $table->index(['requires_delivery', 'order_status', 'payment_status'], 'orders_delivery_status_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Drop the index first
            $table->dropIndex('orders_delivery_status_index');

            // Drop the foreign key constraint
            $table->dropForeign(['delivery_id']);

            // Drop the columns
            $table->dropColumn('delivery_id');
            $table->dropColumn('requires_delivery');
        });
    }
};
