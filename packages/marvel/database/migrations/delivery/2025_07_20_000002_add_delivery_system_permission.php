<?php

use Illuminate\Database\Migrations\Migration;
use Marvel\Enums\Permission;
use Spatie\Permission\Models\Permission as PermissionModel;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the delivery system permission
        PermissionModel::firstOrCreate([
            'name' => Permission::DELIVERY_SYSTEM,
            'guard_name' => 'api',
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the delivery system permission
        PermissionModel::where('name', Permission::DELIVERY_SYSTEM)
            ->where('guard_name', 'api')
            ->delete();
    }
};
