<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('delivery_agent_user_id');
            $table->enum('type', ['BIKE', 'CAR', 'VAN', 'OTHER']);
            $table->string('make')->nullable();
            $table->string('model')->nullable();
            $table->string('registration_number')->unique();
            $table->string('color')->nullable();
            $table->json('vehicle_documents')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->text('verification_notes')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('delivery_agent_user_id')->references('id')->on('users')->onDelete('cascade');
        });

        // Now that the vehicles table is created, we can add the foreign key to delivery_agent_profiles
        Schema::table('delivery_agent_profiles', function (Blueprint $table) {
            $table->foreign('active_vehicle_id')->references('id')->on('vehicles')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicles');
    }
};
