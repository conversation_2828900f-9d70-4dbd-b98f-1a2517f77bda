<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_agent_earnings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('delivery_agent_user_id')->unique();
            $table->decimal('total_earnings', 12, 2)->default(0);
            $table->decimal('withdrawn_amount', 12, 2)->default(0);
            $table->decimal('current_balance', 12, 2)->default(0);
            $table->decimal('pending_withdrawal_amount', 12, 2)->default(0);
            $table->json('payment_info')->nullable();
            $table->timestamps();

            $table->foreign('delivery_agent_user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_agent_earnings');
    }
};
