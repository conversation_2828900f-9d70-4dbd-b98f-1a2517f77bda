<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('deliveries', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->unsignedBigInteger('delivery_agent_user_id')->nullable();
            $table->enum('status', [
                'PENDING_ASSIGNMENT',
                'ASSIGNED',
                'REJECTED_BY_AGENT',
                'ACCEPTED_BY_AGENT',
                'PICKED_UP',
                'IN_TRANSIT',
                'REACHED_DESTINATION',
                'DELIVERED',
                'COMPLETED',
                'CANCELLED',
                'FAILED_DELIVERY'
            ])->default('PENDING_ASSIGNMENT')->index();
            $table->json('pickup_address')->nullable();
            $table->json('delivery_address')->nullable();
            $table->timestamp('estimated_delivery_time')->nullable();
            $table->timestamp('assigned_at')->nullable();
            $table->timestamp('accepted_at')->nullable();
            $table->timestamp('picked_up_at')->nullable();
            $table->timestamp('reached_destination_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->text('cancellation_reason')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->text('failure_reason')->nullable();
            $table->decimal('delivery_fee', 10, 2)->nullable();
            $table->text('notes_by_agent')->nullable();
            $table->text('notes_by_admin')->nullable();
            $table->json('proof_of_delivery')->nullable();
            $table->enum('pod_type', ['SIGNATURE', 'PHOTO', 'CODE', 'NONE'])->nullable();
            $table->unsignedBigInteger('assigned_by_user_id')->nullable();
            $table->enum('assignment_type', ['MANUAL', 'AUTOMATIC'])->default('AUTOMATIC');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
            $table->foreign('delivery_agent_user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('assigned_by_user_id')->references('id')->on('users')->onDelete('set null');

            // Add indexes for common queries
            $table->index('order_id');
            $table->index(['delivery_agent_user_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('deliveries');
    }
};
