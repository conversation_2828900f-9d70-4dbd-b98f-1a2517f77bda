<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add unique constraint on delivery_id to prevent multiple orders with same delivery
        try {
            Schema::table('orders', function (Blueprint $table) {
                $table->unique('delivery_id', 'orders_delivery_id_unique');
            });
        } catch (\Exception $e) {
            // Constraint might already exist, ignore
        }

        // Add unique constraint on order_id to prevent multiple deliveries for same order
        try {
            Schema::table('deliveries', function (Blueprint $table) {
                $table->unique('order_id', 'deliveries_order_id_unique');
            });
        } catch (\Exception $e) {
            // Constraint might already exist, ignore
        }

        // Add index for agent workload queries
        try {
            Schema::table('deliveries', function (Blueprint $table) {
                $table->index(['delivery_agent_user_id', 'status', 'created_at'], 'deliveries_agent_workload_index');
            });
        } catch (\Exception $e) {
            // Index might already exist, ignore
        }

        // Create a table to track agent assignment round-robin state if it doesn't exist
        if (!Schema::hasTable('delivery_agent_assignment_state')) {
            Schema::create('delivery_agent_assignment_state', function (Blueprint $table) {
                $table->id();
                $table->string('region_key')->unique(); // For future regional round-robin
                $table->unsignedBigInteger('last_assigned_agent_id')->nullable();
                $table->unsignedInteger('assignment_count')->default(0);
                $table->timestamp('last_assignment_at')->nullable();
                $table->timestamps();

                $table->foreign('last_assigned_agent_id')->references('id')->on('users')->onDelete('set null');
                $table->index(['region_key', 'last_assignment_at'], 'agent_assignment_state_region_time_idx');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropUnique('orders_delivery_id_unique');
        });

        Schema::table('deliveries', function (Blueprint $table) {
            $table->dropUnique('deliveries_order_id_unique');
            $table->dropIndex('deliveries_agent_workload_index');
        });

        Schema::dropIfExists('delivery_agent_assignment_state');
    }
};
