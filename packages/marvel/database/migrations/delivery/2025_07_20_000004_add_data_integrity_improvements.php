<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add soft deletes to critical delivery tables (if not already present)
        Schema::table('deliveries', function (Blueprint $table) {
            if (!Schema::hasColumn('deliveries', 'deleted_at')) {
                $table->softDeletes();
            }
            if (!Schema::hasColumn('deliveries', 'version')) {
                $table->timestamp('version')->default(now()); // For optimistic locking
            }
        });

        Schema::table('delivery_agent_profiles', function (Blueprint $table) {
            if (!Schema::hasColumn('delivery_agent_profiles', 'deleted_at')) {
                $table->softDeletes();
            }
            if (!Schema::hasColumn('delivery_agent_profiles', 'version')) {
                $table->timestamp('version')->default(now()); // For optimistic locking
            }
        });

        // Note: Data validation constraints are handled at application level
        // due to compatibility issues between MySQL and MariaDB versions

        // Add audit trail table for delivery changes
        if (!Schema::hasTable('delivery_audit_log')) {
            Schema::create('delivery_audit_log', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('delivery_id');
            $table->string('event_type'); // 'created', 'updated', 'status_changed', 'deleted'
            $table->json('old_values')->nullable();
            $table->json('new_values')->nullable();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('user_type')->nullable(); // 'agent', 'admin', 'system'
            $table->ipAddress('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->text('reason')->nullable();
            $table->timestamps();

            $table->index(['delivery_id', 'created_at']);
            $table->index(['event_type', 'created_at']);
            $table->index(['user_id', 'created_at']);
            });
        }

        // Add location history table for agent tracking
        if (!Schema::hasTable('delivery_agent_location_history')) {
            Schema::create('delivery_agent_location_history', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('agent_user_id');
            $table->json('location'); // {lat, lng}
            $table->string('accuracy')->nullable(); // GPS accuracy in meters
            $table->string('source')->default('mobile_app'); // 'mobile_app', 'web', 'api'
            $table->timestamp('recorded_at');
            $table->timestamps();

            $table->index(['agent_user_id', 'recorded_at']);
            $table->foreign('agent_user_id')->references('id')->on('users')->onDelete('cascade');
            });
        }

        // Add delivery performance metrics table
        if (!Schema::hasTable('delivery_performance_metrics')) {
            Schema::create('delivery_performance_metrics', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('delivery_id');
            $table->unsignedBigInteger('agent_user_id');
            $table->timestamp('assigned_at')->nullable();
            $table->timestamp('accepted_at')->nullable();
            $table->timestamp('picked_up_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->integer('total_duration_minutes')->nullable(); // Total time from assignment to delivery
            $table->integer('pickup_duration_minutes')->nullable(); // Time from assignment to pickup
            $table->integer('delivery_duration_minutes')->nullable(); // Time from pickup to delivery
            $table->decimal('distance_traveled_km', 8, 2)->nullable();
            $table->decimal('estimated_distance_km', 8, 2)->nullable();
            $table->integer('eta_accuracy_minutes')->nullable(); // Difference between estimated and actual delivery time
            $table->decimal('customer_rating', 3, 2)->nullable(); // 1.00 to 5.00
            $table->text('performance_notes')->nullable();
            $table->timestamps();

            $table->unique('delivery_id');
            $table->index(['agent_user_id', 'delivered_at']);
            $table->foreign('delivery_id')->references('id')->on('deliveries')->onDelete('cascade');
            $table->foreign('agent_user_id')->references('id')->on('users')->onDelete('cascade');
            });
        }

        // Add delivery exceptions/issues tracking
        if (!Schema::hasTable('delivery_exceptions')) {
            Schema::create('delivery_exceptions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('delivery_id');
            $table->string('exception_type'); // 'delay', 'failed_delivery', 'customer_unavailable', 'address_issue', etc.
            $table->string('severity')->default('medium'); // 'low', 'medium', 'high', 'critical'
            $table->text('description');
            $table->json('metadata')->nullable(); // Additional context data
            $table->timestamp('occurred_at');
            $table->unsignedBigInteger('reported_by_user_id')->nullable();
            $table->string('resolution_status')->default('open'); // 'open', 'in_progress', 'resolved', 'escalated'
            $table->text('resolution_notes')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->timestamps();

            $table->index(['delivery_id', 'occurred_at']);
            $table->index(['exception_type', 'severity']);
            $table->index(['resolution_status', 'occurred_at']);
            $table->foreign('delivery_id')->references('id')->on('deliveries')->onDelete('cascade');
            $table->foreign('reported_by_user_id')->references('id')->on('users')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop new tables
        Schema::dropIfExists('delivery_exceptions');
        Schema::dropIfExists('delivery_performance_metrics');
        Schema::dropIfExists('delivery_agent_location_history');
        Schema::dropIfExists('delivery_audit_log');

        // Remove soft deletes and version columns
        Schema::table('deliveries', function (Blueprint $table) {
            if (Schema::hasColumn('deliveries', 'deleted_at')) {
                $table->dropSoftDeletes();
            }
            if (Schema::hasColumn('deliveries', 'version')) {
                $table->dropColumn('version');
            }
        });

        Schema::table('delivery_agent_profiles', function (Blueprint $table) {
            if (Schema::hasColumn('delivery_agent_profiles', 'deleted_at')) {
                $table->dropSoftDeletes();
            }
            if (Schema::hasColumn('delivery_agent_profiles', 'version')) {
                $table->dropColumn('version');
            }
        });
    }
};
