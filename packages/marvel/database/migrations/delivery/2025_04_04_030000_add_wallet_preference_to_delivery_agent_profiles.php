<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddWalletPreferenceToDeliveryAgentProfiles extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('delivery_agent_profiles', function (Blueprint $table) {
            $table->boolean('use_wallet_for_earnings')->default(false)->after('active_vehicle_id');
            $table->decimal('wallet_points_conversion_rate', 10, 2)->nullable()->after('use_wallet_for_earnings');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('delivery_agent_profiles', function (Blueprint $table) {
            $table->dropColumn('use_wallet_for_earnings');
            $table->dropColumn('wallet_points_conversion_rate');
        });
    }
}
