<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_agent_location_history', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('delivery_agent_user_id');
            $table->json('location');
            $table->timestamp('timestamp')->index();
            $table->unsignedBigInteger('delivery_id')->nullable();
            $table->timestamp('created_at');

            $table->foreign('delivery_agent_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('delivery_id')->references('id')->on('deliveries')->onDelete('set null');

            // Note: Spatial index removed due to compatibility issues
            // For location-based queries, we'll use regular JSON queries instead

            // Add index for common queries with a shorter name
            $table->index(['delivery_agent_user_id', 'timestamp'], 'agent_location_timestamp_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_agent_location_history');
    }
};
