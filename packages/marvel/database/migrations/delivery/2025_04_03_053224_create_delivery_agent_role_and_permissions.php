<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Note: Roles and permissions should be created using seeders, not migrations.
     * See Marvel\Database\Seeders\DeliveryPermissionSeeder for the proper way to create
     * the delivery agent role and permissions.
     */
    public function up(): void
    {
        // This migration previously created roles and permissions directly in the database.
        // This approach has been replaced with a proper seeder: DeliveryPermissionSeeder.
        // No schema changes are needed in this migration.
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No schema changes to reverse.
    }
};
