<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_agent_withdrawals', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('delivery_agent_user_id');
            $table->decimal('amount', 10, 2);
            $table->enum('status', ['PENDING', 'APPROVED', 'PROCESSING', 'COMPLETED', 'REJECTED'])->default('PENDING')->index();
            $table->json('payment_method_details')->nullable();
            $table->string('transaction_reference')->nullable();
            $table->unsignedBigInteger('processed_by_user_id')->nullable();
            $table->text('notes')->nullable();
            $table->timestamp('requested_at')->useCurrent();
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('delivery_agent_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('processed_by_user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_agent_withdrawals');
    }
};
