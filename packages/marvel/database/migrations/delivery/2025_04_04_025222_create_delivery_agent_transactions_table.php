<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_agent_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('delivery_agent_user_id');
            $table->string('transaction_type'); // EARNING, WITHDRAWAL, REFUND, ADJUSTMENT
            $table->string('reference_type')->nullable(); // DELIVERY, WITHDRAWAL, MANUAL
            $table->unsignedBigInteger('reference_id')->nullable(); // ID of the related entity (delivery_id, withdrawal_id)
            $table->decimal('amount', 10, 2);
            $table->string('status'); // PENDING, COMPLETED, FAILED, CANCELLED
            $table->json('payment_details')->nullable(); // Details about the payment method
            $table->string('payment_method')->nullable(); // MOBILE_MONEY, BANK_TRANSFER, CASH, etc.
            $table->string('provider')->nullable(); // ORANGE_MONEY, MTN_MOBILE_MONEY, etc.
            $table->string('transaction_id')->nullable(); // External transaction ID
            $table->text('notes')->nullable();
            $table->unsignedBigInteger('created_by_user_id')->nullable(); // User who created this transaction
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('delivery_agent_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('created_by_user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_agent_transactions');
    }
};
