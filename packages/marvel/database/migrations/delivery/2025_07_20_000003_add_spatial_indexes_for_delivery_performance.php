<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add indexes for delivery system performance
        // Only using universally compatible indexes (no JSON function indexes)

        // Composite index for availability and KYC status (frequently queried together)
        Schema::table('delivery_agent_profiles', function (Blueprint $table) {
            $table->index(['availability_status', 'kyc_status'], 'idx_agent_availability_kyc');
        });

        // Index for delivery status (used in active delivery counting)
        Schema::table('deliveries', function (Blueprint $table) {
            $table->index('status', 'idx_deliveries_status');
            $table->index(['delivery_agent_user_id', 'status'], 'idx_deliveries_agent_status');
        });

        // Index for order delivery requirements
        Schema::table('orders', function (Blueprint $table) {
            $table->index('requires_delivery', 'idx_orders_requires_delivery');
            $table->index(['requires_delivery', 'order_status'], 'idx_orders_delivery_status');
        });

        // Note: JSON function indexes for location data are not included due to
        // compatibility issues between MySQL and MariaDB versions.
        // The application will rely on bounding box pre-filtering for performance.
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop indexes
        Schema::table('delivery_agent_profiles', function (Blueprint $table) {
            $table->dropIndex('idx_agent_availability_kyc');
        });

        Schema::table('deliveries', function (Blueprint $table) {
            $table->dropIndex('idx_deliveries_status');
            $table->dropIndex('idx_deliveries_agent_status');
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_orders_requires_delivery');
            $table->dropIndex('idx_orders_delivery_status');
        });
    }
};
