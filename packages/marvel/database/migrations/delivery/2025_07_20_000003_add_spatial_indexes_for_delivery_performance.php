<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add indexes for JSON location fields to improve spatial query performance
        
        // Index for delivery agent profile current_location (MariaDB compatible)
        try {
            DB::statement("
                CREATE INDEX idx_delivery_agent_profiles_location_lat
                ON delivery_agent_profiles (CAST(JSON_EXTRACT(current_location, '$.lat') AS DECIMAL(10,8)))
            ");
        } catch (\Exception $e) {
            // Fallback for older MySQL/MariaDB versions
            DB::statement("
                CREATE INDEX idx_delivery_agent_profiles_location_lat
                ON delivery_agent_profiles (CAST(JSON_UNQUOTE(JSON_EXTRACT(current_location, '$.lat')) AS DECIMAL(10,8)))
            ");
        }

        try {
            DB::statement("
                CREATE INDEX idx_delivery_agent_profiles_location_lng
                ON delivery_agent_profiles (CAST(JSON_EXTRACT(current_location, '$.lng') AS DECIMAL(11,8)))
            ");
        } catch (\Exception $e) {
            // Fallback for older MySQL/MariaDB versions
            DB::statement("
                CREATE INDEX idx_delivery_agent_profiles_location_lng
                ON delivery_agent_profiles (CAST(JSON_UNQUOTE(JSON_EXTRACT(current_location, '$.lng')) AS DECIMAL(11,8)))
            ");
        }
        
        // Composite index for availability and KYC status (frequently queried together)
        Schema::table('delivery_agent_profiles', function (Blueprint $table) {
            $table->index(['availability_status', 'kyc_status'], 'idx_agent_availability_kyc');
        });
        
        // Index for delivery status (used in active delivery counting)
        Schema::table('deliveries', function (Blueprint $table) {
            $table->index('status', 'idx_deliveries_status');
            $table->index(['delivery_agent_user_id', 'status'], 'idx_deliveries_agent_status');
        });
        
        // Index for shop settings location (if we add spatial queries for shops)
        try {
            DB::statement("
                CREATE INDEX idx_shops_location_lat
                ON shops (CAST(JSON_EXTRACT(settings, '$.location.lat') AS DECIMAL(10,8)))
            ");
        } catch (\Exception $e) {
            // Fallback for older MySQL/MariaDB versions
            DB::statement("
                CREATE INDEX idx_shops_location_lat
                ON shops (CAST(JSON_UNQUOTE(JSON_EXTRACT(settings, '$.location.lat')) AS DECIMAL(10,8)))
            ");
        }

        try {
            DB::statement("
                CREATE INDEX idx_shops_location_lng
                ON shops (CAST(JSON_EXTRACT(settings, '$.location.lng') AS DECIMAL(11,8)))
            ");
        } catch (\Exception $e) {
            // Fallback for older MySQL/MariaDB versions
            DB::statement("
                CREATE INDEX idx_shops_location_lng
                ON shops (CAST(JSON_UNQUOTE(JSON_EXTRACT(settings, '$.location.lng')) AS DECIMAL(11,8)))
            ");
        }
        
        // Index for order delivery requirements
        Schema::table('orders', function (Blueprint $table) {
            $table->index('requires_delivery', 'idx_orders_requires_delivery');
            $table->index(['requires_delivery', 'order_status'], 'idx_orders_delivery_status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop JSON indexes
        DB::statement("DROP INDEX IF EXISTS idx_delivery_agent_profiles_location_lat ON delivery_agent_profiles");
        DB::statement("DROP INDEX IF EXISTS idx_delivery_agent_profiles_location_lng ON delivery_agent_profiles");
        DB::statement("DROP INDEX IF EXISTS idx_shops_location_lat ON shops");
        DB::statement("DROP INDEX IF EXISTS idx_shops_location_lng ON shops");
        
        // Drop regular indexes
        Schema::table('delivery_agent_profiles', function (Blueprint $table) {
            $table->dropIndex('idx_agent_availability_kyc');
        });
        
        Schema::table('deliveries', function (Blueprint $table) {
            $table->dropIndex('idx_deliveries_status');
            $table->dropIndex('idx_deliveries_agent_status');
        });
        
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_orders_requires_delivery');
            $table->dropIndex('idx_orders_delivery_status');
        });
    }
};
