<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_agent_profiles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->unique();
            $table->enum('availability_status', ['ONLINE', 'OFFLINE', 'ON_DELIVERY'])->default('OFFLINE');
            $table->json('current_location')->nullable();
            $table->enum('kyc_status', ['PENDING', 'SUBMITTED', 'APPROVED', 'REJECTED'])->default('PENDING');
            $table->json('kyc_documents')->nullable();
            $table->text('kyc_rejection_reason')->nullable();
            $table->unsignedBigInteger('active_vehicle_id')->nullable();
            $table->float('performance_rating')->nullable();
            $table->unsignedInteger('total_deliveries_completed')->default(0);
            $table->timestamp('last_check_in_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            // We'll add the foreign key for active_vehicle_id after the vehicles table is created

            // Note: Spatial index removed due to compatibility issues
            // For location-based queries, we'll use regular JSON queries instead
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_agent_profiles');
    }
};
