<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('weight')->default('1');
            $table->enum('fragility_level' , ['VERY_FRAGILE' , 'SLIGHTLY_FRAGILE' , 'NOT_FRAGILE'])->default('SLIGHTLY_FRAGILE');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn(['weight' , 'fragility_level']);
        });
    }
};
