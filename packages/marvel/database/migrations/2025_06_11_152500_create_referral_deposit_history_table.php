<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
        Schema::create('referral_deposit_history', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->foreignId('account_balance_id')->constrained('account_balance')->onDelete('cascade');
            $table->text('description')->nullable();
        });
    }


    public function down(): void
    {
        Schema::dropIfExists('referral_deposit_history');
    }
};
