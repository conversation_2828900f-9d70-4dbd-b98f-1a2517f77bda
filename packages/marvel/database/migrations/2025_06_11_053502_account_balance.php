<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('account_balance', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->unsignedDecimal('total_earnings' , 10 , 2)->default(0.00);
            $table->unsignedDecimal('current_balance' , 10 ,2)->default(0.00);
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('account_balance');
    }
};
