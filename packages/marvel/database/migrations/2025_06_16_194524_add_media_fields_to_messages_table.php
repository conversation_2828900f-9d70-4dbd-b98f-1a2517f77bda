<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            $table->string('media_type')->nullable()->after('body'); 
            $table->string('media_url')->nullable()->after('media_type');
            $table->string('media_mime')->nullable()->after('media_url');
            $table->unsignedBigInteger('media_size')->nullable()->after('media_mime');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('messages', function (Blueprint $table) {
            $table->dropColumn(['media_type', 'media_url', 'media_mime', 'media_size']);
        });
    }
};
