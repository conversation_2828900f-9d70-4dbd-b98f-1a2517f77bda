<?xml version="1.0" encoding="UTF-8"?>
<phpunit bootstrap="vendor/autoload.php"
         backupGlobals="false"
         backupStaticAttributes="false"
         colors="true"
         verbose="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false">
    <testsuites>
        <testsuite name="Marvel">
            <directory suffix="Test.php">./tests/</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist>
            <directory>src/</directory>
        </whitelist>
    </filter>
    <php>
        <server name="APP_ENV" value="testing"/>
        <server name="BCRYPT_ROUNDS" value="4"/>
        <server name="CACHE_DRIVER" value="array"/>
        <server name="DB_CONNECTION" value="mysql"/>
        <server name="DB_HOST" value="127.0.0.1"/>
        <server name="DB_PORT" value="3306"/>
        <server name="DB_DATABASE" value="chawkbazar_laravel_testing"/>
        <server name="DB_USERNAME" value="root"/>
        <server name="DB_PASSWORD" value=""/>
        <server name="MAIL_MAILER" value="array"/>
        <server name="QUEUE_CONNECTION" value="sync"/>
        <server name="SESSION_DRIVER" value="array"/>
        <server name="TELESCOPE_ENABLED" value="false"/>
        <!-- Delivery system test configuration -->
        <server name="DELIVERY_AUTO_ASSIGNMENT_ENABLED" value="true"/>
        <server name="DELIVERY_AUTO_ASSIGNMENT_RADIUS" value="15"/>
        <server name="DELIVERY_MAX_CONCURRENT_PER_AGENT" value="3"/>
        <server name="DELIVERY_AGENT_SELECTION_STRATEGY" value="nearest"/>
    </php>
</phpunit>
