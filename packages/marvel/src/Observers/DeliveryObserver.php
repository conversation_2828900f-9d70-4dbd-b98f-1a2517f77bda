<?php

namespace Marvel\Observers;

use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\DeliveryAgentEarning;
use Marvel\Database\Models\DeliveryAgentLocationHistory;
use Marvel\Database\Models\Wallet;
use Marvel\Database\Repositories\DeliveryAgentTransactionRepository;
use Marvel\Traits\WalletsTrait;

class DeliveryObserver
{
    use WalletsTrait;
    /**
     * Handle the Delivery "created" event.
     *
     * @param  Delivery  $delivery
     * @return void
     */
    public function created(Delivery $delivery)
    {
        //
    }

    /**
     * Handle the Delivery "updated" event.
     *
     * @param  Delivery  $delivery
     * @return void
     */
    public function updated(Delivery $delivery)
    {
        // Check if status changed to COMPLETED
        if ($delivery->isDirty('status') && $delivery->status === 'COMPLETED') {
            $this->calculateAndCreditEarnings($delivery);
        }

        // If location is provided in the delivery, save it to location history
        if ($delivery->isDirty('status') && $delivery->delivery_agent_user_id) {
            $this->saveLocationHistory($delivery);
        }
    }

    /**
     * Handle the Delivery "deleted" event.
     *
     * @param  Delivery  $delivery
     * @return void
     */
    public function deleted(Delivery $delivery)
    {
        //
    }

    /**
     * Handle the Delivery "restored" event.
     *
     * @param  Delivery  $delivery
     * @return void
     */
    public function restored(Delivery $delivery)
    {
        //
    }

    /**
     * Handle the Delivery "force deleted" event.
     *
     * @param  Delivery  $delivery
     * @return void
     */
    public function forceDeleted(Delivery $delivery)
    {
        //
    }

    /**
     * Calculate and credit earnings to the delivery agent.
     *
     * @param  Delivery  $delivery
     * @return void
     */
    private function calculateAndCreditEarnings(Delivery $delivery)
    {
        // Skip if no delivery agent or no delivery fee
        if (!$delivery->delivery_agent_user_id || !$delivery->delivery_fee) {
            return;
        }

        $agent = $delivery->deliveryAgent;
        if (!$agent) {
            return;
        }

        $profile = $agent->delivery_agent_profile;

        // Check if agent prefers to use wallet for earnings
        if ($profile && $profile->use_wallet_for_earnings) {
            // Credit to wallet
            $this->creditToWallet($delivery, $agent, $profile);
        } else {
            // Credit to earnings account
            $this->creditToEarningsAccount($delivery);
        }

        // Create transaction record
        try {
            $transactionRepository = app(DeliveryAgentTransactionRepository::class);
            $transactionRepository->createEarningTransaction([
                'delivery_agent_user_id' => $delivery->delivery_agent_user_id,
                'delivery_id' => $delivery->id,
                'amount' => $delivery->delivery_fee,
                'notes' => 'Earnings from delivery #' . $delivery->id,
            ]);
        } catch (\Exception $e) {
            // Log error but don't stop the process
            \Log::error('Failed to create transaction record: ' . $e->getMessage());
        }
    }

    /**
     * Credit earnings to the agent's wallet.
     *
     * @param  Delivery  $delivery
     * @param  User  $agent
     * @param  DeliveryAgentProfile  $profile
     * @return void
     */
    private function creditToWallet($delivery, $agent, $profile)
    {
        // Get or create wallet
        $wallet = Wallet::firstOrCreate(
            ['customer_id' => $agent->id],
            [
                'total_points' => 0,
                'points_used' => 0,
                'available_points' => 0,
            ]
        );

        // Convert currency to points
        $conversionRate = $profile->wallet_points_conversion_rate ?? $this->currencyToWalletRatio();
        $points = $delivery->delivery_fee * $conversionRate;

        // Update wallet
        $wallet->total_points += $points;
        $wallet->available_points += $points;
        $wallet->save();

        // Also update the earnings record for tracking purposes
        $earnings = DeliveryAgentEarning::firstOrCreate(
            ['delivery_agent_user_id' => $delivery->delivery_agent_user_id],
            [
                'total_earnings' => 0,
                'withdrawn_amount' => 0,
                'current_balance' => 0,
                'pending_withdrawal_amount' => 0,
            ]
        );

        $earnings->total_earnings += $delivery->delivery_fee;
        $earnings->save();
    }

    /**
     * Credit earnings to the agent's earnings account.
     *
     * @param  Delivery  $delivery
     * @return void
     */
    private function creditToEarningsAccount($delivery)
    {
        // Get or create earnings record for the agent
        $earnings = DeliveryAgentEarning::firstOrCreate(
            ['delivery_agent_user_id' => $delivery->delivery_agent_user_id],
            [
                'total_earnings' => 0,
                'withdrawn_amount' => 0,
                'current_balance' => 0,
                'pending_withdrawal_amount' => 0,
            ]
        );

        // Update earnings
        $earnings->total_earnings += $delivery->delivery_fee;
        $earnings->current_balance += $delivery->delivery_fee;
        $earnings->save();
    }

    /**
     * Save location history for the delivery agent.
     *
     * @param  Delivery  $delivery
     * @return void
     */
    private function saveLocationHistory(Delivery $delivery)
    {
        // Get the latest status log with location
        $latestLog = $delivery->statusLogs()
            ->whereNotNull('location')
            ->orderBy('created_at', 'desc')
            ->first();

        if ($latestLog && $latestLog->location) {
            // Save to location history
            DeliveryAgentLocationHistory::create([
                'delivery_agent_user_id' => $delivery->delivery_agent_user_id,
                'location' => $latestLog->location,
                'timestamp' => $latestLog->created_at,
                'delivery_id' => $delivery->id,
                'created_at' => now(),
            ]);

            // Update agent's current location in profile
            $agent = $delivery->deliveryAgent;
            if ($agent && $agent->delivery_agent_profile) {
                $agent->delivery_agent_profile->current_location = $latestLog->location;
                $agent->delivery_agent_profile->save();
            }
        }
    }
}
