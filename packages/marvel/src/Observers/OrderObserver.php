<?php

namespace Marvel\Observers;

use Marvel\Database\Models\Order;
use Marvel\Jobs\AutoAssignDeliveryJob;
use Illuminate\Support\Facades\Log;

class OrderObserver
{
    // Define config keys as constants for better maintainability
    private const CONFIG_AUTO_ASSIGNMENT_ENABLED = 'marvel-delivery.auto_assignment_enabled';
    private const CONFIG_ASSIGNMENT_TRIGGER_STATUSES = 'marvel-delivery.assignment_trigger_statuses';
    private const CONFIG_ASSIGNMENT_PAYMENT_STATUSES = 'marvel-delivery.assignment_payment_statuses';
    private const CONFIG_AUTO_ASSIGNMENT_DELAY = 'marvel-delivery.auto_assignment_delay';

    /**
     * Handle the Order "created" event.
     *
     * @param  Order  $order
     * @return void
     */
    public function created(Order $order)
    {
        if ($this->shouldTriggerAutoAssignment($order)) {
            $this->dispatchAutoAssignment($order, 'created');
        }
    }

    /**
     * Handle the Order "updated" event.
     *
     * @param  Order  $order
     * @return void
     */
    public function updated(Order $order)
    {
        // Check if the order status or payment status changed to a deliverable state
        if ($this->shouldTriggerAutoAssignment($order) && $this->hasRelevantStatusChange($order)) {
            $this->dispatchAutoAssignment($order, 'updated');
        }
    }

    /**
     * Determine if automatic assignment should be triggered for this order.
     *
     * @param  Order  $order
     * @return bool
     */
    private function shouldTriggerAutoAssignment(Order $order): bool
    {
        // Check if automatic assignment is enabled
        if (!config(self::CONFIG_AUTO_ASSIGNMENT_ENABLED, true)) {
            return false;
        }

        // Order must require delivery
        if (!$order->requires_delivery) {
            return false;
        }

        // Order must not already have a delivery assigned
        if ($order->delivery_id) {
            return false;
        }

        // Order must be in a deliverable status
        if (!$this->isDeliverableStatus($order)) {
            return false;
        }

        // Payment must be successful or cash on delivery
        if (!$this->isPaymentReady($order)) {
            return false;
        }

        // Order must have a shop with location data
        if (!$this->hasValidShopLocation($order)) {
            return false;
        }

        return true;
    }

    /**
     * Check if the order has relevant status changes that should trigger assignment.
     *
     * @param  Order  $order
     * @return bool
     */
    private function hasRelevantStatusChange(Order $order): bool
    {
        // Check if order status changed to a deliverable state
        if ($order->isDirty('status') && $this->isDeliverableStatus($order)) {
            return true;
        }

        // Check if payment status changed to ready state
        if ($order->isDirty('payment_status') && $this->isPaymentReady($order)) {
            return true;
        }

        // Check if requires_delivery was just set to true
        if ($order->isDirty('requires_delivery') && $order->requires_delivery) {
            return true;
        }

        return false;
    }

    /**
     * Check if the order is in a deliverable status.
     *
     * @param  Order  $order
     * @return bool
     */
    private function isDeliverableStatus(Order $order): bool
    {
        $deliverableStatuses = config(self::CONFIG_ASSIGNMENT_TRIGGER_STATUSES, []);

        return in_array($order->status, $deliverableStatuses);
    }

    /**
     * Check if the payment is ready for delivery.
     *
     * @param  Order  $order
     * @return bool
     */
    private function isPaymentReady(Order $order): bool
    {
        $readyPaymentStatuses = config(self::CONFIG_ASSIGNMENT_PAYMENT_STATUSES, []);

        return in_array($order->payment_status, $readyPaymentStatuses);
    }

    /**
     * Check if the order's shop has valid location data.
     *
     * @param  Order  $order
     * @return bool
     */
    private function hasValidShopLocation(Order $order): bool
    {
        if (!$order->shop) {
            return false;
        }

        $settings = $order->shop->settings;
        if (!$settings || !isset($settings['location'])) {
            return false;
        }

        $location = $settings['location'];
        return isset($location['lat']) && isset($location['lng']) &&
               is_numeric($location['lat']) && is_numeric($location['lng']);
    }

    /**
     * Dispatch the automatic assignment job.
     *
     * @param  Order  $order
     * @param  string  $trigger
     * @return void
     */
    private function dispatchAutoAssignment(Order $order, string $trigger): void
    {
        try {
            // Get delay from config (default: no delay for immediate assignment)
            $delay = config(self::CONFIG_AUTO_ASSIGNMENT_DELAY, 0);

            if ($delay > 0) {
                AutoAssignDeliveryJob::dispatch($order, $trigger)->delay(now()->addMinutes($delay));
            } else {
                AutoAssignDeliveryJob::dispatch($order, $trigger);
            }

            Log::info('Auto assignment job dispatched', [
                'order_id' => $order->id,
                'tracking_number' => $order->tracking_number,
                'trigger' => $trigger,
                'delay_minutes' => $delay,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to dispatch auto assignment job', [
                'order_id' => $order->id,
                'tracking_number' => $order->tracking_number,
                'trigger' => $trigger,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
