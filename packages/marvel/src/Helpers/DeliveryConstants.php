<?php

namespace Marvel\Helpers;

class DeliveryConstants
{
    // Error messages
    public const NOT_AUTHORIZED = 'You are not authorized to perform this action';
    public const FORBIDDEN = 'You are not allowed to perform this action';
    public const NOT_FOUND = 'Resource not found';
    public const SOMETHING_WENT_WRONG = 'Something went wrong';
    public const CANNOT_PROCESS_ORDER = 'Could not process the order. Please contact the admin.';
    public const ITEM_DELETED = 'Item has been deleted successfully';
    public const CANNOT_DELETE_ACTIVE_VEHICLE = 'Cannot delete active vehicle. Please set another vehicle as active first';
    public const VEHICLE_SET_AS_ACTIVE = 'Vehicle has been set as active successfully';
    public const KYC_NOT_APPROVED = 'Your KYC is not approved yet. Please complete KYC verification before going online';
    public const NO_ACTIVE_VEHICLE = 'No active vehicle found. Please add a vehicle and set it as active before going online';
    public const VEHICLE_NOT_VERIFIED = 'Your vehicle is not verified yet. Please wait for admin approval before going online';

    // Delivery assignment errors
    public const ORDER_ALREADY_HAS_DELIVERY = 'This order already has a delivery assigned';
    public const ORDER_DOES_NOT_REQUIRE_DELIVERY = 'Order does not require delivery.';
    public const INVALID_DELIVERY_AGENT = 'Invalid delivery agent';
    public const AGENT_KYC_NOT_APPROVED = 'The selected agent\'s KYC is not approved';

    // Delivery status errors
    public const INVALID_STATUS_TRANSITION = 'Invalid status transition';
    public const INVALID_DELIVERY_STATE = 'Invalid delivery state';
    public const INVALID_STATUS_FOR_POD = 'Proof of delivery can only be added when the delivery status is REACHED_DESTINATION or DELIVERED';
    public const INVALID_STATUS_FOR_PAYMENT_CONFIRMATION = 'Payment can only be confirmed when the delivery status is DELIVERED';
    public const ORDER_NOT_FOUND = 'Order not found';

    // Earnings and withdrawal errors
    public const NO_EARNINGS_FOUND = 'No earnings record found for this agent';
    public const INVALID_WITHDRAWAL_AMOUNT = 'Invalid withdrawal amount';
    public const INSUFFICIENT_BALANCE = 'Insufficient balance for withdrawal';
    public const NO_PAYMENT_INFO = 'Please update your payment information before requesting a withdrawal';
    public const INVALID_WITHDRAWAL_STATUS = 'Invalid withdrawal status for this operation';
    public const TRANSACTION_REFERENCE_REQUIRED = 'Transaction reference is required';
    public const REJECTION_REASON_REQUIRED = 'Rejection reason is required';

    // Admin management errors
    public const AGENT_NOT_FOUND = 'Delivery agent not found';
    public const AGENT_PROFILE_NOT_FOUND = 'Delivery agent profile not found';
    public const VEHICLE_NOT_FOUND = 'Vehicle not found';
    public const DELIVERY_NOT_FOUND = 'Delivery not found or not in the correct status';
    public const WITHDRAWAL_NOT_FOUND = 'Withdrawal request not found';

    // Success messages
    public const DELIVERY_ASSIGNED = 'Delivery assigned successfully';
    public const DELIVERY_STATUS_UPDATED = 'Delivery status updated successfully';
    public const POD_ADDED = 'Proof of delivery added successfully';
    public const PAYMENT_CONFIRMED = 'Payment confirmed successfully';
    public const WITHDRAWAL_REQUESTED = 'Withdrawal requested successfully';
    public const WITHDRAWAL_APPROVED = 'Withdrawal approved successfully';
    public const WITHDRAWAL_PROCESSED = 'Withdrawal processed successfully';
    public const WITHDRAWAL_COMPLETED = 'Withdrawal completed successfully';
    public const WITHDRAWAL_REJECTED = 'Withdrawal rejected successfully';
    public const PAYMENT_INFO_UPDATED = 'Payment information updated successfully';
    public const KYC_APPROVED = 'KYC approved successfully';
    public const KYC_REJECTED = 'KYC rejected successfully';
    public const VEHICLE_VERIFIED = 'Vehicle verified successfully';
    public const AGENT_ACTIVATED = 'Agent activated successfully';
    public const AGENT_DEACTIVATED = 'Agent deactivated successfully';

    // videos upload
    public const VIDEO_UPLOAD_FAILED = 'Video upload failed';
    public const VIDEO_UPLOAD_SUCCESS = 'Video uploaded successfully';
}
