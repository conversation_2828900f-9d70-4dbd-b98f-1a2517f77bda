<?php

namespace Marvel\Helpers;

use <PERSON>\Exceptions\DeliveryException;
use Symfony\Component\HttpFoundation\Response;

class DeliveryExceptionHelper
{
    /**
     * Throw a bad request delivery exception.
     *
     * @param string $message
     * @param string|null $reason
     * @throws DeliveryException
     */
    public static function badRequest($message, $reason = null)
    {
        throw new DeliveryException(
            $message,
            $reason,
            Response::HTTP_BAD_REQUEST
        );
    }

    /**
     * Throw an unauthorized delivery exception.
     *
     * @param string $message
     * @param string|null $reason
     * @throws DeliveryException
     */
    public static function unauthorized($message = 'Unauthorized', $reason = null)
    {
        throw new DeliveryException(
            $message,
            $reason,
            Response::HTTP_UNAUTHORIZED
        );
    }

    /**
     * Throw a forbidden delivery exception.
     *
     * @param string $message
     * @param string|null $reason
     * @throws DeliveryException
     */
    public static function forbidden($message = 'Forbidden', $reason = null)
    {
        throw new DeliveryException(
            $message,
            $reason,
            Response::HTTP_FORBIDDEN
        );
    }

    /**
     * Throw a not found delivery exception.
     *
     * @param string $message
     * @param string|null $reason
     * @throws DeliveryException
     */
    public static function notFound($message = 'Resource not found', $reason = null)
    {
        throw new DeliveryException(
            $message,
            $reason,
            Response::HTTP_NOT_FOUND
        );
    }

    /**
     * Throw a conflict delivery exception.
     *
     * @param string $message
     * @param string|null $reason
     * @throws DeliveryException
     */
    public static function conflict($message, $reason = null)
    {
        throw new DeliveryException(
            $message,
            $reason,
            Response::HTTP_CONFLICT
        );
    }

    /**
     * Throw an internal server error delivery exception.
     *
     * @param string $message
     * @param string|null $reason
     * @throws DeliveryException
     */
    public static function internalError($message = 'Internal server error', $reason = null)
    {
        throw new DeliveryException(
            $message,
            $reason,
            Response::HTTP_INTERNAL_SERVER_ERROR
        );
    }
}
