<?php


namespace Marvel\Enums;

use BenSampo\Enum\Enum;

/**
 * Class RoleType
 * @package App\Enums
 */
final class SubscriptionEnum extends Enum
{
    public const SUPER_ADMIN = 'super_admin';
    public const STORE_OWNER = 'store_owner';
    public const STAFF = 'staff';
    public const CUSTOMER = 'customer';
    public const DELIVERY_AGENT = 'delivery_agent';

    // Note: Following the project's pattern, we're using a single permission for the delivery agent role.
    // The specific permissions would be handled through the role assignment rather than individual permissions.
    // If more granular permissions are needed in the future, they can be added here:
    // public const ACCESS_DELIVERY_AGENT_APP = 'access_delivery_agent_app';
    // public const VIEW_ASSIGNED_DELIVERIES = 'view_assigned_deliveries';
    // etc.
}


