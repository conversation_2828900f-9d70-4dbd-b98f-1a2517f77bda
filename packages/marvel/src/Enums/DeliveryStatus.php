<?php

namespace Marvel\Enums;

use BenSamp<PERSON>\Enum\Enum;

/**
 * Class DeliveryStatus
 * @package Marvel\Enums
 */
final class DeliveryStatus extends Enum
{
    public const PENDING_ASSIGNMENT = 'PENDING_ASSIGNMENT';
    public const ASSIGNED = 'ASSIGNED';
    public const REJECTED_BY_AGENT = 'REJECTED_BY_AGENT';
    public const COMPLETED = 'COMPLETED';
    public const CANCELLED = 'CANCELLED';
    public const ACCEPTED_BY_AGENT = 'ACCEPTED_BY_AGENT';
    public const PICKED_UP = 'PICKED_UP';
    public const IN_TRANSIT = 'IN_TRANSIT';
    public const REACHED_DESTINATION = 'REACHED_DESTINATION';
    public const DELIVERED = 'DELIVERED';
    public const FAILED_DELIVERY = 'FAILED_DELIVERY';

}
