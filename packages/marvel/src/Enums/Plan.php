<?php


namespace Marvel\Enums;

use BenSampo\Enum\Enum;

/**
 * Class RoleType
 * @package App\Enums
 */
final class PlanNames extends Enum
{
    public const FREE = 'Free';
    public const STARTER = 'Starter';
    public const GROWTH = 'Growth';
    public const PRO = 'Pro';
    public const ENTERPRISE = 'Enterprise';
    public const PREMIUM = 'Premium';

    // Note: Following the project's pattern, we're using a single permission for the delivery agent role.
    // The specific permissions would be handled through the role assignment rather than individual permissions.
    // If more granular permissions are needed in the future, they can be added here:
    // public const ACCESS_DELIVERY_AGENT_APP = 'access_delivery_agent_app';
    // public const VIEW_ASSIGNED_DELIVERIES = 'view_assigned_deliveries';
    // etc.
}


