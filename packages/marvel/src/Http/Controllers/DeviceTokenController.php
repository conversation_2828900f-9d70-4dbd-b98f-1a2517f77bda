<?php

namespace Marvel\Http\Controllers;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Marvel\Database\Models\DeviceToken;
use Marvel\Database\Repositories\DeviceTokenRepository;
use Marvel\Exceptions\MarvelException;
use Marvel\Http\Requests\DeviceTokenRequest;
use Prettus\Validator\Exceptions\ValidatorException;

class DeviceTokenController extends CoreController
{
    public $repository;

    public function __construct(DeviceTokenRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Register a device token
     *
     * @param DeviceTokenRequest $request
     * @return JsonResponse
     * @throws ValidatorException
     */
    public function store(DeviceTokenRequest $request)
    {
        try {
            $user = $request->user();
            if (!$user) {
                throw new MarvelException('USER_NOT_FOUND');
            }

            $input = $request->only(['token', 'device_type', 'device_id']);
            $input['user_id'] = $user->id;

            // Check if token already exists
            $existingToken = DeviceToken::where('token', $input['token'])->first();
            if ($existingToken) {
                // Update existing token
                $existingToken->update([
                    'user_id' => $user->id,
                    'device_type' => $input['device_type'] ?? $existingToken->device_type,
                    'device_id' => $input['device_id'] ?? $existingToken->device_id,
                    'is_active' => true,
                ]);
                return $this->repository->findWhere(['id' => $existingToken->id]);
            }

            return $this->repository->create($input);
        } catch (\Exception $e) {
            throw new MarvelException(SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Get all device tokens for the authenticated user
     *
     * @param Request $request
     * @return Collection|DeviceToken[]
     */
    public function index(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            throw new MarvelException('USER_NOT_FOUND');
        }

        return $this->repository->findWhere(['user_id' => $user->id]);
    }

    /**
     * Delete a device token
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy($id, Request $request)
    {
        try {
            $user = $request->user();
            if (!$user) {
                throw new MarvelException('USER_NOT_FOUND');
            }

            $token = $this->repository->findOrFail($id);
            if ($token->user_id !== $user->id) {
                throw new MarvelException('NOT_AUTHORIZED');
            }

            return $this->repository->delete($id);
        } catch (\Exception $e) {
            throw new MarvelException(SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Deactivate a device token by token value
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function deactivate(Request $request)
    {
        try {
            $user = $request->user();
            if (!$user) {
                throw new MarvelException('USER_NOT_FOUND');
            }

            $token = $request->input('token');
            if (!$token) {
                throw new MarvelException('TOKEN_REQUIRED');
            }

            $deviceToken = DeviceToken::where('token', $token)
                ->where('user_id', $user->id)
                ->first();

            if (!$deviceToken) {
                throw new MarvelException('TOKEN_NOT_FOUND');
            }

            $deviceToken->update(['is_active' => false]);

            return response()->json(['success' => true, 'message' => 'Token deactivated successfully']);
        } catch (\Exception $e) {
            throw new MarvelException(SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }
}
