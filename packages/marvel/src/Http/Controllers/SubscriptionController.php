<?php

namespace Marvel\Http\Controllers;

use App\Http\Requests\StoreSubscriptionRequest;
use App\Models\ReferralDepositHistory;
use Http\Discovery\Exception\NotFoundException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;
use Marvel\Database\Models\AccountBalance;
use Marvel\Database\Models\Plan;
use Marvel\Database\Models\Product;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\Subscription;
use Illuminate\Http\JsonResponse;
use Marvel\Database\Models\User;
use Marvel\Enums\Permission;
use Marvel\Enums\PlanNames;
use Mollie\Api\Types\SubscriptionStatus;



/**
 * Subscribe to a store plan.
 *
 * @param int $planId
 * @return JsonResponse
 */
class SubscriptionController extends CoreController
{

    public function calCulateReferralPercentage($referrer , $referree,  $plan) {
        // after payment is successful, recharge the account of the referrer and send email
        $percentage = 5; // five percent of vendor subscription for the moment. This is to be declared by admin

        if($plan->name != PlanNames::FREE) {
            $mainPercent = ((int)$plan->price * $percentage) / 100;

            $account = AccountBalance::where('user_id' , $referrer->id)->first();

            if($account) {

                ReferralDepositHistory::create([
                    'account_balance_id' => $account->id,
                    'referree_id' => $referree->id,
                    'amount' => $mainPercent
                ]);

                $account->update([
                    'total_earnings' => (float)$account->total_earnings + $mainPercent,
                    "current_balance" => (float)$account->current_balance + $mainPercent
                ]);
            }
        }
    }
public function subscribe(Request $request, $planId)
{
    $user = $request->user();

    $plan = Plan::findOrFail($planId);

    $referrer = User::where('id' , $user->referred_by)->first();



    if(!$plan) {
        throw new NotFoundException('Plan not found');
    }

    $startDate = now();
    $endDate = $startDate->copy()->addDays(30);

    // Check if the user already has a subscription
    $subscription = Subscription::where('user_id', $user->id)->where('status' , SubscriptionStatus::STATUS_ACTIVE)->where('is_active' , true)->first();

    
    if ($subscription) {
        // Update existing subscription
        $subscription->update([
            'is_active' => false,
            'status' => 'cancelled'
        ]);

        $message = 'Subscription updated successfully';
    }
      // Create new subscription
    $subscription = Subscription::create([
        'user_id' => $user->id,
        'plan_id' => $plan->id,
        'start_date' => $startDate,
        'end_date' => $endDate,
        'is_active' => true,
    ]);

    $message = 'Subscribed successfully';

    if($referrer) {
        $this->calCulateReferralPercentage($referrer, $user, $plan);
    }

    return response()->json([
        'message' => $message,
        'subscription' => $subscription->load('plan')
    ]);
}

    public function getShopCount(Request $request){
        $user = $request->user();

        $shopCount = Shop::where('owner_id' , $user->id)->count();

        return response()->json(['data' => $shopCount]);
    }

    public function getProductCount(Request $request){
        $user = $request->user();

        $ownerId = $user->id;

        $productCount = Product::where('shop_id', function($query) use ($ownerId , $user) { 
            if($user->shop_id) {
                $query->select('id')->from('shops')->where('id', $user->shop_id);
            }else {

                $query->select('id')->from('shops')->where('owner_id', $ownerId);
            }
         })->count();

         return response()->json(['data' => $productCount]);
    }

   public function getActiveSubscription(Request $request)
    {
        $user = $request->user();

        $activeSubscription = null;

         if ($user->hasPermissionTo(Permission::STAFF)) {
            $shop = Shop::where('id' , $user->shop_id)->first();
            $activeSubscription = Subscription::with(['plan', 'user'])
                ->where('user_id', $shop->owner_id)
                ->where('is_active', true)
                ->first();
        }else {
            $activeSubscription = Subscription::with(['plan', 'user'])
                ->where('user_id', $user->id)
                ->where('is_active', true)
                ->first();
        }

        
        return response()->json($activeSubscription);
    }

    public function getSubscriptionPaymentHistory(Request $request){
        $limit = $request->query('limit' , 15);
        $user = $request->user();

        $subscription = Subscription::with('plan')->where('user_id', $user->id)->orderBy('created_at' , 'desc')->paginate($limit);

        return response()->json($subscription);
    }
}
