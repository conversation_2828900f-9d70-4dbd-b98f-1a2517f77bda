<?php


namespace Marvel\Http\Controllers;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Marvel\Database\Models\Category;
use Marvel\Database\Repositories\CategoryRepository;
use Marvel\Exceptions\MarvelException;
use Marvel\Exceptions\DeliveryException;
use Marvel\Helpers\DeliveryExceptionHelper;
use Marvel\Http\Requests\CategoryCreateRequest;
use Marvel\Http\Requests\CategoryUpdateRequest;
use Marvel\Http\Resources\CategoryResource;
use Prettus\Validator\Exceptions\ValidatorException;


class CategoryController extends CoreController
{
    public $repository;

    public function __construct(CategoryRepository $repository)
    {
        $this->repository = $repository;
    }

    // /**
    //  * Display a listing of the resource.
    //  *
    //  * @param Request $request
    //  * @return Collection|Category[]
    //  */
    // public function fetchOnlyParent(Request $request)
    // {
    //     $limit = $request->limit ?   $request->limit : 15;
    //     return $this->repository->withCount(['products'])->with(['type', 'parent', 'children'])->where('parent', null)->paginate($limit);
    //     // $limit = $request->limit ?   $request->limit : 15;
    //     // return $this->repository->withCount(['children', 'products'])->with(['type', 'parent', 'children.type', 'children.children.type', 'children.children' => function ($query) {
    //     //     $query->withCount('products');
    //     // },  'children' => function ($query) {
    //     //     $query->withCount('products');
    //     // }])->where('parent', null)->paginate($limit);
    // }

    // /**
    //  * Display a listing of the resource.
    //  *
    //  * @param Request $request
    //  * @return Collection|Category[]
    //  */
    // public function fetchCategoryRecursively(Request $request)
    // {
    //     $limit = $request->limit ?   $request->limit : 15;
    //     return $this->repository->withCount(['products'])->with(['parent', 'subCategories'])->where('parent', null)->paginate($limit);
    // }
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return Collection|Category[]
     */
    public function index(Request $request)
    {
        $language = $request->language ?? DEFAULT_LANGUAGE;
        $parent = $request->parent;
        $selfId = $request->self ?? null;
        $limit = $request->limit ?? 15;

        $categoriesQuery = $this->repository->with(['type', 'parent', 'children' , 'translations'])
            ->withCount(['products']);

        if ($parent === 'null') {
            $categoriesQuery->whereNull('parent');
        }
        if ($selfId) {
            $categoriesQuery->where('id', '!=', $selfId);
        }

        $categories = $categoriesQuery->paginate($limit);

         $categories->each(function ($category) use ($language) {
            $category->setRelation('translation', $category->translations->firstWhere('locale', $language));
        });

        $data = CategoryResource::collection($categories)->response()->getData(true);
        
        return formatAPIResourcePaginate($data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param CategoryCreateRequest $request
     * @return mixed
     * @throws ValidatorException
     */
    public function store(CategoryCreateRequest $request)
    {
        try {
            return $this->repository->saveCategory($request);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::badRequest(COULD_NOT_CREATE_THE_RESOURCE, $e->getMessage());
        }
        // $language = $request->language ?? DEFAULT_LANGUAGE;
        // $translation_item_id = $request->translation_item_id ?? null;
        // $category->storeTranslation($translation_item_id, $language);
        // return $category;
    }

    /**
     * Display the specified resource.
     *
     * @param string|int $params
     * @return JsonResponse
     */
    public function show(Request $request, $params)
    {
        try {
            $language = $request->language ?? DEFAULT_LANGUAGE;
            if (is_numeric($params)) {
                $params = (int) $params;
                $category = $this->repository->with(['type', 'parentCategory', 'children'])->where('id', $params)->firstOrFail();
                return new CategoryResource($category);
            }
            $category = $this->repository->with(['type', 'parentCategory', 'children'])->where('slug', $params)->firstOrFail();
            return new CategoryResource($category);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::notFound(NOT_FOUND, $e->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param CategoryUpdateRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(CategoryUpdateRequest $request, $id)
    {
        try {
            $request->merge(['id' => $id]);
            return $this->categoryUpdate($request);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::notFound(NOT_FOUND, $e->getMessage());
        }
    }


    public function categoryUpdate(CategoryUpdateRequest $request)
    {
        $category = $this->repository->findOrFail($request->id);
        return $this->repository->updateCategory($request, $category);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy($id)
    {
        try {
            return $this->repository->findOrFail($id)->delete();
        } catch (\Exception $e) {
            DeliveryExceptionHelper::notFound(NOT_FOUND, $e->getMessage());
        }
    }

    /**
     * fetchFeaturedCategories -- Chawkbazar specific here
     *
     * @param  mixed $request
     * @return void
     */
    public function fetchFeaturedCategories(Request $request)
    {
        //        $limit = isset($request->limit) ? $request->limit : 3;
        //        return $this->repository->with(['products'])->take($limit)->get()->map(function ($category) {
        //            $category->setRelation('products', $category->products->withCount('orders')->sortBy('orders_count', "desc")->take(3));
        //            return $category;
        //        });
        return $this->repository->with(['products'])->limit(3);
    }

    /**
     * Get recommended categories for a user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function recommendedCategories(Request $request)
    {
        $limit = $request->limit ?? 5;
        $language = $request->language ?? DEFAULT_LANGUAGE;
        $user = $request->user();

        // For non-logged in users or as a fallback, use popular categories with pagination
        if (!$user) {
            $categories = $this->repository->getPopularCategories($limit, $language, true);
            $data = CategoryResource::collection($categories)->response()->getData(true);
            return formatAPIResourcePaginate($data);
        }

        // For logged-in users, try to get personalized recommendations
        // Get categories from user's order history
        $userOrderCategories = $this->repository->getCategoriesFromUserOrders($user->id, $limit, $language);

        // If we have enough categories from order history, use them with pagination
        if ($userOrderCategories->count() >= $limit) {
            // Use user's order history categories with pagination
            $categories = $this->repository->getCategoriesFromUserOrders($user->id, $limit, $language, true);
        } else {
            // Otherwise, just use popular categories with pagination
            $categories = $this->repository->getPopularCategories($limit, $language, true);
        }

        $data = CategoryResource::collection($categories)->response()->getData(true);
        return formatAPIResourcePaginate($data);
    }

    /**
     * Get the three most trending categories
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function trendingCategories(Request $request)
    {
        $limit = $request->limit ?? 3;
        $language = $request->language ?? DEFAULT_LANGUAGE;
        $days = $request->days ?? 30; // Default to last 30 days

        $categories = $this->repository->getTrendingCategories($limit, $days, $language, true);
        $data = CategoryResource::collection($categories)->response()->getData(true);
        return formatAPIResourcePaginate($data);
    }

    /**
     * Get the most popular categories
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function popularCategories(Request $request)
    {
        $limit = $request->limit ?? 10;
        $language = $request->language ?? DEFAULT_LANGUAGE;

        $categories = $this->repository->getPopularCategories($limit, $language, true);
        $data = CategoryResource::collection($categories)->response()->getData(true);
        return formatAPIResourcePaginate($data);
    }

    /**
     * Get all parent categories (paginated)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function parentCategories(Request $request)
    {
        $language = $request->language ?? DEFAULT_LANGUAGE;
        $limit = $request->limit ?? 15;

        $categoriesQuery = $this->repository->with(['type', 'children'])
            ->where('language', $language)
            ->whereNull('parent')
            ->has('children')
            ->withCount(['products']);

        $categories = $categoriesQuery->paginate($limit);
        $data = CategoryResource::collection($categories)->response()->getData(true);
        return formatAPIResourcePaginate($data);
    }

    /**
     * Get child categories for a specific parent category
     *
     * @param Request $request
     * @param int $parentId
     * @return JsonResponse
     */
    public function childCategories(Request $request, $parentId)
    {
        try {
            $language = $request->language ?? DEFAULT_LANGUAGE;
            $limit = $request->limit ?? 15;

            $categoriesQuery = $this->repository->with(['type', 'parent', 'children'])
                ->where('language', $language)
                ->where('parent', $parentId)
                ->withCount(['products']);

            $categories = $categoriesQuery->paginate($limit);
            $data = CategoryResource::collection($categories)->response()->getData(true);
            return formatAPIResourcePaginate($data);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::notFound(NOT_FOUND, $e->getMessage());
        }
    }
}
