<?php

namespace  Marvel\Http\Controllers;

use App\Models\ReferralDepositHistory;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;
use Marvel\Database\Models\AccountBalance;

class ReferralController extends CoreController
{
    public function getReferralBalance(Request $request){
        $user = $request->user();

        if(!$user) {
            throw new UnauthorizedException("You are not allowed to access this ressource");
        }

        $account = AccountBalance::where("user_id", $user->id)->first();

        $referrals = ReferralDepositHistory::where('account_balance_id' , $account->id)->distinct('referree_id')->count();

        return response()->json([
            'account' => $account,
            'total_referred' => $referrals,
        ]);
    }

    public function getReferralList(Request $request){
        $user = $request->user();

        $limit = $request->query('limit' , 15);

        $account = AccountBalance::where("user_id", $user->id)->first();

        $query = ReferralDepositHistory::with('referree')->where("account_balance_id", $account->id);


        $referrals = $query->orderBy('created_at' , 'desc')->paginate($limit);

        return response()->json($referrals);
    }
}
