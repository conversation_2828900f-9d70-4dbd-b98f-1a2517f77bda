<?php

namespace Marvel\Http\Controllers\Agent;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Marvel\Database\Models\User;
use Marvel\Database\Models\Vehicle;
use Marvel\Database\Repositories\VehicleRepository;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Requests\VehicleCreateRequest;
use Marvel\Http\Requests\VehicleUpdateRequest;
use Marvel\Http\Resources\VehicleResource;
use Marvel\Http\Resources\DeliveryAgentProfileResource;
use Marvel\Enums\Permission;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;
use Marvel\Exceptions\DeliveryException; // Keep for type hinting
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

class VehicleController extends CoreController
{
    public $repository;

    public function __construct(VehicleRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Display a listing of the vehicles.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $vehicles = $this->repository->getVehiclesByDeliveryAgentId($user->id);

            return response()->json([
                'success' => true,
                'data' => VehicleResource::collection($vehicles)
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve vehicles', $e->getMessage());
        }
    }

    /**
     * Store a newly created vehicle in storage.
     *
     * @param VehicleCreateRequest $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function store(VehicleCreateRequest $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $data = $request->validated();
            $data['delivery_agent_user_id'] = $user->id;

            $vehicle = $this->repository->storeVehicle($data);

            // Handle vehicle document uploads
            if ($request->hasFile('vehicle_registration')) {
                $vehicle->addMedia($request->file('vehicle_registration'))->toMediaCollection('vehicle_registration');
            }

            if ($request->hasFile('vehicle_insurance')) {
                $vehicle->addMedia($request->file('vehicle_insurance'))->toMediaCollection('vehicle_insurance');
            }

            if ($request->hasFile('vehicle_images')) {
                foreach ($request->file('vehicle_images') as $image) {
                    $vehicle->addMedia($image)->toMediaCollection('vehicle_images');
                }
            }

            // If this is the first vehicle, set it as active
            $profile = $user->delivery_agent_profile;
            if ($profile && !$profile->active_vehicle_id) {
                $profile->active_vehicle_id = $vehicle->id;
                $profile->save();
            }

            return response()->json([
                'success' => true,
                'message' => 'Vehicle created successfully',
                'data' => new VehicleResource($vehicle)
            ], 201);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error($e);
            DeliveryExceptionHelper::internalError('Failed to create vehicle', $e->getMessage());
        }
    }

    /**
     * Display the specified vehicle.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function show(Request $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $vehicle = $this->repository->findOrFail($id);

            // Check if the vehicle belongs to the user
            if ($vehicle->delivery_agent_user_id != $user->id) {
                DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
            }

            return response()->json([
                'success' => true,
                'data' => new VehicleResource($vehicle)
            ]);
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::VEHICLE_NOT_FOUND);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve vehicle', $e->getMessage());
        }
    }

    /**
     * Update the specified vehicle in storage.
     *
     * @param VehicleUpdateRequest $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function update(VehicleUpdateRequest $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $vehicle = $this->repository->findOrFail($id);

            // Check if the vehicle belongs to the user
            if ($vehicle->delivery_agent_user_id != $user->id) {
                DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
            }

            $data = $request->validated();

            // Handle vehicle document uploads
            if ($request->hasFile('vehicle_registration')) {
                $vehicle->addMedia($request->file('vehicle_registration'))->toMediaCollection('vehicle_registration');
            }

            if ($request->hasFile('vehicle_insurance')) {
                $vehicle->addMedia($request->file('vehicle_insurance'))->toMediaCollection('vehicle_insurance');
            }

            if ($request->hasFile('vehicle_images')) {
                foreach ($request->file('vehicle_images') as $image) {
                    $vehicle->addMedia($image)->toMediaCollection('vehicle_images');
                }
            }

            $vehicle = $this->repository->updateVehicle($data, $id);

            return response()->json([
                'success' => true,
                'message' => 'Vehicle updated successfully',
                'data' => new VehicleResource($vehicle)
            ]);
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::VEHICLE_NOT_FOUND);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to update vehicle', $e->getMessage());
        }
    }

    /**
     * Remove the specified vehicle from storage.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $vehicle = $this->repository->findOrFail($id);

            // Check if the vehicle belongs to the user
            if ($vehicle->delivery_agent_user_id != $user->id) {
                DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
            }

            // Check if this is the active vehicle
            $profile = $user->delivery_agent_profile;
            if ($profile && $profile->active_vehicle_id == $id) {
                DeliveryExceptionHelper::badRequest(
                    DeliveryConstants::CANNOT_DELETE_ACTIVE_VEHICLE,
                    'You cannot delete your active vehicle. Please set another vehicle as active first.'
                );
            }

            $this->repository->delete($id);

            return response()->json([
                'success' => true,
                'message' => DeliveryConstants::ITEM_DELETED
            ]);
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::VEHICLE_NOT_FOUND);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to delete vehicle', $e->getMessage());
        }
    }

    /**
     * Set the specified vehicle as active.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function setActive(Request $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $profile = $this->repository->setActiveVehicle($id, $user->id);

            // Load relationships
            $profile->load(['user', 'activeVehicle']);

            return response()->json([
                'success' => true,
                'message' => DeliveryConstants::VEHICLE_SET_AS_ACTIVE,
                'data' => new DeliveryAgentProfileResource($profile)
            ]);
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::VEHICLE_NOT_FOUND);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to set active vehicle', $e->getMessage());
        }
    }
}
