<?php

namespace Marvel\Http\Controllers\Agent;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Marvel\Database\Models\User;
use Marvel\Database\Models\DeliveryAgentLocationHistory;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Requests\LocationUpdateRequest;
use Marvel\Enums\Permission;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Exceptions\DeliveryException;
use Marvel\Helpers\DeliveryExceptionHelper;
use Marvel\Events\AgentLocationUpdated;
use Carbon\Carbon;

class LocationController extends CoreController
{
    /**
     * Update the delivery agent's current location.
     * Authorization is handled by the LocationUpdateRequest.
     *
     * @param LocationUpdateRequest $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function update(LocationUpdateRequest $request): JsonResponse
    {
        $user = $request->user();

        try {
            $profile = $user->delivery_agent_profile;

            if (!$profile) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::AGENT_PROFILE_NOT_FOUND);
            }

            // Check if agent is online (only online agents should update location)
            if ($profile->availability_status !== 'ONLINE') {
                DeliveryExceptionHelper::badRequest(
                    'Location update not allowed',
                    'Only online agents can update their location'
                );
            }

            $data = $request->validated();
            $timestamp = $data['timestamp'] ? Carbon::parse($data['timestamp']) : now();

            // Prepare location data
            $locationData = [
                'lat' => (float) $data['lat'],
                'lng' => (float) $data['lng'],
                'timestamp' => $timestamp->toISOString(),
            ];

            // Add optional GPS metadata if provided
            if (isset($data['accuracy'])) {
                $locationData['accuracy'] = (float) $data['accuracy'];
            }
            if (isset($data['speed'])) {
                $locationData['speed'] = (float) $data['speed'];
            }
            if (isset($data['heading'])) {
                $locationData['heading'] = (float) $data['heading'];
            }

            // Check if location has significantly changed to avoid spam updates
            $updateThreshold = config('delivery.location_update_threshold_meters', 10);
            $hasLocationChanged = $this->hasLocationChanged(
                $profile->current_location,
                $locationData,
                $updateThreshold
            );

            if ($hasLocationChanged) {
                // Update agent's current location in profile
                $profile->current_location = $locationData;
                $profile->last_check_in_at = $timestamp;
                $profile->save();

                // Save to location history for tracking
                $this->saveLocationHistory($user->id, $locationData);

                // Broadcast location update event for real-time tracking
                event(new AgentLocationUpdated($user, $locationData));

                return response()->json([
                    'success' => true,
                    'message' => 'Location updated successfully',
                    'data' => [
                        'current_location' => $locationData,
                        'last_check_in_at' => $timestamp->toISOString(),
                    ]
                ]);
            } else {
                // Location hasn't changed significantly, just update timestamp
                $profile->last_check_in_at = $timestamp;
                $profile->save();

                return response()->json([
                    'success' => true,
                    'message' => 'Check-in updated',
                    'data' => [
                        'current_location' => $profile->current_location,
                        'last_check_in_at' => $timestamp->toISOString(),
                    ]
                ]);
            }
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError(
                'Failed to update location',
                $e->getMessage()
            );
        }
    }

    /**
     * Get the delivery agent's current location.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function show(Request $request): JsonResponse
    {
        $user = $request->user();

        // Authorization check is required here as we are not using a FormRequest
        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $profile = $user->delivery_agent_profile;

            if (!$profile) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::AGENT_PROFILE_NOT_FOUND);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'current_location' => $profile->current_location,
                    'availability_status' => $profile->availability_status,
                    'last_check_in_at' => $profile->last_check_in_at?->toISOString(),
                ]
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError(
                'Failed to retrieve location',
                $e->getMessage()
            );
        }
    }

    /**
     * Check if location has changed significantly.
     *
     * @param array|null $oldLocation
     * @param array $newLocation
     * @param float $thresholdMeters
     * @return bool
     */
    private function hasLocationChanged($oldLocation, array $newLocation, float $thresholdMeters = 10): bool
    {
        if (!$oldLocation || !isset($oldLocation['lat']) || !isset($oldLocation['lng'])) {
            return true; // No previous location, so it's a change
        }

        $distance = $this->calculateDistance(
            (float) $oldLocation['lat'],
            (float) $oldLocation['lng'],
            (float) $newLocation['lat'],
            (float) $newLocation['lng']
        );

        // Convert km to meters and check threshold
        return ($distance * 1000) > $thresholdMeters;
    }

    /**
     * Calculate distance between two points using Haversine formula.
     *
     * @param float $lat1
     * @param float $lng1
     * @param float $lat2
     * @param float $lng2
     * @return float Distance in kilometers
     */
    private function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $lat1Rad = deg2rad($lat1);
        $lng1Rad = deg2rad($lng1);
        $lat2Rad = deg2rad($lat2);
        $lng2Rad = deg2rad($lng2);

        $deltaLat = $lat2Rad - $lat1Rad;
        $deltaLng = $lng2Rad - $lng1Rad;

        $a = sin($deltaLat / 2) * sin($deltaLat / 2) +
             cos($lat1Rad) * cos($lat2Rad) *
             sin($deltaLng / 2) * sin($deltaLng / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Save location to history for tracking purposes.
     *
     * @param int $agentUserId
     * @param array $locationData
     * @return void
     */
    private function saveLocationHistory(int $agentUserId, array $locationData): void
    {
        try {
            // Only save if location is significantly different from last entry
            $lastEntry = DeliveryAgentLocationHistory::where('delivery_agent_user_id', $agentUserId)
                ->orderBy('created_at', 'desc')
                ->first();

            $shouldSave = true;
            if ($lastEntry && $lastEntry->location) {
                $distance = $this->calculateDistance(
                    (float) $lastEntry->location['lat'],
                    (float) $lastEntry->location['lng'],
                    (float) $locationData['lat'],
                    (float) $locationData['lng']
                );

                // Get thresholds from config file, with sensible defaults
                $distanceThreshold = config('delivery.history_save_threshold_meters', 50);
                $timeThreshold = config('delivery.history_save_threshold_minutes', 5);

                // Only save if moved more than threshold distance or more than threshold time has passed
                $timeDiff = now()->diffInMinutes($lastEntry->created_at);
                $shouldSave = ($distance * 1000) > $distanceThreshold || $timeDiff > $timeThreshold;
            }

            if ($shouldSave) {
                DeliveryAgentLocationHistory::create([
                    'delivery_agent_user_id' => $agentUserId,
                    'location' => $locationData,
                    'timestamp' => $locationData['timestamp'],
                    'delivery_id' => null, // Not associated with a specific delivery
                    'created_at' => now(),
                ]);
            }
        } catch (\Exception $e) {
            // Log error but don't fail the location update
            \Log::error('Failed to save location history: ' . $e->getMessage());
        }
    }
}