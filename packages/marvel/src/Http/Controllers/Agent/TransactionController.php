<?php

namespace Marvel\Http\Controllers\Agent;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Marvel\Database\Models\DeliveryAgentTransaction;
use Marvel\Database\Repositories\DeliveryAgentTransactionRepository;
use Marvel\Exceptions\DeliveryException;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Resources\DeliveryAgentTransactionResource;
use Marvel\Enums\Permission;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;

class TransactionController extends CoreController
{
    public $repository;

    public function __construct(DeliveryAgentTransactionRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Display a listing of the transactions.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $params = [
                'transaction_type' => $request->query('transaction_type'),
                'status' => $request->query('status'),
                'date_from' => $request->query('date_from'),
                'date_to' => $request->query('date_to'),
                'limit' => $request->query('limit', 15),
            ];

            $transactions = $this->repository->getTransactionsForAgent($user, $params);

            return response()->json([
                'success' => true,
                'data' => DeliveryAgentTransactionResource::collection($transactions),
                'pagination' => [
                    'total' => $transactions->total(),
                    'per_page' => $transactions->perPage(),
                    'current_page' => $transactions->currentPage(),
                    'last_page' => $transactions->lastPage()
                ]
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve transactions', $e->getMessage());
        }
    }

    /**
     * Display the specified transaction.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function show(Request $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $transaction = DeliveryAgentTransaction::where('delivery_agent_user_id', $user->id)
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => new DeliveryAgentTransactionResource($transaction)
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound('Transaction not found');
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve transaction', $e->getMessage());
        }
    }
}
