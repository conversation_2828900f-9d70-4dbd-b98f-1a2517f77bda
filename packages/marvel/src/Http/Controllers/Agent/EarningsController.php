<?php

namespace Marvel\Http\Controllers\Agent;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Marvel\Database\Repositories\DeliveryAgentEarningRepository;
use Marvel\Database\Repositories\DeliveryAgentWithdrawalRepository;
use Marvel\Exceptions\DeliveryException;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Requests\PaymentInfoRequest;
use Marvel\Http\Resources\DeliveryAgentEarningResource;
use Marvel\Http\Resources\DeliveryResource;
use Marvel\Http\Resources\DeliveryAgentWithdrawalResource;
use Marvel\Enums\Permission;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;

class EarningsController extends CoreController
{
    public $earningRepository;
    public $withdrawalRepository;

    public function __construct(
        DeliveryAgentEarningRepository $earningRepository,
        DeliveryAgentWithdrawalRepository $withdrawalRepository
    ) {
        $this->earningRepository = $earningRepository;
        $this->withdrawalRepository = $withdrawalRepository;
    }

    /**
     * Display the agent's current balance.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function balance(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $earnings = $this->earningRepository->getEarningsForAgent($user);

            return response()->json([
                'success' => true,
                'data' => new DeliveryAgentEarningResource($earnings)
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve balance', $e->getMessage());
        }
    }

    /**
     * Display the agent's earnings history.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function history(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $params = [
                'date_from' => $request->query('date_from'),
                'date_to' => $request->query('date_to'),
                'limit' => $request->query('limit', 15),
            ];

            $deliveries = $this->earningRepository->getEarningsHistory($user, $params);

            // Load relationships
            $deliveries->load(['order']);

            return response()->json([
                'success' => true,
                'data' => DeliveryResource::collection($deliveries),
                'pagination' => [
                    'total' => $deliveries->total(),
                    'per_page' => $deliveries->perPage(),
                    'current_page' => $deliveries->currentPage(),
                    'last_page' => $deliveries->lastPage()
                ]
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve earnings history', $e->getMessage());
        }
    }

    /**
     * Display the agent's withdrawal history.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function withdrawals(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $params = [
                'status' => $request->query('status'),
                'limit' => $request->query('limit', 15),
            ];

            $withdrawals = $this->withdrawalRepository->getWithdrawalsForAgent($user, $params);

            return response()->json([
                'success' => true,
                'data' => DeliveryAgentWithdrawalResource::collection($withdrawals),
                'pagination' => [
                    'total' => $withdrawals->total(),
                    'per_page' => $withdrawals->perPage(),
                    'current_page' => $withdrawals->currentPage(),
                    'last_page' => $withdrawals->lastPage()
                ]
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve withdrawals', $e->getMessage());
        }
    }

    /**
     * Display the agent's payment info.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function paymentInfo(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $earnings = $this->earningRepository->getEarningsForAgent($user);

            return response()->json([
                'success' => true,
                'data' => [
                    'payment_info' => $earnings->payment_info,
                ]
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve payment info', $e->getMessage());
        }
    }

    /**
     * Update the agent's payment info.
     *
     * @param PaymentInfoRequest $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function updatePaymentInfo(PaymentInfoRequest $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $data = $request->validated();

            $earnings = $this->earningRepository->updatePaymentInfo($data, $user);

            return response()->json([
                'success' => true,
                'message' => DeliveryConstants::PAYMENT_INFO_UPDATED,
                'data' => [
                    'payment_info' => $earnings->payment_info,
                ]
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to update payment info', $e->getMessage());
        }
    }
}
