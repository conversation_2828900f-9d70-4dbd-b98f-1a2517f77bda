<?php

namespace Marvel\Http\Controllers\Agent;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Repositories\DeliveryRepository;
use Marvel\Exceptions\DeliveryException;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Resources\DeliveryResource;
use Marvel\Enums\Permission;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;

class DeliveryController extends CoreController
{
    public $repository;

    public function __construct(DeliveryRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Display a listing of the deliveries assigned to the agent.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $params = [
                'status' => $request->query('status'),
                'date_from' => $request->query('date_from'),
                'date_to' => $request->query('date_to'),
                'limit' => $request->query('limit', 15),
            ];

            $deliveries = $this->repository->getDeliveriesForAgent($user, $params);

            // Load relationships
            $deliveries->load([ 'assignedBy']);

            return response()->json([
                'success' => true,
                'data' => DeliveryResource::collection($deliveries),
                'pagination' => [
                    'total' => $deliveries->total(),
                    'per_page' => $deliveries->perPage(),
                    'current_page' => $deliveries->currentPage(),
                    'last_page' => $deliveries->lastPage()
                ]
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve deliveries', $e->getMessage());
        }
    }

    /**
     * Display the specified delivery.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function show(Request $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $delivery = Delivery::with(['order.shop', 'order.customer', 'assignedBy', 'statusLogs.user'])
                ->where('delivery_agent_user_id', $user->id)
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => new DeliveryResource($delivery)
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound('Delivery not found or not assigned to you');
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve delivery', $e->getMessage());
        }
    }

    /**
     * Accept a delivery.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function accept(Request $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $delivery = Delivery::where('delivery_agent_user_id', $user->id)
                ->where('status', 'ASSIGNED')
                ->findOrFail($id);

            $data = [
                'notes' => $request->input('notes'),
                'location' => $request->input('location'),
            ];

            $delivery = $this->repository->updateDeliveryStatus($delivery, 'ACCEPTED_BY_AGENT', $user, $data);

            return response()->json([
                'success' => true,
                'message' => DeliveryConstants::DELIVERY_STATUS_UPDATED,
                'data' => new DeliveryResource($delivery->load(['order', 'assignedBy']))
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound('Delivery not found or not in the correct status');
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to accept delivery', $e->getMessage());
        }
    }

    /**
     * Reject a delivery.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function reject(Request $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $delivery = Delivery::where('delivery_agent_user_id', $user->id)
                ->where('status', 'ASSIGNED')
                ->findOrFail($id);

            $request->validate([
                'reason' => 'required|string',
            ]);

            if (empty($request->input('reason'))) {
                DeliveryExceptionHelper::badRequest(
                    'Reason is required',
                    'You must provide a reason when rejecting a delivery'
                );
            }

            $data = [
                'reason' => $request->input('reason'),
                'notes' => $request->input('notes'),
                'location' => $request->input('location'),
            ];

            $delivery = $this->repository->updateDeliveryStatus($delivery, 'REJECTED_BY_AGENT', $user, $data);

            return response()->json([
                'success' => true,
                'message' => DeliveryConstants::DELIVERY_STATUS_UPDATED,
                'data' => new DeliveryResource($delivery->load(['order', 'assignedBy']))
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound('Delivery not found or not in the correct status');
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to reject delivery', $e->getMessage());
        }
    }
}
