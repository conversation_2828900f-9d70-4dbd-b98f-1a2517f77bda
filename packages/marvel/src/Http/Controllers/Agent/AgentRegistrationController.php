<?php

namespace Marvel\Http\Controllers\Agent;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Notification;
use Marvel\Database\Models\User;
use Marvel\Database\Models\DeliveryAgentProfile;
use Marvel\Enums\Role as UserRole;
use Marvel\Enums\Permission as UserPermission;
use Marvel\Exceptions\DeliveryException;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Requests\AgentRegisterRequest;
use Marvel\Http\Requests\AgentRegisterInitialRequest;
use Marvel\Http\Requests\AgentVerifyOtpRequest;
use Marvel\Database\Models\Settings;
use Marvel\Helpers\DeliveryExceptionHelper;
use Marvel\Notifications\AgentRegistrationOtpNotification;
use Marvel\Traits\WalletsTrait;
use <PERSON><PERSON>\Permission\PermissionRegistrar;

class AgentRegistrationController extends CoreController
{
    use WalletsTrait;

    /**
     * Generate a random 6-digit OTP code
     *
     * @return string
     */
    private function generateOtpCode(): string
    {
        return str_pad((string)random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }

    /**
     * Initiate the registration process by sending OTP to email
     *
     * @param AgentRegisterInitialRequest $request
     * @return JsonResponse
     */
    public function initiateRegistration(AgentRegisterInitialRequest $request): JsonResponse
    {
        $validatedData = $request->validated();

        try {
            // Check if email already exists
            if (User::where('email', $validatedData['email'])->exists()) {
                DeliveryExceptionHelper::conflict(
                    'Email already exists',
                    'A user with this email address already exists'
                );
            }

            // Check if phone number already exists
            if (DeliveryAgentProfile::where('phone_number', $validatedData['phone_number'])->exists()) {
                DeliveryExceptionHelper::conflict(
                    'Phone number already exists',
                    'A delivery agent with this phone number already exists'
                );
            }

            // Generate OTP code
            $otpCode = $this->generateOtpCode();

            // Store registration data and OTP in cache
            // Cache key is email to ensure uniqueness
            $cacheKey = 'agent_registration_' . $validatedData['email'];
            $cacheData = [
                'name' => $validatedData['name'],
                'email' => $validatedData['email'],
                'password' => $validatedData['password'],
                'phone_number' => $validatedData['phone_number'],
                'otp_code' => $otpCode,
                'created_at' => now()->timestamp
            ];

            // Cache for 10 minutes
            Cache::put($cacheKey, $cacheData, now()->addMinutes(10));

            // Send OTP via email
            Notification::route('mail', $validatedData['email'])
                ->notify(new AgentRegistrationOtpNotification($otpCode, $validatedData));

            return response()->json([
                'success' => true,
                'message' => 'OTP sent to your email. Please verify to complete registration.',
                'data' => [
                    'email' => $validatedData['email']
                ]
            ]);
        } catch (DeliveryException $e) {
            // Re-throw DeliveryException
            throw $e;
        } catch (\Exception $e) {
            Log::error('Agent Registration Initiation Failed: ' . $e->getMessage() . ' at ' . $e->getFile() . ':' . $e->getLine());
            DeliveryExceptionHelper::internalError(
                'Could not initiate delivery agent registration',
                $e->getMessage()
            );
        }
    }

    /**
     * Verify OTP and complete registration
     *
     * @param AgentVerifyOtpRequest $request
     * @return JsonResponse
     */
    public function verifyAndRegister(AgentVerifyOtpRequest $request): JsonResponse
    {
        $validatedData = $request->validated();
        $user = null;

        try {
            // Get cached registration data
            $cacheKey = 'agent_registration_' . $validatedData['email'];
            $registrationData = Cache::get($cacheKey);

            // Check if registration data exists
            if (!$registrationData) {
                DeliveryExceptionHelper::badRequest(
                    'Invalid or expired registration session',
                    'Please restart the registration process'
                );
            }

            // Check if OTP is valid
            if ($registrationData['otp_code'] !== $validatedData['otp_code']) {
                DeliveryExceptionHelper::badRequest(
                    'Invalid OTP code',
                    'The OTP code you entered is incorrect'
                );
            }

            // Check if OTP is expired (10 minutes)
            $otpCreatedAt = $registrationData['created_at'];
            if (now()->timestamp - $otpCreatedAt > 600) {
                // Remove expired cache entry
                Cache::forget($cacheKey);

                DeliveryExceptionHelper::badRequest(
                    'OTP expired',
                    'Your OTP has expired. Please restart the registration process'
                );
            }

            // Create user and profile in a transaction
            DB::transaction(function () use ($registrationData, &$user) {
                // 1. Create User
                $user = User::create([
                    'name'     => $registrationData['name'],
                    'email'    => $registrationData['email'],
                    'password' => Hash::make($registrationData['password']),
                    'is_active' => true,
                ]);
                $user->email_verified_at = now(); // Set email as verified
                $user->save();

                // 2. Assign Roles
                $user->assignRole(UserRole::DELIVERY_AGENT);
                $user->assignRole(UserRole::CUSTOMER);

                // 3. Clear Spatie Cache INSIDE transaction
                app()[PermissionRegistrar::class]->forgetCachedPermissions();

                // 4. Create Delivery Agent Profile
                $user->delivery_agent_profile()->create([
                    'phone_number' => $registrationData['phone_number'],
                    'availability_status' => 'OFFLINE',
                    'kyc_status' => 'PENDING',
                ]);

                // 5. Give Signup Points (Optional)
                // $this->giveSignupPointsToCustomer($user->id);

            });

            if (!$user) {
                DeliveryExceptionHelper::internalError(
                    'User creation failed',
                    'Failed to create user within transaction'
                );
            }

            // Remove cache entry after successful registration
            Cache::forget($cacheKey);

            // 6. Clear cache AGAIN before retrieving for response
            app()[PermissionRegistrar::class]->forgetCachedPermissions();

            // 8. Create Auth Token
            $token = $user->createToken('agent_auth_token')->plainTextToken;

            // 9. Get fresh user instance with roles and permissions
            $user = User::with('roles', 'permissions')->find($user->id);

            // 10. Get role/permission names
            $roles = $user->getRoleNames();

            // 11. Get all permissions including those inherited from roles
            $permissions = $user->getAllPermissions()->pluck('name');

            // Determine primary role
            $primaryRole = $roles->contains(UserRole::DELIVERY_AGENT) ? UserRole::DELIVERY_AGENT : $roles->first();

            // 12. Return Response
            return response()->json([
                'success' => true,
                'message' => 'Delivery agent registered successfully.',
                'data' => [
                    'token' => $token,
                    'permissions' => $permissions,
                    'role' => $primaryRole,
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email
                    ]
                ]
            ], 201);
        } catch (DeliveryException $e) {
            // Re-throw DeliveryException
            throw $e;
        } catch (\Exception $e) {
            Log::error('Agent Registration Failed: ' . $e->getMessage() . ' at ' . $e->getFile() . ':' . $e->getLine());
            DeliveryExceptionHelper::internalError(
                'Could not register the delivery agent',
                $e->getMessage()
            );
        }
    }

    /**
     * Legacy registration method (without OTP)
     *
     * @param AgentRegisterRequest $request
     * @return JsonResponse
     */
    public function register(AgentRegisterRequest $request): JsonResponse
    {
        $validatedData = $request->validated();
        $user = null;

        try {
            DB::transaction(function () use ($validatedData, &$user) {
                // Check if email already exists
                if (User::where('email', $validatedData['email'])->exists()) {
                    DeliveryExceptionHelper::conflict(
                        'Email already exists',
                        'A user with this email address already exists'
                    );
                }

                // 1. Create User
                $user = User::create([
                    'name'     => $validatedData['name'],
                    'email'    => $validatedData['email'],
                    'password' => Hash::make($validatedData['password']),
                    'is_active' => true,
                ]);
                $user->email_verified_at = now(); // Set email as verified
                $user->save();

                // 2. Assign Roles
                $user->assignRole(UserRole::DELIVERY_AGENT);
                $user->assignRole(UserRole::CUSTOMER);

                // 3. Clear Spatie Cache INSIDE transaction
                app()[PermissionRegistrar::class]->forgetCachedPermissions();

                // 4. Create Delivery Agent Profile
                $user->delivery_agent_profile()->create([
                    'phone_number' => $validatedData['phone_number'],
                    'availability_status' => 'OFFLINE',
                    'kyc_status' => 'PENDING',
                ]);

                // 5. Give Signup Points (Optional)
                // $this->giveSignupPointsToCustomer($user->id);
            });

            if (!$user) {
                DeliveryExceptionHelper::internalError(
                    'User creation failed',
                    'Failed to create user within transaction'
                );
            }

            // 6. Clear cache AGAIN before retrieving for response
            app()[PermissionRegistrar::class]->forgetCachedPermissions();

            // 8. Create Auth Token
            $token = $user->createToken('agent_auth_token')->plainTextToken;

            // 9. Get fresh user instance with roles and permissions
            $user = User::with('roles', 'permissions')->find($user->id);

            // 10. Get role/permission names
            $roles = $user->getRoleNames();

            // 11. Get all permissions including those inherited from roles
            $permissions = $user->getAllPermissions()->pluck('name');

            // Determine primary role
            $primaryRole = $roles->contains(UserRole::DELIVERY_AGENT) ? UserRole::DELIVERY_AGENT : $roles->first();

            // 12. Return Response
            return response()->json([
                'success' => true,
                'message' => 'Delivery agent registered successfully.',
                'data' => [
                    'token' => $token,
                    'permissions' => $permissions,
                    'role' => $primaryRole,
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email
                    ]
                ]
            ], 201);
        } catch (DeliveryException $e) {
            // Re-throw DeliveryException
            throw $e;
        } catch (\Exception $e) {
            Log::error('Agent Registration Failed: ' . $e->getMessage() . ' at ' . $e->getFile() . ':' . $e->getLine());
            DeliveryExceptionHelper::internalError(
                'Could not register the delivery agent',
                $e->getMessage()
            );
        }
    }
}
