<?php

namespace Marvel\Http\Controllers\Agent;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Marvel\Database\Models\User;
use Marvel\Database\Models\DeliveryAgentProfile;
use Marvel\Database\Repositories\DeliveryAgentProfileRepository;
use Marvel\Exceptions\DeliveryException;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Requests\DeliveryAgentProfileUpdateRequest;
use Marvel\Http\Resources\DeliveryAgentProfileResource;
use Marvel\Http\Resources\WalletResource;
use Marvel\Database\Models\Wallet;
use Marvel\Enums\Permission;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;
use Illuminate\Database\Eloquent\ModelNotFoundException; // Although not directly used, good practice if repository throws it
use Illuminate\Support\Facades\Log;
use Marvel\Traits\WalletsTrait;

class ProfileController extends CoreController
{
    use WalletsTrait;
    public $repository;

    public function __construct(DeliveryAgentProfileRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Display the delivery agent profile.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function show(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $profile = $user->delivery_agent_profile;

            if (!$profile) {
                // Create a profile if it doesn't exist
                $profile = DeliveryAgentProfile::create([
                    'user_id' => $user->id,
                    'availability_status' => 'OFFLINE',
                    'kyc_status' => 'PENDING',
                ]);
            }

            // Load relationships
            $profile->load(['user', 'activeVehicle']);

            return response()->json([
                'success' => true,
                'data' => new DeliveryAgentProfileResource($profile)
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve profile', $e->getMessage());
        }
    }

    /**
     * Update the delivery agent profile.
     *
     * @param DeliveryAgentProfileUpdateRequest $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function update(DeliveryAgentProfileUpdateRequest $request): JsonResponse
    {
        // dd(config('filesystems.disks.s3'));
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $profile = $user->delivery_agent_profile;

            if (!$profile) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::AGENT_PROFILE_NOT_FOUND); // Use specific constant
            }

            $data = $request->validated();

            // Handle profile photo upload
            if ($request->hasFile('profile_photo')) {
                // Remove old photo if exists, then add new one
                $profile->clearMediaCollection('profile_photo');
                $profile->addMedia($request->file('profile_photo'))->toMediaCollection('profile_photo');
            }

            // Handle KYC document uploads
            if ($request->hasFile('kyc_id_front')) {
                $profile->addMedia($request->file('kyc_id_front'))->toMediaCollection('kyc_id_front');
            }

            if ($request->hasFile('kyc_id_back')) {
                $profile->addMedia($request->file('kyc_id_back'))->toMediaCollection('kyc_id_back');
            }

            if ($request->hasFile('kyc_license_front')) {
                $profile->addMedia($request->file('kyc_license_front'))->toMediaCollection('kyc_license_front');
            }

            if ($request->hasFile('kyc_license_back')) {
                $profile->addMedia($request->file('kyc_license_back'))->toMediaCollection('kyc_license_back');
            }

            if ($request->hasFile('kyc_address_proof')) {
                $profile->addMedia($request->file('kyc_address_proof'))->toMediaCollection('kyc_address_proof');
            }

            if ($request->hasFile('kyc_passport')) {
                $profile->addMedia($request->file('kyc_passport'))->toMediaCollection('kyc_passport');
            }

            // If KYC documents are uploaded, update the KYC status to SUBMITTED
            if ($request->hasAny(['kyc_id_front', 'kyc_id_back', 'kyc_license_front', 'kyc_license_back', 'kyc_address_proof', 'kyc_passport'])) {
                $data['kyc_status'] = 'SUBMITTED';
            }

            // Separate data for User model and DeliveryAgentProfile model
            $userData = [];
            if (isset($data['name'])) {
                $userData['name'] = $data['name'];
                unset($data['name']); // Remove name from data intended for DeliveryAgentProfile
            }

            // Update User model if there's user data
            if (!empty($userData)) {
                $userToUpdate = $profile->user;
                if ($userToUpdate) {
                    $userToUpdate->update($userData);
                }
            }

            // Update DeliveryAgentProfile model if there's profile data
            if (!empty($data)) {
                $profile = $this->repository->updateProfile($data, $profile->id);
            }

            // Load relationships
            $profile->load(['user', 'activeVehicle']);

            return response()->json([
                'success' => true,
                'data' => new DeliveryAgentProfileResource($profile)
            ]);
        } catch (DeliveryException $e) {
            Log::error($e);
            throw $e;
        } catch (\Exception $e) {
            Log::error($e);
            DeliveryExceptionHelper::internalError('Failed to update profile', $e->getMessage());
        }
    }

    /**
     * Submit KYC documents.
     *
     * @param Request $request // Consider using a specific FormRequest if validation gets complex
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function submitKyc(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $profile = $user->delivery_agent_profile;

            if (!$profile) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::AGENT_PROFILE_NOT_FOUND); // Use specific constant
            }

            // Check if KYC status allows submission
            if (!in_array($profile->kyc_status, ['PENDING', 'REJECTED'])) {
                DeliveryExceptionHelper::badRequest('You cannot submit KYC documents at this time'); // Use badRequest for invalid state
            }

            // Validate that at least one KYC document is provided
            $request->validate([
                'kyc_id_front' => 'required_without_all:kyc_id_back,kyc_license_front,kyc_license_back,kyc_address_proof,kyc_passport|file|mimes:jpeg,png,jpg,pdf|max:8192',
                'kyc_id_back' => 'required_without_all:kyc_id_front,kyc_license_front,kyc_license_back,kyc_address_proof,kyc_passport|file|mimes:jpeg,png,jpg,pdf|max:8192',
                'kyc_license_front' => 'required_without_all:kyc_id_front,kyc_id_back,kyc_license_back,kyc_address_proof,kyc_passport|file|mimes:jpeg,png,jpg,pdf|max:8192',
                'kyc_license_back' => 'required_without_all:kyc_id_front,kyc_id_back,kyc_license_front,kyc_address_proof,kyc_passport|file|mimes:jpeg,png,jpg,pdf|max:8192',
                'kyc_address_proof' => 'required_without_all:kyc_id_front,kyc_id_back,kyc_license_front,kyc_license_back,kyc_passport|file|mimes:jpeg,png,jpg,pdf|max:8192',
                'kyc_passport' => 'required_without_all:kyc_id_front,kyc_id_back,kyc_license_front,kyc_license_back,kyc_address_proof|file|mimes:jpeg,png,jpg,pdf|max:8192',
            ]);

            // Handle KYC document uploads
            if ($request->hasFile('kyc_id_front')) {
                $profile->addMedia($request->file('kyc_id_front'))->toMediaCollection('kyc_id_front');
            }

            if ($request->hasFile('kyc_id_back')) {
                $profile->addMedia($request->file('kyc_id_back'))->toMediaCollection('kyc_id_back');
            }

            if ($request->hasFile('kyc_license_front')) {
                $profile->addMedia($request->file('kyc_license_front'))->toMediaCollection('kyc_license_front');
            }

            if ($request->hasFile('kyc_license_back')) {
                $profile->addMedia($request->file('kyc_license_back'))->toMediaCollection('kyc_license_back');
            }

            if ($request->hasFile('kyc_address_proof')) {
                $profile->addMedia($request->file('kyc_address_proof'))->toMediaCollection('kyc_address_proof');
            }

            if ($request->hasFile('kyc_passport')) {
                $profile->addMedia($request->file('kyc_passport'))->toMediaCollection('kyc_passport');
            }

            // Update KYC status to SUBMITTED
            $profile = $this->repository->updateKycStatus(['kyc_status' => 'SUBMITTED'], $profile->id);

            // Load relationships
            $profile->load(['user', 'activeVehicle']);

            return response()->json([
                'success' => true,
                'data' => new DeliveryAgentProfileResource($profile)
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to submit KYC documents', $e->getMessage());
        }
    }

    /**
     * Toggle wallet preference for earnings.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function toggleWalletPreference(Request $request): JsonResponse
    {
        $user = $request->user();

        if (
            !$user ||
            (!$user->hasPermissionTo(Permission::DELIVERY_AGENT) && !$user->hasPermissionTo(Permission::SUPER_ADMIN))
        ) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            // If admin, require user_id and fetch the target agent's profile
            if ($user->hasPermissionTo(Permission::SUPER_ADMIN)) {
                $request->validate([
                    'user_id' => 'required|exists:users,id',
                    'use_wallet_for_earnings' => 'required|boolean',
                    'wallet_points_conversion_rate' => 'nullable|numeric|min:0',
                ]);
                $targetUser = User::find($request->input('user_id'));
                $profile = $targetUser ? $targetUser->delivery_agent_profile : null;
            } else {
                // Delivery agent updating their own profile
                $request->validate([
                    'use_wallet_for_earnings' => 'required|boolean',
                    'wallet_points_conversion_rate' => 'nullable|numeric|min:0',
                ]);
                $profile = $user->delivery_agent_profile;
            }

            if (!$profile) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::AGENT_PROFILE_NOT_FOUND); // Use specific constant
            }

            // Update profile
            $data = [
                'use_wallet_for_earnings' => $request->input('use_wallet_for_earnings'),
            ];

            if ($request->has('wallet_points_conversion_rate')) {
                $data['wallet_points_conversion_rate'] = $request->input('wallet_points_conversion_rate');
            } elseif ($request->input('use_wallet_for_earnings') && !$profile->wallet_points_conversion_rate) {
                // Set default conversion rate if enabling wallet and no rate is set
                $data['wallet_points_conversion_rate'] = $this->currencyToWalletRatio();
            }

            $profile = $this->repository->updateProfile($data, $profile->id);

            return response()->json([
                'success' => true,
                'message' => 'Wallet preference updated successfully',
                'data' => new DeliveryAgentProfileResource($profile)
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to update wallet preference', $e->getMessage());
        }
    }

    /**
     * Get the agent's wallet.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function getWallet(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $wallet = $user->wallet;

            if (!$wallet) {
                // Create a new wallet if it doesn't exist
                $wallet = $user->wallet()->create([
                    'total_points' => 0,
                    'points_used' => 0,
                    'available_points' => 0,
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'wallet' => new WalletResource($wallet),
                    'conversion_rate' => $user->delivery_agent_profile->wallet_points_conversion_rate ?? $this->currencyToWalletRatio(),
                    'currency_amount' => $this->walletPointsToCurrency($wallet->available_points)
                ]
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve wallet information', $e->getMessage());
        }
    }
}
