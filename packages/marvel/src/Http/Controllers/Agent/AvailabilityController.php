<?php

namespace Marvel\Http\Controllers\Agent;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Marvel\Database\Repositories\DeliveryAgentProfileRepository;
use Marvel\Exceptions\DeliveryException;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Requests\AvailabilityUpdateRequest;
use Marvel\Http\Resources\DeliveryAgentProfileResource;
use Marvel\Enums\Permission;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;

class AvailabilityController extends CoreController
{
    public $repository;

    public function __construct(DeliveryAgentProfileRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Display the current availability status.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function show(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $profile = $user->delivery_agent_profile;

            if (!$profile) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::NOT_FOUND);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'availability_status' => $profile->availability_status,
                    'current_location' => $profile->current_location,
                ]
            ]);
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to retrieve availability status', $e->getMessage());
        }
    }

    /**
     * Update the availability status.
     *
     * @param AvailabilityUpdateRequest $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function update(AvailabilityUpdateRequest $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $profile = $user->delivery_agent_profile;

            if (!$profile) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::NOT_FOUND);
            }

            // Check if agent has a verified KYC and active vehicle before going online
            if ($request->availability_status === 'ONLINE') {
                if ($profile->kyc_status !== 'APPROVED') {
                    DeliveryExceptionHelper::badRequest(
                        DeliveryConstants::KYC_NOT_APPROVED,
                        'Your KYC must be approved before you can go online'
                    );
                }

                if (!$profile->active_vehicle_id) {
                    DeliveryExceptionHelper::badRequest(
                        DeliveryConstants::NO_ACTIVE_VEHICLE,
                        'You must set an active vehicle before going online'
                    );
                }

                $vehicle = $profile->activeVehicle;
                if (!$vehicle || !$vehicle->is_verified) {
                    DeliveryExceptionHelper::badRequest(
                        DeliveryConstants::VEHICLE_NOT_VERIFIED,
                        'Your vehicle must be verified before you can go online'
                    );
                }
            }

            $data = $request->validated();

            $profile = $this->repository->updateAvailabilityStatus($data, $profile->id);

            // Load relationships
            $profile->load(['user', 'activeVehicle']);

            return response()->json([
                'success' => true,
                'message' => 'Availability status updated successfully',
                'data' => new DeliveryAgentProfileResource($profile)
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to update availability status', $e->getMessage());
        }
    }
}
