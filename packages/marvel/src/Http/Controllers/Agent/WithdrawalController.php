<?php

namespace Marvel\Http\Controllers\Agent;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Marvel\Database\Repositories\DeliveryAgentWithdrawalRepository;
use Marvel\Exceptions\DeliveryException;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Requests\WithdrawalRequest;
use Marvel\Http\Resources\DeliveryAgentWithdrawalResource;
use Marvel\Enums\Permission;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;

class WithdrawalController extends CoreController
{
    public $repository;

    public function __construct(DeliveryAgentWithdrawalRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Request a withdrawal.
     *
     * @param WithdrawalRequest $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function requestWithdrawal(WithdrawalRequest $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $data = $request->validated();

            $withdrawal = $this->repository->requestWithdrawal($data, $user);

            return response()->json([
                'success' => true,
                'message' => DeliveryConstants::WITHDRAWAL_REQUESTED,
                'data' => new DeliveryAgentWithdrawalResource($withdrawal)
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to request withdrawal', $e->getMessage());
        }
    }
}
