<?php

namespace Marvel\Http\Controllers\Agent;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Repositories\DeliveryRepository;
use Marvel\Exceptions\DeliveryException;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Requests\UpdateDeliveryStatusRequest;
use Marvel\Http\Requests\ProofOfDeliveryRequest;
use Marvel\Http\Resources\DeliveryResource;
use Illuminate\Database\Eloquent\ModelNotFoundException; // Import ModelNotFoundException
use Marvel\Enums\Permission;
use Marvel\Enums\DeliveryStatus;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;

class DeliveryStatusController extends CoreController
{
    public $repository;

    public function __construct(DeliveryRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Update delivery status to PICKED_UP.
     *
     * @param UpdateDeliveryStatusRequest $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function pickup(UpdateDeliveryStatusRequest $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            // 1. Find the delivery
            $delivery = Delivery::where('delivery_agent_user_id', $user->id)
                ->where('status', DeliveryStatus::ACCEPTED_BY_AGENT)
                ->findOrFail($id);

            // 2. Validate data
            $data = $request->validated();

            // 3. Update status
            $delivery = $this->repository->updateDeliveryStatus($delivery, DeliveryStatus::PICKED_UP, $user, $data);

            // 4. Return success response
            return response()->json([
                'message' => DeliveryConstants::DELIVERY_STATUS_UPDATED,
                'delivery' => new DeliveryResource($delivery->load(['order', 'assignedBy']))
            ]);
        } catch (\Exception $e) {
            if ($e instanceof ModelNotFoundException) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::DELIVERY_NOT_FOUND);
            } elseif ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to update delivery status', $e->getMessage());
        }
    }

    /**
     * Update delivery status to IN_TRANSIT.
     *
     * @param UpdateDeliveryStatusRequest $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function inTransit(UpdateDeliveryStatusRequest $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            // 1. Find the delivery
            $delivery = Delivery::where('delivery_agent_user_id', $user->id)
                ->where('status', DeliveryStatus::PICKED_UP)
                ->findOrFail($id);

            // 2. Validate data
            $data = $request->validated();

            // 3. Update status
            $delivery = $this->repository->updateDeliveryStatus($delivery, DeliveryStatus::IN_TRANSIT, $user, $data);

            // 4. Return success response
            return response()->json([
                'message' => DeliveryConstants::DELIVERY_STATUS_UPDATED,
                'delivery' => new DeliveryResource($delivery->load(['order', 'assignedBy']))
            ]);
        } catch (\Exception $e) {
            if ($e instanceof ModelNotFoundException) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::DELIVERY_NOT_FOUND);
            } elseif ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to update delivery status', $e->getMessage());
        }
    }

    /**
     * Update delivery status to REACHED_DESTINATION.
     *
     * @param UpdateDeliveryStatusRequest $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function reached(UpdateDeliveryStatusRequest $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            // 1. Find the delivery
            $delivery = Delivery::where('delivery_agent_user_id', $user->id)
                ->where('status', DeliveryStatus::IN_TRANSIT)
                ->findOrFail($id);

            // 2. Validate data
            $data = $request->validated();

            // 3. Update status
            $delivery = $this->repository->updateDeliveryStatus($delivery, DeliveryStatus::REACHED_DESTINATION, $user, $data);

            // 4. Return success response
            return response()->json([
                'message' => DeliveryConstants::DELIVERY_STATUS_UPDATED,
                'delivery' => new DeliveryResource($delivery->load(['order', 'assignedBy']))
            ]);
        } catch (\Exception $e) {
            if ($e instanceof ModelNotFoundException) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::DELIVERY_NOT_FOUND);
            } elseif ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to update delivery status', $e->getMessage());
        }
    }

    /**
     * Add proof of delivery.
     *
     * @param ProofOfDeliveryRequest $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function addProofOfDelivery(ProofOfDeliveryRequest $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            // 1. Find the delivery
            $delivery = Delivery::where('delivery_agent_user_id', $user->id)
                ->whereIn('status', [DeliveryStatus::REACHED_DESTINATION, DeliveryStatus::DELIVERED])
                ->findOrFail($id);

            // 2. Validate data
            $data = $request->validated();

            // 3. Handle file uploads & data prep
            if ($request->hasFile('pod_image')) {
                $delivery->addMedia($request->file('pod_image'))->toMediaCollection('proof_of_delivery');
            }

            if (isset($data['signature_data']) && !empty($data['signature_data'])) {
                $data['proof_of_delivery'] = [
                    'type' => 'SIGNATURE',
                    'data' => $data['signature_data']
                ];
            }

            if (isset($data['code']) && !empty($data['code'])) {
                $data['proof_of_delivery'] = [
                    'type' => 'CODE',
                    'data' => $data['code']
                ];
            }

            // 4. Add proof of delivery via repository
            $delivery = $this->repository->addProofOfDelivery($delivery, $data);

            // 5. Return success response
            return response()->json([
                'message' => DeliveryConstants::POD_ADDED,
                'delivery' => new DeliveryResource($delivery->load(['order', 'assignedBy']))
            ]);
        } catch (\Exception $e) {
            if ($e instanceof ModelNotFoundException) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::DELIVERY_NOT_FOUND);
            } elseif ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to add proof of delivery', $e->getMessage());
        }
    }

    /**
     * Update delivery status to DELIVERED.
     *
     * @param UpdateDeliveryStatusRequest $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function deliver(UpdateDeliveryStatusRequest $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            // 1. Find the delivery
            $delivery = Delivery::where('delivery_agent_user_id', $user->id)
                ->where('status', DeliveryStatus::REACHED_DESTINATION)
                ->findOrFail($id);

            // 2. Check if proof of delivery is required and provided
            if ($delivery->pod_type && $delivery->pod_type !== 'NONE' && !$delivery->proof_of_delivery && !$delivery->getFirstMediaUrl('proof_of_delivery')) {
                DeliveryExceptionHelper::badRequest('Proof of delivery is required before marking as delivered');
            }

            // 3. Validate data
            $data = $request->validated();

            // 4. Update status
            $delivery = $this->repository->updateDeliveryStatus($delivery, DeliveryStatus::DELIVERED, $user, $data);

            // 5. Return success response
            return response()->json([
                'message' => DeliveryConstants::DELIVERY_STATUS_UPDATED,
                'delivery' => new DeliveryResource($delivery->load(['order', 'assignedBy']))
            ]);
        } catch (\Exception $e) {
            if ($e instanceof ModelNotFoundException) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::DELIVERY_NOT_FOUND);
            } elseif ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to update delivery status', $e->getMessage());
        }
    }

    /**
     * Update delivery status to FAILED_DELIVERY.
     *
     * @param UpdateDeliveryStatusRequest $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function fail(UpdateDeliveryStatusRequest $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            // 1. Find the delivery
            $delivery = Delivery::where('delivery_agent_user_id', $user->id)
                ->whereIn('status', [DeliveryStatus::PICKED_UP, DeliveryStatus::IN_TRANSIT, DeliveryStatus::REACHED_DESTINATION])
                ->findOrFail($id);

            // 2. Validate specific reason for failure
            $request->validate([
                'reason' => 'required|string',
            ]);

            // 3. Get validated data (includes reason)
            $data = $request->validated();

            // 4. Update status
            $delivery = $this->repository->updateDeliveryStatus($delivery, DeliveryStatus::FAILED_DELIVERY, $user, $data);

            // 5. Return success response
            return response()->json([
                'message' => DeliveryConstants::DELIVERY_STATUS_UPDATED,
                'delivery' => new DeliveryResource($delivery->load(['order', 'assignedBy']))
            ]);
        } catch (\Exception $e) {
            if ($e instanceof ModelNotFoundException) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::DELIVERY_NOT_FOUND);
            } elseif ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to update delivery status', $e->getMessage());
        }
    }

    /**
     * Confirm payment for cash on delivery.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function confirmPayment(Request $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            // 1. Find the delivery
            $delivery = Delivery::where('delivery_agent_user_id', $user->id)
                ->where('status', DeliveryStatus::DELIVERED)
                ->findOrFail($id);

            // 2. Confirm payment via repository
            $delivery = $this->repository->confirmPayment($delivery, $user);

            // 3. Return success response
            return response()->json([
                'message' => DeliveryConstants::PAYMENT_CONFIRMED,
                'delivery' => new DeliveryResource($delivery->load(['order', 'assignedBy']))
            ]);
        } catch (\Exception $e) {
            if ($e instanceof ModelNotFoundException) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::DELIVERY_NOT_FOUND);
            } elseif ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to confirm payment', $e->getMessage());
        }
    }
}
