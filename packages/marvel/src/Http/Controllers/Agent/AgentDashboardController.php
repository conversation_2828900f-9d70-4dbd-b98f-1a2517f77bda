<?php

namespace Marvel\Http\Controllers\Agent;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Marvel\Database\Models\User;
use Marvel\Database\Repositories\DeliveryRepository;
use Marvel\Database\Repositories\DeliveryAgentEarningRepository;
use Marvel\Enums\Permission;
use Marvel\Exceptions\DeliveryException;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Http\Controllers\CoreController;
use Marvel\Helpers\DeliveryExceptionHelper;
use Marvel\Http\Resources\DeliveryResource;

class AgentDashboardController extends CoreController
{
    public $deliveryRepository;
    public $earningRepository;

    public function __construct(DeliveryRepository $deliveryRepository, DeliveryAgentEarningRepository $earningRepository)
    {
        $this->deliveryRepository = $deliveryRepository;
        $this->earningRepository = $earningRepository;
    }

    /**
     * Get summary of deliveries and earnings.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function summary(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }
        try {
            $deliveryCount = $this->deliveryRepository->getDeliveryCountsForAgent($user);
            $earnings = $this->earningRepository->getEarningsSummaryForAgent($user);

            return response()->json([
                'success' => true,
                'data' => [
                    'earnings' => $earnings,
                    'delivery_counts' => $deliveryCount,
                ]
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        }
        catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve summary', $e->getMessage());
        }
    }

    /**
     * Get recent deliveries for the agent.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function recentDeliveries(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !$user->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $limit = $request->query('limit', 5);
            $deliveries = $this->deliveryRepository->getRecentDeliveriesForAgent($user, $limit);
            return response()->json([
                'success' => true,
                'data' => DeliveryResource::collection($deliveries)
            ]);
        } catch (DeliveryException $e) {
            throw $e;
        }
        catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve recent deliveries', $e->getMessage());
        }
    }
}