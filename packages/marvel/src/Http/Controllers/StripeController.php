<?php

namespace Marvel\Http\Controllers;

use Illuminate\Http\Request;
use Stripe\Customer;
use Stripe\PaymentIntent;
use Stripe\PaymentMethod;
use Stripe\SetupIntent;
use Stripe\Stripe;
use Stripe\StripeClient;

class StripeController extends CoreController
{
    public function __construct(){
        Stripe::setApiKey(env("STRIPE_API_KEY"));

        $this->stripe = new StripeClient(env("STRIPE_API_KEY"));
    }

    public function createCustomer(Request $request){
        $user = $request->user();


        if(!$user->stripe_customer_id) {
            $customer = Customer::create([
                'email' => $user->email,
                'name' => $user->name
            ]);

            $user->stripe_customer_id = $customer->id;
            $user->save();
        }

        return response()->json(['customer_id' => $user->stripe_customer_id]);
    }

    public function createSetupIntent(Request $request){
        return response()->json([
            'clientSecret' => SetupIntent::create([
                'customer' => $request->user()->stripe_customer_id
            ])->client_secret,
            ]);
    }

    public function deletePaymentMethod(Request $request , $paymentMethodId){
        $user = $request->user();

        try {
            $paymentMethod = PaymentMethod::retrieve($paymentMethodId);

            if($paymentMethod->customer !== $user->stripe_customer_id) {
                return response()->json(['error' => 'Unauthorized'] , 403);
            }

            $paymentMethod->detach();

            return response()->json(['message' => 'Payment method detached successfully']);
        } catch (\Throwable $th) {
             return response()->json(['error' => $th->getMessage()], 400);
        }
    }

    public function pay(Request $request){

        $customerId = '';

        $save = $request->boolean('save');

        $paymentMethodId = $request->payment_method_id;

        if(!$request->user()->stripe_customer_id) {
            $response = $this->createCustomer($request);
            $customerId = $response->getData(true)['customer_id'];
        }else {
            $customerId = $request->user()->stripe_customer_id;
        }

        try {
            if($save) {
                $this->stripe->paymentMethods->attach($paymentMethodId , [
                    'customer' => $customerId
                ]);
            }
        } catch (\Throwable $th) {
            
        }

        $intent = PaymentIntent::create([
            'amount' => $request->amount,
            'currency' => 'xaf',
            'payment_method' => $request->payment_method_id,
            'customer' => $customerId,
            'confirm' => true,
            'automatic_payment_methods' => [
                'enabled' => true,
                'allow_redirects' => 'never',
            ],
        ]);

        return response()->json($intent);
    }

    public function listPaymentMethods(Request $request){
        return response()->json(PaymentMethod::all([
            'customer' => $request->user()->stripe_customer_id,
            'type' => 'card'
        ]));
    }

    public function setDefaultMethod(Request $request){
        Customer::update($request->user()->stripe_customer_id , [
            'invoice_settings' => [
                'default_payment_method' => $request->payment_method_id,
            ]
            ]);

            return response()->json(['message' => 'Default payment method set']);
    }
}
