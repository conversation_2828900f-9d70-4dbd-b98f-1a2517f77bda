<?php

namespace Marvel\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Marvel\Database\Models\DeliveryAgentTransaction;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Marvel\Database\Models\User;
use Marvel\Database\Repositories\DeliveryAgentTransactionRepository;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Resources\DeliveryAgentTransactionResource;
use Marvel\Enums\Permission;
use Marvel\Enums\Role;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;
use Marvel\Exceptions\DeliveryException;

class AgentTransactionController extends CoreController
{
    public $repository;

    public function __construct(DeliveryAgentTransactionRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Display a listing of the transactions.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        
        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $limit = $request->query('limit', 15);
            $transaction_type = $request->query('transaction_type');
            $status = $request->query('status');
            $agent_id = $request->query('agent_id');
            $date_from = $request->query('date_from');
            $date_to = $request->query('date_to');

            $query = DeliveryAgentTransaction::with(['deliveryAgent', 'createdBy']);

            if ($transaction_type) {
                $query->where('transaction_type', $transaction_type);
            }

            if ($status) {
                $query->where('status', $status);
            }

            if ($agent_id) {
                $query->where('delivery_agent_user_id', $agent_id);
            }

            if ($date_from) {
                $query->whereDate('created_at', '>=', $date_from);
            }

            if ($date_to) {
                $query->whereDate('created_at', '<=', $date_to);
            }

            $transactions = $query->orderBy('created_at', 'desc')->paginate($limit);

            return response()->json(DeliveryAgentTransactionResource::collection($transactions));
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to retrieve transactions', $e->getMessage());
        }
    }

    /**
     * Display the specified transaction.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function show(Request $request, $id): JsonResponse
    {
        $user = $request->user();
        
        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $transaction = DeliveryAgentTransaction::with(['deliveryAgent', 'createdBy'])
                ->findOrFail($id);

            return response()->json(new DeliveryAgentTransactionResource($transaction));
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound('Transaction not found');
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to retrieve transaction', $e->getMessage());
        }
    }

    /**
     * Create a manual adjustment transaction.
     *
     * @param Request $request
     * @param int $agentId
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function createAdjustment(Request $request, $agentId): JsonResponse
    {
        $user = $request->user();
        
        if (!$user || !$user->hasPermissionTo(Permission::SUPER_ADMIN)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $request->validate([
                'amount' => 'required|numeric|not_in:0',
                'notes' => 'required|string',
            ]);

            $agent = User::whereHas('roles', function ($q) {
                $q->where('name', Role::DELIVERY_AGENT);
            })->findOrFail($agentId);

            $data = $request->only(['amount', 'notes']);
            $data['delivery_agent_user_id'] = $agent->id;
            $data['created_by_user_id'] = $user->id;

            $transaction = $this->repository->createAdjustmentTransaction($data);

            // Update agent's earnings
            $earnings = $agent->delivery_agent_earnings;
            if ($earnings) {
                $earnings->total_earnings += max(0, $data['amount']); // Only add positive adjustments to total earnings
                $earnings->current_balance += $data['amount'];
                $earnings->save();
            }

            return response()->json([
                'message' => 'Manual adjustment created successfully',
                'transaction' => new DeliveryAgentTransactionResource($transaction),
            ]);
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::AGENT_NOT_FOUND);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to create adjustment', $e->getMessage());
        }
    }
}
