<?php

namespace Marvel\Http\Controllers\Admin;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;
use Marvel\Database\Models\Plan;
use Marvel\Enums\Permission;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Requests\PlanRequest;

class AdminSubscriptionController extends CoreController
{
    /**
     * Creates a new plan ressource.
     *
     * @param PlanRequest $request
     * @return JsonResponse
     */

     public function createPlan(PlanRequest $request) {
        $user = $request->user();


         if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN))) {
            throw new UnauthorizedException("You are not allowed to access this ressource");
        }
        try {
            $plan = Plan::create([
                "name"=> $request->name,
                "description" => $request->description,
                "features" => $request->features,
                "price" => $request->price,
            ]);

            return response()->json($plan);
        } catch (\Throwable $th) {
            throw $th;
        }
     }

     public function getPlans(Request $request) {
        $plans = Plan::all();

        return response()->json($plans);
     }
}
