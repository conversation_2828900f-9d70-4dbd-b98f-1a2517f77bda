<?php

namespace Marvel\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Repositories\DeliveryRepository;
use Marvel\Services\AgentDiscoveryService;
use Marvel\Exceptions\DeliveryException;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Requests\AssignDeliveryRequest;
use Marvel\Http\Requests\UpdateDeliveryStatusRequest;
use Marvel\Http\Resources\DeliveryResource;
use Marvel\Http\Resources\NearestAgentResource;
use Marvel\Enums\Permission;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;
use Marvel\Enums\OrderStatus;
use Marvel\Enums\PaymentStatus;

class DeliveryController extends CoreController
{
    public $repository;
    private AgentDiscoveryService $agentDiscoveryService;

    public function __construct(DeliveryRepository $repository, AgentDiscoveryService $agentDiscoveryService)
    {
        $this->repository = $repository;
        $this->agentDiscoveryService = $agentDiscoveryService;
    }

    /**
     * Display a listing of the deliveries.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $limit = $request->query('limit', 15);
            $status = $request->query('status');
            $agent_id = $request->query('agent_id');
            $date_from = $request->query('date_from');
            $date_to = $request->query('date_to');

            $query = Delivery::with(['order', 'deliveryAgent', 'assignedBy']);

            if ($status) {
                $query->where('status', $status);
            }

            if ($agent_id) {
                $query->where('delivery_agent_user_id', $agent_id);
            }

            if ($date_from) {
                $query->whereDate('created_at', '>=', $date_from);
            }

            if ($date_to) {
                $query->whereDate('created_at', '<=', $date_to);
            }

            // Store owner should only see deliveries for their orders
            if ($user->hasPermissionTo(Permission::STORE_OWNER) && !($user->hasPermissionTo(Permission::SUPER_ADMIN))) {
                $query->whereHas('order', function ($q) use ($user) {
                    $q->whereHas('shop', function ($q) use ($user) {
                        $q->where('owner_id', $user->id);
                    });
                });
            }

            $deliveries = $query->orderBy('created_at', 'desc')->paginate($limit);

            return response()->json([
                'success' => true,
                'data' => DeliveryResource::collection($deliveries),
                'pagination' => [
                    'total' => $deliveries->total(),
                    'per_page' => $deliveries->perPage(),
                    'current_page' => $deliveries->currentPage(),
                    'last_page' => $deliveries->lastPage()
                ]
            ]);
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to retrieve deliveries', $e->getMessage());
        }
    }

    /**
     * Display the specified delivery.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function show(Request $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $delivery = Delivery::with(['order', 'deliveryAgent', 'assignedBy', 'statusLogs.user'])->findOrFail($id);

            // Store owner should only see deliveries for their orders
            if ($user->hasPermissionTo(Permission::STORE_OWNER) && !($user->hasPermissionTo(Permission::SUPER_ADMIN))) {
                $order = $delivery->order;
                if (!$order || $order->shop->owner_id !== $user->id) {
                    DeliveryExceptionHelper::unauthorized(
                        DeliveryConstants::NOT_AUTHORIZED,
                        'You are not authorized to view this delivery'
                    );
                }
            }

            return response()->json([
                'success' => true,
                'data' => new DeliveryResource($delivery)
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound('Delivery not found');
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to retrieve delivery', $e->getMessage());
        }
    }

    /**
     * Assign a delivery to an agent.
     *
     * @param AssignDeliveryRequest $request
     * @param int $orderId
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function assignDelivery(AssignDeliveryRequest $request, $orderId): JsonResponse
    {
        $user = $request->user();

        // Explicitly check if the user has EITHER Super Admin OR Store Owner permission
        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $order = Order::findOrFail($orderId);

            // Store owner should only assign deliveries for their orders
            if ($user->hasPermissionTo(Permission::STORE_OWNER) && !($user->hasPermissionTo(Permission::SUPER_ADMIN))) {
                if (!$order->shop || $order->shop->owner_id !== $user->id) {
                    DeliveryExceptionHelper::unauthorized(
                        DeliveryConstants::NOT_AUTHORIZED,
                        'You are not authorized to assign deliveries for this order'
                    );
                }
            }

            $data = $request->validated();

            $delivery = $this->repository->assignDelivery($data, $order, $user);

            return response()->json([
                'success' => true,
                'message' => DeliveryConstants::DELIVERY_ASSIGNED,
                'data' => new DeliveryResource($delivery->load(['order', 'deliveryAgent', 'assignedBy']))
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound('Order not found');
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to assign delivery', $e->getMessage());
        }
    }

    /**
     * Cancel a delivery.
     *
     * @param UpdateDeliveryStatusRequest $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function cancelDelivery(UpdateDeliveryStatusRequest $request, $id): JsonResponse
    {
        $user = $request->user();

        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $delivery = Delivery::findOrFail($id);

            // Store owner should only cancel deliveries for their orders
            if ($user->hasPermissionTo(Permission::STORE_OWNER) && !($user->hasPermissionTo(Permission::SUPER_ADMIN))) {
                $order = $delivery->order;
                if (!$order || $order->shop->owner_id !== $user->id) {
                    DeliveryExceptionHelper::unauthorized(
                        DeliveryConstants::NOT_AUTHORIZED,
                        'You are not authorized to cancel this delivery'
                    );
                }
            }

            $data = $request->validated();

            if (empty($data['reason'])) {
                DeliveryExceptionHelper::badRequest(
                    'Cancellation reason is required',
                    'You must provide a reason when cancelling a delivery'
                );
            }

            $delivery = $this->repository->updateDeliveryStatus($delivery, 'CANCELLED', $user, $data);

            return response()->json([
                'success' => true,
                'message' => DeliveryConstants::DELIVERY_STATUS_UPDATED,
                'data' => new DeliveryResource($delivery->load(['order', 'deliveryAgent', 'assignedBy']))
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound('Delivery not found');
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to cancel delivery', $e->getMessage());
        }
    }

    /**
     * Find nearest available delivery agents for an order.
     *
     * @param Request $request
     * @param int $orderId
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function findNearestAgents(Request $request, $orderId): JsonResponse
    {
        $user = $request->user();

        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $radius = (float) $request->query('radius', 15);

            // Validate radius
            if (!is_numeric($radius) || $radius < 1 || $radius > 100) {
                DeliveryExceptionHelper::badRequest(
                    'Invalid radius',
                    'Radius must be a number between 1 and 100 kilometers'
                );
            }

            // Find order and load relationships
            $order = Order::with(['shop', 'children.shop'])->findOrFail($orderId);

            // Store owner should only find agents for their own orders
            if ($user->hasPermissionTo(Permission::STORE_OWNER) && !($user->hasPermissionTo(Permission::SUPER_ADMIN))) {
                // If it's a parent order, check if any child order belongs to the owner's shop
                if ($order->children && $order->children->count() > 0) {
                    $hasAccess = $order->children->contains(function ($childOrder) use ($user) {
                        return $childOrder->shop && $childOrder->shop->owner_id === $user->id;
                    });
                    if (!$hasAccess) {
                        DeliveryExceptionHelper::forbidden(
                            DeliveryConstants::FORBIDDEN,
                            'Access forbidden: You cannot view nearest agents for this multi-vendor order.'
                        );
                    }
                } else {
                    // For single shop orders, check if the order's shop belongs to the owner
                    if (!$order->shop || $order->shop->owner_id !== $user->id) {
                        DeliveryExceptionHelper::forbidden(
                            DeliveryConstants::FORBIDDEN,
                            'Access forbidden: You cannot view nearest agents for this order.'
                        );
                    }
                }
            }
            
            // Handle multi-vendor delivery assignment
            $result = $this->agentDiscoveryService->findNearestAgentsForMultiVendor($order, $radius);
            
            $response = [
                'success' => true,
                'meta' => [
                    'order_id' => $order->id,
                    'search_radius_km' => $radius,
                    'is_multi_vendor' => $order->children && $order->children->count() > 1,
                    'should_consolidate' => $result['should_consolidate']
                ]
            ];

            if ($result['should_consolidate']) {
                $response['data'] = NearestAgentResource::collection($result['agents']);
                $response['meta']['central_point'] = $result['central_point'];
                $response['meta']['max_distance_between_shops'] = round($result['max_distance_between_shops'], 2);
                $response['meta']['total_agents_found'] = $result['agents']->count();
            } else {
                if ($response['meta']['is_multi_vendor']) {
                    // Multiple shops case
                    $response['data'] = [];
                    foreach ($result['agents'] as $shopId => $agents) {
                        Log::info('Nearest agents for shop', [
                            'shop_id' => $shopId,
                            'agents' => collect($agents)
                        ]);
                        $response['data'][$shopId] = [
                            'shop_id' => $shopId,
                            'agents' => NearestAgentResource::collection(collect($agents))
                        ];
                    }
                    $response['meta']['total_agents_found'] = collect($result['agents'])->sum(function($agents) {
                        return $agents->count();
                    });
                } else {
                    // Single shop case
                    $response['data'] = NearestAgentResource::collection($result['agents']);
                    $response['meta']['shop_location'] = $order->shop->settings['location'] ?? null;
                    $response['meta']['total_agents_found'] = $result['agents']->count();
                }
            }
            return response()->json($response);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            Log::error('Order not found in findNearestAgents', [
                'order_id' => $orderId,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            DeliveryExceptionHelper::notFound('Order not found');
        } catch (\Exception $e) {
            Log::error('Failed to find nearest agents', [
                'order_id' => $orderId,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to find nearest agents', $e->getMessage());
        }
    }

    /**
     * Assign delivery agent(s) to a multi-vendor order.
     *
     * @param Request $request
     * @param int $orderId
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function assignMultiVendorDelivery(Request $request, $orderId): JsonResponse
    {
        $user = $request->user();

        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            // Find order and load relationships
            $order = Order::with(['shop', 'children.shop'])->findOrFail($orderId);

            if (!$order->children || $order->children->count() === 0) {
                DeliveryExceptionHelper::badRequest(
                    'Invalid order',
                    'Not a multi-vendor order'
                );
            }

            // Validate request data
            $request->validate([
                'is_consolidated' => 'required|boolean',
                'delivery_agent_id' => 'required_if:is_consolidated,true|numeric',
                'agent_assignments' => 'required_if:is_consolidated,false|array',
                'agent_assignments.*' => 'required|numeric',
                'pickup_sequence' => 'array',
                'pickup_sequence.*' => 'numeric',
            ]);

            if ($request->input('is_consolidated')) {
                // Handle consolidated delivery
                $delivery = $this->repository->assignConsolidatedDelivery([
                    'delivery_agent_user_id' => $request->input('delivery_agent_id'),
                    'notes' => $request->input('notes'),
                    'pickup_sequence' => $request->input('pickup_sequence'),
                ], $order, $user);

                return response()->json([
                    'success' => true,
                    'message' => DeliveryConstants::DELIVERY_ASSIGNED,
                    'delivery' => new DeliveryResource($delivery->load(['order', 'assignedBy']))
                ]);
            } else {
                // Handle split deliveries
                $deliveries = $this->repository->assignSplitDeliveries(
                    $request->input('agent_assignments'),
                    $order,
                    $user
                );

                return response()->json([
                    'success' => true,
                    'message' => DeliveryConstants::DELIVERY_ASSIGNED,
                    'deliveries' => DeliveryResource::collection(collect($deliveries)->load(['order', 'assignedBy']))
                ]);
            }
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            Log::error('Order not found in assignMultiVendorDelivery', [
                'order_id' => $orderId,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            DeliveryExceptionHelper::notFound('Order not found');
        } catch (\Exception $e) {
            Log::error('Failed to assign multi-vendor delivery', [
                'order_id' => $orderId,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to assign delivery', $e->getMessage());
        }
    }

    /**
     * Calculate the maximum distance between any two shops in a collection
     *
     * @param Collection $shops Collection of shops with location data
     * @return float Maximum distance in kilometers
     */
    private function calculateMaxDistanceBetweenShops($shops)
    {
        $maxDistance = 0;

        // Compare each shop with every other shop
        $shops->each(function ($shop1) use ($shops, &$maxDistance) {
            $loc1 = $shop1->settings['location'];
            
            $shops->each(function ($shop2) use ($shop1, $loc1, &$maxDistance) {
                if ($shop1->id === $shop2->id) return;
                
                $loc2 = $shop2->settings['location'];
                $distance = $this->calculateHaversineDistance(
                    $loc1['lat'],
                    $loc1['lng'],
                    $loc2['lat'],
                    $loc2['lng']
                );
                
                $maxDistance = max($maxDistance, $distance);
            });
        });

        return $maxDistance;
    }

    /**
     * Calculate the central point between multiple shop locations
     * Uses the geometric median approach for better real-world applicability
     *
     * @param Collection $shops Collection of shops with location data
     * @return array ['lat' => float, 'lng' => float]
     */
    private function calculateCentralPoint($shops)
    {
        // Initialize with first shop's coordinates
        $totalLat = 0;
        $totalLng = 0;
        $count = 0;

        $shops->each(function ($shop) use (&$totalLat, &$totalLng, &$count) {
            $location = $shop->settings['location'];
            $totalLat += (float)$location['lat'];
            $totalLng += (float)$location['lng'];
            $count++;
        });

        // Calculate the geometric center (centroid)
        return [
            'lat' => $totalLat / $count,
            'lng' => $totalLng / $count
        ];
    }

    /**
     * Calculate the distance between two points using the Haversine formula
     *
     * @param float $lat1 Latitude of first point
     * @param float $lng1 Longitude of first point
     * @param float $lat2 Latitude of second point
     * @param float $lng2 Longitude of second point
     * @return float Distance in kilometers
     */
    private function calculateHaversineDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $lat1 = deg2rad($lat1);
        $lng1 = deg2rad($lng1);
        $lat2 = deg2rad($lat2);
        $lng2 = deg2rad($lng2);

        $dlat = $lat2 - $lat1;
        $dlng = $lng2 - $lng1;

        $a = sin($dlat/2) * sin($dlat/2) + 
             cos($lat1) * cos($lat2) * 
             sin($dlng/2) * sin($dlng/2);

        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
        
        return $earthRadius * $c;
    }
}
