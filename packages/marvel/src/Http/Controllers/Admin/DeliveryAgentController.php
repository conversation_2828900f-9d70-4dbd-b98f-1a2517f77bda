<?php

namespace Marvel\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Marvel\Database\Models\User;
use Marvel\Database\Models\Vehicle;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Requests\KYCActionRequest;
use Marvel\Http\Resources\DeliveryAgentProfileResource;
use Marvel\Http\Resources\UserResource;
use Marvel\Http\Resources\VehicleResource;
use Marvel\Enums\Permission;
use Marvel\Enums\Role;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;
use Marvel\Exceptions\DeliveryException; // Keep this for type hinting if needed
use Illuminate\Database\Eloquent\ModelNotFoundException;

class DeliveryAgentController extends CoreController
{
    /**
     * Display a listing of the delivery agents.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse {
        $user = $request->user();

        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $limit = $request->query('limit', 15);
            $availability_status = $request->query('availability_status');
            $kyc_status = $request->query('kyc_status');

            $query = User::with(['delivery_agent_profile', 'roles'])
                ->whereHas('roles', function ($q) {
                    $q->where('name', Role::DELIVERY_AGENT);
                });

            if ($availability_status) {
                $query->whereHas('delivery_agent_profile', function ($q) use ($availability_status) {
                    $q->where('availability_status', $availability_status);
                });
            }

            if ($kyc_status) {
                $query->whereHas('delivery_agent_profile', function ($q) use ($kyc_status) {
                    $q->where('kyc_status', $kyc_status);
                });
            }

            $agents = $query->orderBy('created_at', 'desc')->paginate($limit);

            return response()->json(UserResource::collection($agents));
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to retrieve delivery agents', $e->getMessage());
        }
    }

    /**
     * Display the specified delivery agent.
     *
     * @param Request $request
     * @param int     $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function show(Request $request, $id): JsonResponse {
        $user = $request->user();

        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $agent = User::with(['delivery_agent_profile', 'vehicles', 'roles'])
                ->whereHas('roles', function ($q) {
                    $q->where('name', Role::DELIVERY_AGENT);
                })
                ->findOrFail($id);

            return response()->json([
                'agent' => new UserResource($agent),
                'profile' => $agent->delivery_agent_profile ? new DeliveryAgentProfileResource($agent->delivery_agent_profile) : null,
                'vehicles' => $agent->vehicles ? VehicleResource::collection($agent->vehicles) : [],
            ]);
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::AGENT_NOT_FOUND);
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to retrieve delivery agent', $e->getMessage());
        }
    }

    /**
     * Approve KYC for a delivery agent.
     *
     * @param KYCActionRequest $request
     * @param int              $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function approveKYC(KYCActionRequest $request, $id): JsonResponse
    {
        $user = $request->user();
        
        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $agent = User::whereHas('roles', function ($q) {
                $q->where('name', Role::DELIVERY_AGENT);
            })->findOrFail($id);

            $profile = $agent->delivery_agent_profile;
            if (!$profile) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::AGENT_PROFILE_NOT_FOUND);
            }

            // Update KYC status
            $profile->kyc_status = 'APPROVED';
            $profile->kyc_rejection_reason = null;
            $profile->save();

            return response()->json([
                'message' => DeliveryConstants::KYC_APPROVED,
                'profile' => new DeliveryAgentProfileResource($profile),
            ]);
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::AGENT_NOT_FOUND);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to approve KYC', $e->getMessage());
        }
    }

    /**
     * Reject KYC for a delivery agent.
     *
     * @param KYCActionRequest $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function rejectKYC(KYCActionRequest $request, $id): JsonResponse
    {
        $user = $request->user();
        
        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $agent = User::whereHas('roles', function ($q) {
                $q->where('name', Role::DELIVERY_AGENT);
            })->findOrFail($id);

            $profile = $agent->delivery_agent_profile;
            if (!$profile) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::AGENT_PROFILE_NOT_FOUND);
            }

            $data = $request->validated();

            // Update KYC status
            $profile->kyc_status = 'REJECTED';
            $profile->kyc_rejection_reason = $data['rejection_reason'];
            $profile->save();

            return response()->json([
                'message' => DeliveryConstants::KYC_REJECTED,
                'profile' => new DeliveryAgentProfileResource($profile),
            ]);
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::AGENT_NOT_FOUND);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to reject KYC', $e->getMessage());
        }
    }

    /**
     * Verify a vehicle for a delivery agent.
     *
     * @param Request $request
     * @param int     $agentId
     * @param int     $vehicleId
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function verifyVehicle(Request $request, $agentId, $vehicleId): JsonResponse
    {
        $user = $request->user();
        
        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        $agent = User::whereHas('roles', function ($q) {
            $q->where('name', Role::DELIVERY_AGENT);
        })->findOrFail($agentId);
        
        $vehicle = Vehicle::where('delivery_agent_user_id', $agent->id)
            ->findOrFail($vehicleId);
        
        try {
            // Update vehicle verification
            $vehicle->is_verified = true;
            $vehicle->verification_notes = $request->input('notes');
            $vehicle->save();

            return response()->json([
                'message' => DeliveryConstants::VEHICLE_VERIFIED,
                'vehicle' => new VehicleResource($vehicle),
            ]);
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::VEHICLE_NOT_FOUND);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to verify vehicle', $e->getMessage());
        }
    }


    /**
     * Toggle activation status for a delivery agent.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function toggleActivation(Request $request, $id): JsonResponse
    {
        $user = $request->user();
        
        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $agent = User::whereHas('roles', function ($q) {
                $q->where('name', Role::DELIVERY_AGENT);
            })->findOrFail($id);

            // Toggle active status
            $agent->is_active = !$agent->is_active;
            $agent->save();

            // If deactivating, also set availability status to OFFLINE
            if (!$agent->is_active && $agent->delivery_agent_profile) {
                $agent->delivery_agent_profile->availability_status = 'OFFLINE';
                $agent->delivery_agent_profile->save();
            }

            $message = $agent->is_active ? DeliveryConstants::AGENT_ACTIVATED : DeliveryConstants::AGENT_DEACTIVATED;

            return response()->json([
                'message' => $message,
                'agent' => new UserResource($agent),
            ]);
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::AGENT_NOT_FOUND);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to toggle agent activation', $e->getMessage());
        }
    }
}
