<?php

namespace Marvel\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Marvel\Database\Models\DeliveryAgentWithdrawal;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Marvel\Database\Repositories\DeliveryAgentWithdrawalRepository;
use Marvel\Http\Controllers\CoreController;
use Marvel\Http\Requests\WithdrawalActionRequest;
use Marvel\Http\Resources\DeliveryAgentWithdrawalResource;
use Marvel\Enums\Permission;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;
use Marvel\Exceptions\DeliveryException;

class AgentWithdrawalController extends CoreController
{
    public $repository;

    public function __construct(DeliveryAgentWithdrawalRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Display a listing of the withdrawals.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        
        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $limit = $request->query('limit', 15);
            $status = $request->query('status');
            $agent_id = $request->query('agent_id');

            $query = DeliveryAgentWithdrawal::with(['deliveryAgent', 'processedBy']);

            if ($status) {
                $query->where('status', $status);
            }

            if ($agent_id) {
                $query->where('delivery_agent_user_id', $agent_id);
            }

            $withdrawals = $query->orderBy('created_at', 'desc')->paginate($limit);

            return response()->json(DeliveryAgentWithdrawalResource::collection($withdrawals));
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to retrieve withdrawals', $e->getMessage());
        }
    }

    /**
     * Display the specified withdrawal.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function show(Request $request, $id): JsonResponse
    {
        $user = $request->user();
        
        if (!$user || !($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER))) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $withdrawal = DeliveryAgentWithdrawal::with(['deliveryAgent', 'processedBy'])
                ->findOrFail($id);

            return response()->json(new DeliveryAgentWithdrawalResource($withdrawal));
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::WITHDRAWAL_NOT_FOUND);
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError('Failed to retrieve withdrawal', $e->getMessage());
        }
    }

    /**
     * Approve a withdrawal request.
     *
     * @param WithdrawalActionRequest $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function approve(WithdrawalActionRequest $request, $id): JsonResponse
    {
        $user = $request->user();

        // Admin only action
        if (!$user || !$user->hasPermissionTo(Permission::SUPER_ADMIN)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $withdrawal = DeliveryAgentWithdrawal::findOrFail($id);
            $data = $request->validated();
            $withdrawal = $this->repository->approveWithdrawal($withdrawal, $user, $data);

            return response()->json([
                'message' => DeliveryConstants::WITHDRAWAL_APPROVED,
                'withdrawal' => new DeliveryAgentWithdrawalResource($withdrawal->load(['deliveryAgent', 'processedBy'])),
            ]);
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::WITHDRAWAL_NOT_FOUND);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to approve withdrawal', $e->getMessage());
        }
    }

    /**
     * Process a withdrawal request.
     *
     * @param WithdrawalActionRequest $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function process(WithdrawalActionRequest $request, $id): JsonResponse
    {
        $user = $request->user();

        // Admin only action
        if (!$user || !$user->hasPermissionTo(Permission::SUPER_ADMIN)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $withdrawal = DeliveryAgentWithdrawal::findOrFail($id);
            $data = $request->validated();
            $withdrawal = $this->repository->processWithdrawal($withdrawal, $user, $data);

            return response()->json([
                'message' => DeliveryConstants::WITHDRAWAL_PROCESSED,
                'withdrawal' => new DeliveryAgentWithdrawalResource($withdrawal->load(['deliveryAgent', 'processedBy'])),
            ]);
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::WITHDRAWAL_NOT_FOUND);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to process withdrawal', $e->getMessage());
        }
    }

    /**
     * Complete a withdrawal request.
     *
     * @param WithdrawalActionRequest $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function complete(WithdrawalActionRequest $request, $id): JsonResponse
    {
        $user = $request->user();

        // Admin only action
        if (!$user || !$user->hasPermissionTo(Permission::SUPER_ADMIN)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $withdrawal = DeliveryAgentWithdrawal::findOrFail($id);
            $data = $request->validated();
            $withdrawal = $this->repository->completeWithdrawal($withdrawal, $user, $data);

            return response()->json([
                'message' => DeliveryConstants::WITHDRAWAL_COMPLETED,
                'withdrawal' => new DeliveryAgentWithdrawalResource($withdrawal->load(['deliveryAgent', 'processedBy'])),
            ]);
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::WITHDRAWAL_NOT_FOUND);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to complete withdrawal', $e->getMessage());
        }
    }

    /**
     * Reject a withdrawal request.
     *
     * @param WithdrawalActionRequest $request
     * @param int $id
     * @return JsonResponse
     * @throws DeliveryException
     */
    public function reject(WithdrawalActionRequest $request, $id): JsonResponse
    {
        $user = $request->user();

        // Admin only action
        if (!$user || !$user->hasPermissionTo(Permission::SUPER_ADMIN)) {
            DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
        }

        try {
            $withdrawal = DeliveryAgentWithdrawal::findOrFail($id);
            $data = $request->validated();
            $withdrawal = $this->repository->rejectWithdrawal($withdrawal, $user, $data);

            return response()->json([
                'message' => DeliveryConstants::WITHDRAWAL_REJECTED,
                'withdrawal' => new DeliveryAgentWithdrawalResource($withdrawal->load(['deliveryAgent', 'processedBy'])),
            ]);
        } catch (ModelNotFoundException $e) {
            DeliveryExceptionHelper::notFound(DeliveryConstants::WITHDRAWAL_NOT_FOUND);
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError('Failed to reject withdrawal', $e->getMessage());
        }
    }
}
