<?php

namespace Marvel\Http\Controllers;

use Illuminate\Support\Facades\Log;
use Marvel\Database\Models\Attachment;
use Marvel\Http\Requests\VideoUploadRequest;
use Prettus\Validator\Exceptions\ValidatorException;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;

class VideoController extends CoreController
{
    /**
     * Store a newly created video in storage.
     *
     * @param VideoUploadRequest $request
     * @return mixed
     * @throws ValidatorException
     */
    public function store(VideoUploadRequest $request)
    {
        try {
            $video = $request->file('video');
            $attachment = new Attachment;
            $attachment->save();
            $mediaItem = $attachment->addMedia($video)->toMediaCollection('videos');

            return [
                'id'          => $attachment->id,
                'original'    => $mediaItem->getUrl(),
                'thumbnail'   => $mediaItem->getUrl('thumbnail'),
                'mime_type'   => $mediaItem->mime_type,
            ];
        } catch (\Exception $e) {
            Log::error($e);
            DeliveryExceptionHelper::internalError(DeliveryConstants::VIDEO_UPLOAD_FAILED, $e->getMessage());
        }
    }
}