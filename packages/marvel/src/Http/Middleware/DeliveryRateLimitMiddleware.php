<?php

namespace Marvel\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\JsonResponse;

class DeliveryRateLimitMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $operation
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $operation = 'default')
    {
        $user = $request->user();
        if (!$user) {
            return $next($request);
        }

        $limits = $this->getRateLimits($operation);
        $key = $this->getRateLimitKey($user->id, $operation, $request);

        $executed = RateLimiter::attempt(
            $key,
            $limits['max_attempts'],
            function () use ($next, $request) {
                return $next($request);
            },
            $limits['decay_minutes'] * 60
        );

        if (!$executed) {
            return $this->buildRateLimitResponse($key, $limits);
        }

        return $executed;
    }

    /**
     * Get rate limits for different operations
     *
     * @param string $operation
     * @return array
     */
    private function getRateLimits(string $operation): array
    {
        $limits = [
            'location_update' => [
                'max_attempts' => 60, // 60 updates per minute
                'decay_minutes' => 1,
            ],
            'status_update' => [
                'max_attempts' => 10, // 10 status changes per minute
                'decay_minutes' => 1,
            ],
            'delivery_assignment' => [
                'max_attempts' => 20, // 20 assignments per minute
                'decay_minutes' => 1,
            ],
            'delivery_status_update' => [
                'max_attempts' => 30, // 30 status updates per minute
                'decay_minutes' => 1,
            ],
            'proof_of_delivery' => [
                'max_attempts' => 5, // 5 POD submissions per minute
                'decay_minutes' => 1,
            ],
            'default' => [
                'max_attempts' => 100, // General API limit
                'decay_minutes' => 1,
            ],
        ];

        return $limits[$operation] ?? $limits['default'];
    }

    /**
     * Generate rate limit key
     *
     * @param int $userId
     * @param string $operation
     * @param Request $request
     * @return string
     */
    private function getRateLimitKey(int $userId, string $operation, Request $request): string
    {
        $ip = $request->ip();
        return "delivery_rate_limit:{$operation}:{$userId}:{$ip}";
    }

    /**
     * Build rate limit exceeded response
     *
     * @param string $key
     * @param array $limits
     * @return JsonResponse
     */
    private function buildRateLimitResponse(string $key, array $limits): JsonResponse
    {
        $retryAfter = RateLimiter::availableIn($key);
        $remaining = RateLimiter::remaining($key, $limits['max_attempts']);

        return Response::json([
            'message' => 'Too many requests. Please slow down.',
            'error' => 'RATE_LIMIT_EXCEEDED',
            'retry_after' => $retryAfter,
            'limit' => $limits['max_attempts'],
            'remaining' => $remaining,
        ], 429)->header('Retry-After', $retryAfter);
    }
}

/**
 * Middleware aliases for different operations
 */
class LocationUpdateRateLimit extends DeliveryRateLimitMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        return parent::handle($request, $next, 'location_update');
    }
}

class StatusUpdateRateLimit extends DeliveryRateLimitMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        return parent::handle($request, $next, 'status_update');
    }
}

class DeliveryAssignmentRateLimit extends DeliveryRateLimitMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        return parent::handle($request, $next, 'delivery_assignment');
    }
}

class DeliveryStatusUpdateRateLimit extends DeliveryRateLimitMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        return parent::handle($request, $next, 'delivery_status_update');
    }
}

class ProofOfDeliveryRateLimit extends DeliveryRateLimitMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        return parent::handle($request, $next, 'proof_of_delivery');
    }
}
