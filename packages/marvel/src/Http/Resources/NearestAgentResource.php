<?php

namespace Marvel\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NearestAgentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'distance' => round($this->distance, 2), // Distance in kilometers
            'delivery_agent_profile' => $this->whenLoaded('delivery_agent_profile', function () {
                return [
                    'id' => $this->delivery_agent_profile->id,
                    'availability_status' => $this->delivery_agent_profile->availability_status,
                    'current_location' => $this->delivery_agent_profile->current_location,
                    'kyc_status' => $this->delivery_agent_profile->kyc_status,
                    'performance_rating' => $this->delivery_agent_profile->performance_rating,
                    'total_deliveries_completed' => $this->delivery_agent_profile->total_deliveries_completed,
                    'last_check_in_at' => $this->delivery_agent_profile->last_check_in_at,
                ];
            }),
            'profile' => $this->whenLoaded('profile', function () {
                return [
                    'avatar' => $this->profile->avatar ?? null,
                    'contact' => $this->profile->contact ?? null,
                ];
            }),
        ];
    }
}
