<?php

namespace Marvel\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryAgentTransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'delivery_agent_user_id' => $this->delivery_agent_user_id,
            'delivery_agent' => $this->whenLoaded('deliveryAgent', function () {
                return [
                    'id' => $this->deliveryAgent->id,
                    'name' => $this->deliveryAgent->name,
                    'email' => $this->deliveryAgent->email,
                    'profile' => $this->deliveryAgent->profile,
                ];
            }),
            'transaction_type' => $this->transaction_type,
            'reference_type' => $this->reference_type,
            'reference_id' => $this->reference_id,
            'amount' => $this->amount,
            'status' => $this->status,
            'payment_details' => $this->payment_details,
            'payment_method' => $this->payment_method,
            'provider' => $this->provider,
            'transaction_id' => $this->transaction_id,
            'notes' => $this->notes,
            'created_by_user_id' => $this->created_by_user_id,
            'created_by' => $this->whenLoaded('createdBy', function () {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->name,
                ];
            }),
            'processed_at' => $this->processed_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
