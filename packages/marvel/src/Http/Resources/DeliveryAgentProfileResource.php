<?php

namespace Marvel\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryAgentProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                    'email' => $this->user->email,
                    'profile' => $this->user->profile,
                ];
            }),
            'phone_number' => $this->phone_number,
            'availability_status' => $this->availability_status,
            'current_location' => $this->current_location,
            'kyc_status' => $this->kyc_status,
            'kyc_documents' => $this->kyc_documents,
            'kyc_rejection_reason' => $this->kyc_rejection_reason,
            'active_vehicle_id' => $this->active_vehicle_id,
            'use_wallet_for_earnings' => $this->use_wallet_for_earnings,
            'wallet_points_conversion_rate' => $this->wallet_points_conversion_rate,
            'active_vehicle' => $this->whenLoaded('activeVehicle'),
            'performance_rating' => $this->performance_rating,
            'total_deliveries_completed' => $this->total_deliveries_completed,
            'last_check_in_at' => $this->last_check_in_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'profile_photo_url' => $this->getFirstMediaUrl('profile_photo'),
            'kyc_media' => [
                'kyc_id_front' => $this->getFirstMediaUrl('kyc_id_front'),
                'kyc_id_back' => $this->getFirstMediaUrl('kyc_id_back'),
                'kyc_license_front' => $this->getFirstMediaUrl('kyc_license_front'),
                'kyc_license_back' => $this->getFirstMediaUrl('kyc_license_back'),
                'kyc_address_proof' => $this->getFirstMediaUrl('kyc_address_proof'),
                'kyc_passport' => $this->getFirstMediaUrl('kyc_passport'),
            ],
        ];
    }
}
