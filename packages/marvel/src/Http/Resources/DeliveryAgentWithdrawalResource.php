<?php

namespace Marvel\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryAgentWithdrawalResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'delivery_agent_user_id' => $this->delivery_agent_user_id,
            'delivery_agent' => $this->whenLoaded('deliveryAgent', function () {
                return [
                    'id' => $this->deliveryAgent->id,
                    'name' => $this->deliveryAgent->name,
                    'email' => $this->deliveryAgent->email,
                    'profile' => $this->deliveryAgent->profile,
                ];
            }),
            'amount' => $this->amount,
            'status' => $this->status,
            'payment_method_details' => $this->payment_method_details,
            'transaction_reference' => $this->transaction_reference,
            'processed_by_user_id' => $this->processed_by_user_id,
            'processed_by' => $this->whenLoaded('processedBy', function () {
                return [
                    'id' => $this->processedBy->id,
                    'name' => $this->processedBy->name,
                ];
            }),
            'notes' => $this->notes,
            'requested_at' => $this->requested_at,
            'processed_at' => $this->processed_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
