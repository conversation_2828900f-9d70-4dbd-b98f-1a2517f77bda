<?php

namespace Marvel\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VehicleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'delivery_agent_user_id' => $this->delivery_agent_user_id,
            'type' => $this->type,
            'make' => $this->make,
            'model' => $this->model,
            'registration_number' => $this->registration_number,
            'color' => $this->color,
            'vehicle_documents' => $this->vehicle_documents,
            'is_verified' => $this->is_verified,
            'verification_notes' => $this->verification_notes,
            'is_active' => $this->is_active,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'media' => [
                'vehicle_registration' => $this->getFirstMediaUrl('vehicle_registration'),
                'vehicle_insurance' => $this->getFirstMediaUrl('vehicle_insurance'),
                'vehicle_images' => $this->getMedia('vehicle_images')->map(function ($media) {
                    return [
                        'id' => $media->id,
                        'url' => $media->getFullUrl(),
                        // 'thumbnail' => $media->getFullUrl('thumbnail'),
                    ];
                }),
            ],
        ];
    }
}
