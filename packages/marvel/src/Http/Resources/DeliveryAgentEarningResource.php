<?php

namespace Marvel\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryAgentEarningResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'delivery_agent_user_id' => $this->delivery_agent_user_id,
            'delivery_agent' => $this->whenLoaded('deliveryAgent', function () {
                return [
                    'id' => $this->deliveryAgent->id,
                    'name' => $this->deliveryAgent->name,
                    'email' => $this->deliveryAgent->email,
                    'profile' => $this->deliveryAgent->profile,
                ];
            }),
            'total_earnings' => $this->total_earnings,
            'withdrawn_amount' => $this->withdrawn_amount,
            'current_balance' => $this->current_balance,
            'pending_withdrawal_amount' => $this->pending_withdrawal_amount,
            'payment_info' => $this->payment_info,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
