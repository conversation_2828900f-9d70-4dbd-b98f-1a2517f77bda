<?php

namespace Marvel\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WalletResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'total_points' => $this->total_points,
            'points_used' => $this->points_used,
            'available_points' => $this->available_points,
            'user_id' => $this->customer_id, // Using customer_id field from database but exposing as user_id
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
