<?php

namespace Marvel\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'order_id' => $this->order_id,
            'order' => $this->whenLoaded('order', function () {
                return new OrderSummaryResource($this->order);
            }),
            'delivery_agent_user_id' => $this->delivery_agent_user_id,
            'delivery_agent' => $this->whenLoaded('deliveryAgent', function () {
                return [
                    'id' => $this->deliveryAgent->id,
                    'name' => $this->deliveryAgent->name,
                    'email' => $this->deliveryAgent->email,
                    'profile' => $this->deliveryAgent->profile,
                    'delivery_agent_profile' => $this->deliveryAgent->delivery_agent_profile ? [
                        'id' => $this->deliveryAgent->delivery_agent_profile->id,
                        'availability_status' => $this->deliveryAgent->delivery_agent_profile->availability_status,
                        'current_location' => $this->deliveryAgent->delivery_agent_profile->current_location,
                        'performance_rating' => $this->deliveryAgent->delivery_agent_profile->performance_rating,
                    ] : null,
                ];
            }),
            'status' => $this->status,
            'pickup_address' => $this->pickup_address,
            'delivery_address' => $this->delivery_address,
            'estimated_delivery_time' => $this->estimated_delivery_time,
            'assigned_at' => $this->assigned_at,
            'accepted_at' => $this->accepted_at,
            'picked_up_at' => $this->picked_up_at,
            'reached_destination_at' => $this->reached_destination_at,
            'delivered_at' => $this->delivered_at,
            'completed_at' => $this->completed_at,
            'cancelled_at' => $this->cancelled_at,
            'cancellation_reason' => $this->cancellation_reason,
            'failed_at' => $this->failed_at,
            'failure_reason' => $this->failure_reason,
            'delivery_fee' => $this->delivery_fee,
            'notes_by_agent' => $this->notes_by_agent,
            'notes_by_admin' => $this->notes_by_admin,
            'proof_of_delivery' => $this->proof_of_delivery,
            'pod_type' => $this->pod_type,
            'assigned_by_user_id' => $this->assigned_by_user_id,
            'assigned_by' => $this->whenLoaded('assignedBy', function () {
                return [
                    'id' => $this->assignedBy->id,
                    'name' => $this->assignedBy->name,
                ];
            }),
            'assignment_type' => $this->assignment_type,
            'status_logs' => $this->whenLoaded('statusLogs', function () {
                return DeliveryStatusLogResource::collection($this->statusLogs);
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'pod_media' => $this->getFirstMediaUrl('proof_of_delivery'),
        ];
    }
}
