<?php

namespace Marvel\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Marvel\Enums\Permission;

class ProofOfDeliveryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return $this->user() && $this->user()->hasPermissionTo(Permission::DELIVERY_AGENT);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'pod_type' => ['required', Rule::in(['SIGNATURE', 'PHOTO', 'CODE', 'NONE'])],
            'proof_of_delivery' => 'nullable|array',
            'pod_image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'signature_data' => 'nullable|string',
            'code' => 'nullable|string',
        ];
    }
}
