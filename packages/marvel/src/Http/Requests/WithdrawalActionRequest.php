<?php

namespace Marvel\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Marvel\Enums\Permission;

class WithdrawalActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return $this->user() && ($this->user()->hasPermissionTo(Permission::SUPER_ADMIN) || $this->user()->hasPermissionTo(Permission::STORE_OWNER));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'notes' => 'nullable|string',
        ];
        
        // Add transaction_reference validation for complete action
        if ($this->route()->getName() === 'agent-withdrawals.complete') {
            $rules['transaction_reference'] = 'required|string';
        }
        
        // Add notes validation for reject action
        if ($this->route()->getName() === 'agent-withdrawals.reject') {
            $rules['notes'] = 'required|string';
        }
        
        return $rules;
    }
}
