<?php

namespace Marvel\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Marvel\Enums\Permission;

class UpdateAgentLocationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return $this->user() && $this->user()->hasPermissionTo(Permission::DELIVERY_AGENT);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'location' => 'required|array',
            'location.lat' => [
                'required',
                'numeric',
                'between:-90,90',
                function ($attribute, $value, $fail) {
                    // Exclude null island coordinates (0,0)
                    if ($value == 0 && $this->input('location.lng') == 0) {
                        $fail('Invalid coordinates: null island location not allowed.');
                    }
                },
            ],
            'location.lng' => [
                'required',
                'numeric',
                'between:-180,180',
            ],
            'status' => [
                'nullable',
                Rule::in(['ONLINE', 'OFFLINE', 'BUSY'])
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'location.required' => 'Location data is required.',
            'location.array' => 'Location must be an object with lat and lng properties.',
            'location.lat.required' => 'Latitude is required.',
            'location.lat.numeric' => 'Latitude must be a valid number.',
            'location.lat.between' => 'Latitude must be between -90 and 90 degrees.',
            'location.lng.required' => 'Longitude is required.',
            'location.lng.numeric' => 'Longitude must be a valid number.',
            'location.lng.between' => 'Longitude must be between -180 and 180 degrees.',
            'status.in' => 'Status must be one of: ONLINE, OFFLINE, BUSY.',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Additional validation for coordinate precision
            $lat = $this->input('location.lat');
            $lng = $this->input('location.lng');
            
            if ($lat !== null && $lng !== null) {
                // Check for reasonable precision (not more than 8 decimal places)
                if (strlen(substr(strrchr($lat, "."), 1)) > 8) {
                    $validator->errors()->add('location.lat', 'Latitude precision too high (max 8 decimal places).');
                }
                
                if (strlen(substr(strrchr($lng, "."), 1)) > 8) {
                    $validator->errors()->add('location.lng', 'Longitude precision too high (max 8 decimal places).');
                }
                
                // Check for suspicious patterns (e.g., repeated digits)
                if (preg_match('/(\d)\1{5,}/', str_replace('.', '', $lat))) {
                    $validator->errors()->add('location.lat', 'Latitude appears to be invalid (suspicious pattern detected).');
                }
                
                if (preg_match('/(\d)\1{5,}/', str_replace('.', '', $lng))) {
                    $validator->errors()->add('location.lng', 'Longitude appears to be invalid (suspicious pattern detected).');
                }
            }
        });
    }
}
