<?php

namespace Marvel\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Marvel\Enums\Permission;

class KYCActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return $this->user() && ($this->user()->hasPermissionTo(Permission::SUPER_ADMIN) || $this->user()->hasPermissionTo(Permission::STORE_OWNER));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'notes' => 'nullable|string',
        ];
        
        // Add rejection_reason validation for reject action
        if ($this->route()->getName() === 'agents.reject-kyc') {
            $rules['rejection_reason'] = 'required|string';
        }
        
        return $rules;
    }
}
