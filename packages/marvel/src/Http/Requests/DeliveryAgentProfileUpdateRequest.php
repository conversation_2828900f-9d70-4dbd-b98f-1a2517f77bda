<?php

namespace Marvel\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Marvel\Enums\DeliveryAgentAvailabilityStatus; // Assuming this enum exists
use Illuminate\Validation\Rule;
use Marvel\Enums\Permission;

class DeliveryAgentProfileUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'nullable|string|max:255', // Rule for updating the user's name
            'profile_photo' => 'nullable|file|mimes:jpeg,png,jpg|image|max:8192', 
            'phone_number' => 'nullable|string|max:255',
            'current_location' => 'nullable|array',
            'current_location.lat' => 'nullable|numeric|between:-90,90',
            'current_location.lng' => 'nullable|numeric|between:-180,180',
            'kyc_documents' => 'nullable|array',
            'kyc_id_front' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:8192',      
            'kyc_id_back' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:8192',       
            'kyc_license_front' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:8192', 
            'kyc_license_back' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:8192',  
            'kyc_address_proof' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:8192', 
            'kyc_passport' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:8192',
            'use_wallet_for_earnings' => 'nullable|boolean',
        ];
    }
}
