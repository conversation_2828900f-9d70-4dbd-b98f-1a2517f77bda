<?php

namespace Marvel\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Marvel\Enums\Permission;

class AvailabilityUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'availability_status' => ['required', Rule::in(['ONLINE', 'OFFLINE', 'ON_DELIVERY'])],
            'current_location' => 'nullable|array',
            'current_location.lat' => 'nullable|numeric',
            'current_location.lng' => 'nullable|numeric',
        ];
    }
}
