<?php

namespace Marvel\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Marvel\Enums\Permission;

class VehicleCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'type' => ['required', Rule::in(['BIKE', 'CAR', 'VAN', 'OTHER'])],
            'make' => 'nullable|string|max:255',
            'model' => 'nullable|string|max:255',
            'registration_number' => 'required|string|max:255|unique:vehicles,registration_number',
            'color' => 'nullable|string|max:255',
            'vehicle_documents' => 'nullable|array',
            'vehicle_registration' => 'required|mimes:jpeg,png,jpg,pdf|max:2048',
            'vehicle_insurance' => 'required|mimes:jpeg,png,jpg,pdf|max:2048',
            'vehicle_images.*' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ];
    }
}
