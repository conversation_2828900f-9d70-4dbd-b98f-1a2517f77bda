<?php

namespace Marvel\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Marvel\Enums\Permission;

class LocationUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return $this->user() && $this->user()->hasPermissionTo(Permission::DELIVERY_AGENT);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'lat' => 'required|numeric|between:-90,90',
            'lng' => 'required|numeric|between:-180,180',
            'timestamp' => 'nullable|date',
            'accuracy' => 'nullable|numeric|min:0', // GPS accuracy in meters
            'speed' => 'nullable|numeric|min:0', // Speed in km/h
            'heading' => 'nullable|numeric|between:0,360', // Direction in degrees
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'lat.required' => 'Latitude is required',
            'lat.numeric' => 'Latitude must be a valid number',
            'lat.between' => 'Latitude must be between -90 and 90 degrees',
            'lng.required' => 'Longitude is required',
            'lng.numeric' => 'Longitude must be a valid number',
            'lng.between' => 'Longitude must be between -180 and 180 degrees',
            'timestamp.date' => 'Timestamp must be a valid date',
            'accuracy.numeric' => 'Accuracy must be a valid number',
            'accuracy.min' => 'Accuracy cannot be negative',
            'speed.numeric' => 'Speed must be a valid number',
            'speed.min' => 'Speed cannot be negative',
            'heading.numeric' => 'Heading must be a valid number',
            'heading.between' => 'Heading must be between 0 and 360 degrees',
        ];
    }
}
