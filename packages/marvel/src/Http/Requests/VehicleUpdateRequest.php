<?php

namespace Marvel\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Marvel\Enums\Permission;

class VehicleUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'type' => ['nullable', Rule::in(['BIKE', 'CAR', 'VAN', 'OTHER'])],
            'make' => 'nullable|string|max:255',
            'model' => 'nullable|string|max:255',
            'registration_number' => [
                'nullable',
                'string',
                'max:255',
                Rule::unique('vehicles', 'registration_number')->ignore($this->route('vehicle')),
            ],
            'color' => 'nullable|string|max:255',
            'vehicle_documents' => 'nullable|array',
            'vehicle_registration' => 'nullable|mimes:jpeg,png,jpg,pdf|max:2048',
            'vehicle_insurance' => 'nullable|mimes:jpeg,png,jpg,pdf|max:2048',
            'vehicle_images.*' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'is_active' => 'nullable|boolean',
        ];
    }
}
