<?php

namespace Marvel\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Marvel\Enums\Permission;

class AssignDeliveryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return $this->user() && ($this->user()->hasPermissionTo(Permission::SUPER_ADMIN) || $this->user()->hasPermissionTo(Permission::STORE_OWNER));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'delivery_agent_user_id' => 'required|exists:users,id',
            'delivery_fee' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ];
    }
}
