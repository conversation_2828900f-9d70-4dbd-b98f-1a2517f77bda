<?php

namespace Marvel\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;


class ManufacturerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', 'string'],
            'slug'        => ['nullable', 'string'],
            'description' => ['nullable', 'string', 'max:10000'],
            'type_id' => ['required', 'exists:Marvel\Database\Models\Type,id'],
            'shop_id'     => ['nullable', 'exists:Marvel\Database\Models\Shop,id'],
            'image' => ['array'],
            'language'     => ['nullable', 'string'],
            'cover_image' => ['array'],
            'is_approved' => ['boolean'],
        ];
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json($validator->errors(), 422));
    }
}
