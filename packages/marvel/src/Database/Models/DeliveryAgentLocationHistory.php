<?php

namespace Marvel\Database\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryAgentLocationHistory extends Model
{
    protected $table = 'delivery_agent_location_history';

    public $timestamps = false;

    protected $fillable = [
        'delivery_agent_user_id',
        'location',
        'timestamp',
        'delivery_id',
        'created_at',
    ];

    protected $casts = [
        'location' => 'array',
        'timestamp' => 'datetime',
        'created_at' => 'datetime',
    ];

    /**
     * @return BelongsTo
     */
    public function deliveryAgent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'delivery_agent_user_id');
    }

    /**
     * @return BelongsTo
     */
    public function delivery(): BelongsTo
    {
        return $this->belongsTo(Delivery::class, 'delivery_id');
    }
}
