<?php

namespace Marvel\Database\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class DeliveryAgentWithdrawal extends Model
{
    use SoftDeletes;

    protected $table = 'delivery_agent_withdrawals';

    protected $fillable = [
        'delivery_agent_user_id',
        'amount',
        'status',
        'payment_method_details',
        'transaction_reference',
        'processed_by_user_id',
        'notes',
        'requested_at',
        'processed_at',
    ];

    protected $casts = [
        'payment_method_details' => 'json',
        'amount' => 'float',
        'requested_at' => 'datetime',
        'processed_at' => 'datetime',
    ];

    /**
     * @return BelongsTo
     */
    public function deliveryAgent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'delivery_agent_user_id');
    }

    /**
     * @return BelongsTo
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by_user_id');
    }
}
