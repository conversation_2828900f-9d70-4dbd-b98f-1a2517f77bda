<?php

namespace Marvel\Database\Models;

use Illuminate\Database\Eloquent\Model;
use C<PERSON>brock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Builder;
use Marvel\Traits\TranslationTrait;

class RefundReason extends Model
{
    use Sluggable;
    use TranslationTrait;


    protected $table = 'refund_reasons';

    public $guarded = [];

    protected $appends = ['translated_languages'];

    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'name'
            ]
        ];
    }

    public function scopeWithUniqueSlugConstraints(Builder $query, Model $model): Builder
    {
        return $query->where('language', $model->language);
    }
}
