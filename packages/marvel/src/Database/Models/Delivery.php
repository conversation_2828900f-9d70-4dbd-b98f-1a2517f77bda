<?php

namespace Marvel\Database\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Delivery extends Model implements HasMedia
{
    use HasFactory, SoftDeletes, InteractsWithMedia;

    protected $table = 'deliveries';

    protected $fillable = [
        'order_id',
        'delivery_agent_user_id',
        'status',
        'pickup_address',
        'delivery_address',
        'estimated_delivery_time',
        'assigned_at',
        'accepted_at',
        'picked_up_at',
        'reached_destination_at',
        'delivered_at',
        'completed_at',
        'cancelled_at',
        'cancellation_reason',
        'failed_at',
        'failure_reason',
        'delivery_fee',
        'notes_by_agent',
        'notes_by_admin',
        'proof_of_delivery',
        'pod_type',
        'assigned_by_user_id',
        'assignment_type',
    ];

    protected $casts = [
        'pickup_address' => 'json',
        'delivery_address' => 'json',
        'proof_of_delivery' => 'json',
        'estimated_delivery_time' => 'datetime',
        'assigned_at' => 'datetime',
        'accepted_at' => 'datetime',
        'picked_up_at' => 'datetime',
        'reached_destination_at' => 'datetime',
        'delivered_at' => 'datetime',
        'completed_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'failed_at' => 'datetime',
        'delivery_fee' => 'float',
    ];

    /**
     * @return BelongsTo
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    /**
     * @return BelongsTo
     */
    public function deliveryAgent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'delivery_agent_user_id');
    }

    /**
     * @return BelongsTo
     */
    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by_user_id');
    }

    /**
     * @return HasMany
     */
    public function statusLogs(): HasMany
    {
        return $this->hasMany(DeliveryStatusLog::class, 'delivery_id');
    }

    /**
     * Register media collections
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('proof_of_delivery')
            ->singleFile();
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return \Marvel\Database\Factories\DeliveryFactory::new();
    }
}
