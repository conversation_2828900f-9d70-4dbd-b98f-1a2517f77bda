<?php

namespace Marvel\Database\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryAgentAssignmentState extends Model
{
    protected $table = 'delivery_agent_assignment_state';

    protected $fillable = [
        'region_key',
        'last_assigned_agent_id',
        'assignment_count',
        'last_assignment_at',
    ];

    protected $casts = [
        'last_assignment_at' => 'datetime',
    ];

    /**
     * @return BelongsTo
     */
    public function lastAssignedAgent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'last_assigned_agent_id');
    }
}
