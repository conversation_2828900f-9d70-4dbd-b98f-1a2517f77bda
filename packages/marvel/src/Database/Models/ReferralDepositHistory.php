<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Marvel\Database\Models\AccountBalance;

class ReferralDepositHistory extends Model
{
    use HasFactory;

    protected $table = "referral_deposit_history";

    protected $fillable = ['account_balance_id' , 'referree_id', 'description' , 'amount'];

     public function accountBalance(): BelongsTo {
        return $this->belongsTo(AccountBalance::class);
    }

     public function referree(): BelongsTo {
        return $this->belongsTo(User::class , 'referree_id');
    }
}
