<?php

namespace Marvel\Database\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Marvel\Database\Models\Wallet;

class DeliveryAgentProfile extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia, HasFactory;

    protected $table = 'delivery_agent_profiles';

    protected $fillable = [
        'user_id',
        'phone_number',
        'availability_status',
        'current_location',
        'kyc_status',
        'kyc_documents',
        'kyc_rejection_reason',
        'active_vehicle_id',
        'use_wallet_for_earnings',
        'wallet_points_conversion_rate',
        'performance_rating',
        'total_deliveries_completed',
        'last_check_in_at',
    ];

    protected $casts = [
        'kyc_documents' => 'json',
        'current_location' => 'array',
        'last_check_in_at' => 'datetime',
        'use_wallet_for_earnings' => 'boolean',
        'wallet_points_conversion_rate' => 'float',
    ];

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return BelongsTo
     */
    public function activeVehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class, 'active_vehicle_id');
    }

    /**
     * @return HasMany
     */
    public function vehicles(): HasMany
    {
        return $this->hasMany(Vehicle::class, 'delivery_agent_user_id', 'user_id');
    }

    /**
     * @return HasOne
     */
    public function earnings(): HasOne
    {
        return $this->hasOne(DeliveryAgentEarning::class, 'delivery_agent_user_id', 'user_id');
    }

    /**
     * @return HasOne
     */
    public function wallet(): HasOne
    {
        return $this->hasOne(Wallet::class, 'customer_id', 'user_id');
    }

    /**
     * @return HasMany
     */
    public function deliveries(): HasMany
    {
        return $this->hasMany(Delivery::class, 'delivery_agent_user_id', 'user_id');
    }

    /**
     * @return HasMany
     */
    public function locationHistory(): HasMany
    {
        return $this->hasMany(DeliveryAgentLocationHistory::class, 'delivery_agent_user_id', 'user_id');
    }

    /**
     * @return HasMany
     */
    public function withdrawals(): HasMany
    {
        return $this->hasMany(DeliveryAgentWithdrawal::class, 'delivery_agent_user_id', 'user_id');
    }

    /**
     * Register media collections
     */
    public function registerMediaCollections(): void
    {
        // Profile photo
        $this->addMediaCollection('profile_photo')
             ->singleFile(); // Only one profile photo per agent

        // For potentially two-sided ID card
        $this->addMediaCollection('kyc_id_front')
             ->singleFile(); // Store only the front image here
        $this->addMediaCollection('kyc_id_back')
             ->singleFile(); // Store only the back image here

        // For potentially two-sided Driver's License
        $this->addMediaCollection('kyc_license_front')
             ->singleFile(); // Store only the front image here
        $this->addMediaCollection('kyc_license_back')
             ->singleFile(); // Store only the back image here

        // For typically one-sided Address Proof (e.g., utility bill)
        $this->addMediaCollection('kyc_address_proof')
             ->singleFile(); // Only one collection needed

        // For single-sided Passport
        $this->addMediaCollection('kyc_passport')
             ->singleFile(); // Only one passport document
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory()
    {
        return \Marvel\Database\Factories\DeliveryAgentProfileFactory::new();
    }
}
