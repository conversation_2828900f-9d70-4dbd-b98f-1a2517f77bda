<?php


namespace Marvel\Database\Models;

use App\Models\TypeTranslations;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\hasMany;
use C<PERSON>brock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Marvel\Database\Factories\TypeFactory;
use Marvel\Traits\TranslationTrait;

class Type extends Model
{

    use Sluggable;
    use TranslationTrait;
    use HasFactory;

    protected $appends = ['translated_languages'];

    protected $table = 'types';

    public $guarded = [];

    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'name',
            ]
        ];
    }

    public function scopeWithUniqueSlugConstraints(Builder $query, Model $model): Builder
    {
        return $query->where('language', $model->language);
    }

    protected $casts = [
        'promotional_sliders'   => 'json',
        'images' => 'json',
        'settings'   => 'json',
    ];

    /**
     * @return HasMany
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'type_id');
    }

    /**
     * @return HasMany
     */
    public function categories(): HasMany
    {
        return $this->hasMany(Category::class, 'type_id');
    }

    /**
     * @return HasMany
     */
    public function banners(): HasMany
    {
        return $this->hasMany(Banner::class, 'type_id');
    }
    protected static function newFactory()
    {
        return TypeFactory::new();
    }

    public function translations()
    {
        return $this->hasMany(TypeTranslations::class);
    }

    public function getTranslation($locale = null)
    {
        $locale = $locale ?? app()->getLocale();
        return $this->translations()->where('locale', $locale)->first();
    }

    public function translationByLocale($locale)
    {
        return $this->hasOne(TypeTranslations::class)->where('locale', $locale);
    }
}
