<?php

namespace Marvel\Database\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Marvel\Database\Factories\ShippingFactory;

class Shipping extends Model
{
    protected $table = 'shipping_classes';
    public $guarded = [];
    use HasFactory;

    protected static function boot()
    {
        parent::boot();
        // Order by updated_at desc
        static::addGlobalScope('order', function (Builder $builder) {
            $builder->orderBy('updated_at', 'desc');
        });
    }

    /**
     * @return HasMany
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'shipping_class_id');
    }
    protected static function newFactory()
    {
        return ShippingFactory::new();
    }
}
