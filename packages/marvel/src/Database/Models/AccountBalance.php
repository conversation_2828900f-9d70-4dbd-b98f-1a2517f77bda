<?php

namespace Marvel\Database\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AccountBalance extends Model
{
    use HasFactory;

    protected $table = "account_balance";

    protected $fillable = ['user_id' , 'current_balance', 'total_earnings', ];
    public function user(): BelongsTo {
        return $this->belongsTo(User::class);
    }

}
