<?php

namespace Marvel\Database\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Plan extends Model
{
    use HasFactory;

        protected $fillable = ['name' , 'price' , 'currency' , 'description', 'features'];

        protected $casts = ['features' => 'array'];
        public function subscriptions() {
            return $this->hasMany(Subscription::class);
        }
}
