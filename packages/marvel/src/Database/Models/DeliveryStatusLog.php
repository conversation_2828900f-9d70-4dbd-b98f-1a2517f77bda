<?php

namespace Marvel\Database\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryStatusLog extends Model
{
    protected $table = 'delivery_status_logs';

    public $timestamps = false;

    protected $fillable = [
        'delivery_id',
        'status',
        'user_id',
        'notes',
        'location',
        'created_at',
    ];

    protected $casts = [
        'location' => 'array',
        'created_at' => 'datetime',
    ];

    /**
     * @return BelongsTo
     */
    public function delivery(): BelongsTo
    {
        return $this->belongsTo(Delivery::class, 'delivery_id');
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
