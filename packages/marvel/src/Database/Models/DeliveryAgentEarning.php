<?php

namespace Marvel\Database\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeliveryAgentEarning extends Model
{
    protected $table = 'delivery_agent_earnings';

    protected $fillable = [
        'delivery_agent_user_id',
        'total_earnings',
        'withdrawn_amount',
        'current_balance',
        'pending_withdrawal_amount',
        'payment_info',
    ];

    protected $casts = [
        'payment_info' => 'json',
        'total_earnings' => 'float',
        'withdrawn_amount' => 'float',
        'current_balance' => 'float',
        'pending_withdrawal_amount' => 'float',
    ];

    /**
     * @return BelongsTo
     */
    public function deliveryAgent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'delivery_agent_user_id');
    }

    /**
     * @return HasMany
     */
    public function withdrawals(): HasMany
    {
        return $this->hasMany(DeliveryAgentWithdrawal::class, 'delivery_agent_user_id', 'delivery_agent_user_id');
    }
}
