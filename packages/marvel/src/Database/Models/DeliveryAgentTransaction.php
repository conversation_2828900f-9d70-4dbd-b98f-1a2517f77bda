<?php

namespace Marvel\Database\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class DeliveryAgentTransaction extends Model
{
    use SoftDeletes;

    protected $table = 'delivery_agent_transactions';

    protected $fillable = [
        'delivery_agent_user_id',
        'transaction_type',
        'reference_type',
        'reference_id',
        'amount',
        'status',
        'payment_details',
        'payment_method',
        'provider',
        'transaction_id',
        'notes',
        'created_by_user_id',
        'processed_at',
    ];

    protected $casts = [
        'payment_details' => 'json',
        'amount' => 'float',
        'processed_at' => 'datetime',
    ];

    /**
     * Transaction types
     */
    public const TYPE_EARNING = 'EARNING';
    public const TYPE_WITHDRAWAL = 'WITHDRAWAL';
    public const TYPE_REFUND = 'REFUND';
    public const TYPE_ADJUSTMENT = 'ADJUSTMENT';

    /**
     * Reference types
     */
    public const REF_DELIVERY = 'DELIVERY';
    public const REF_WITHDRAWAL = 'WITHDRAWAL';
    public const REF_MANUAL = 'MANUAL';

    /**
     * Status types
     */
    public const STATUS_PENDING = 'PENDING';
    public const STATUS_COMPLETED = 'COMPLETED';
    public const STATUS_FAILED = 'FAILED';
    public const STATUS_CANCELLED = 'CANCELLED';
    public const STATUS_REJECTED = 'REJECTED';

    /**
     * Payment methods
     */
    public const METHOD_MOBILE_MONEY = 'MOBILE_MONEY';
    public const METHOD_BANK_TRANSFER = 'BANK_TRANSFER';
    public const METHOD_CASH = 'CASH';
    public const METHOD_OTHER = 'OTHER';

    /**
     * @return BelongsTo
     */
    public function deliveryAgent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'delivery_agent_user_id');
    }

    /**
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }

    /**
     * Get the reference model based on reference_type
     */
    public function reference()
    {
        if ($this->reference_type === self::REF_DELIVERY && $this->reference_id) {
            return $this->belongsTo(Delivery::class, 'reference_id');
        } elseif ($this->reference_type === self::REF_WITHDRAWAL && $this->reference_id) {
            return $this->belongsTo(DeliveryAgentWithdrawal::class, 'reference_id');
        }

        return null;
    }
}
