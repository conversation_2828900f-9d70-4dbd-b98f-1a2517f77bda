<?php

namespace Marvel\Database\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Vehicle extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia;

    protected $table = 'vehicles';

    protected $fillable = [
        'delivery_agent_user_id',
        'type',
        'make',
        'model',
        'registration_number',
        'color',
        'vehicle_documents',
        'is_verified',
        'verification_notes',
        'is_active',
    ];

    protected $casts = [
        'vehicle_documents' => 'json',
        'is_verified' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * @return BelongsTo
     */
    public function deliveryAgent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'delivery_agent_user_id');
    }

    /**
     * Register media collections
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('vehicle_registration')
            ->singleFile();
        
        $this->addMediaCollection('vehicle_insurance')
            ->singleFile();
        
        $this->addMediaCollection('vehicle_images');
    }
}
