<?php

namespace Marvel\Database\Repositories;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\DeliveryStatusLog;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\User;
use Marvel\Enums\DeliveryStatus;
use Marvel\Enums\Permission;
use Marvel\Events\DeliveryAssigned;
use Marvel\Events\DeliveryStatusChanged;
use Marvel\Exceptions\DeliveryException;
use Marvel\Exceptions\MarvelException;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Exceptions\RepositoryException;
use Prettus\Validator\Exceptions\ValidatorException;
use Marvel\Services\RoutingCalculationService;
use Marvel\Services\AgentDiscoveryService; 

class DeliveryRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'order_id',
        'delivery_agent_user_id',
        'status',
        'assignment_type',
    ];

    /**
     * @var RoutingCalculationService
     */
    private RoutingCalculationService $routingService;
    private AgentDiscoveryService $agentDiscoveryService;

    /**
     * Configure the Model
     *
     * @return string
     */
    public function model()
    {
        return Delivery::class;
    }

    /**
     * DeliveryRepository constructor
     * 
     * @param RoutingCalculationService $routingService
     */
    public function __construct(RoutingCalculationService $routingService, AgentDiscoveryService $agentDiscoveryService)
    {
        parent::__construct(app());
        $this->routingService = $routingService;
        $this->agentDiscoveryService = $agentDiscoveryService;
    }

    /**
     * @throws RepositoryException
     */
    public function boot()
    {
        try {
            $this->pushCriteria(app(RequestCriteria::class));
        } catch (RepositoryException $e) {
            throw new RepositoryException($e);
        }
    }

    /**
     * Assign delivery to an agent
     *
     * @param array $data
     * @param Order $order
     * @param User $assignedBy
     * @return Delivery
     * @throws DeliveryException
     */
    public function assignDelivery(array $data, Order $order, User $assignedBy)
    {
        try {
            // Check if order already has a delivery
            if ($order->delivery_id) {
                DeliveryExceptionHelper::conflict(DeliveryConstants::ORDER_ALREADY_HAS_DELIVERY);
            }

            // Check if order requires delivery
            if (!$order->requires_delivery) {
                DeliveryExceptionHelper::badRequest(DeliveryConstants::ORDER_DOES_NOT_REQUIRE_DELIVERY);
            }

            // Check if agent exists and is a delivery agent
            $agent = User::find($data['delivery_agent_user_id']);
            if (!$agent || !$agent->hasPermissionTo(Permission::DELIVERY_AGENT)) {
                DeliveryExceptionHelper::badRequest(DeliveryConstants::INVALID_DELIVERY_AGENT);
            }

            // Check if agent has an approved profile
            $profile = $agent->delivery_agent_profile;
            if (!$profile || $profile->kyc_status !== 'APPROVED') {
                DeliveryExceptionHelper::badRequest(
                    DeliveryConstants::AGENT_KYC_NOT_APPROVED,
                    "Delivery agent's KYC is not approved"
                );
            }

            // Check if this is a non-consolidated parent order trying to access shop
            if ($order->children && !$order->shop && !$data['is_consolidated']) {
                DeliveryExceptionHelper::badRequest(
                    DeliveryConstants::SOMETHING_WENT_WRONG,
                    'Cannot assign delivery to parent order without consolidation'
                );
            }

            // Create delivery record
            $deliveryData = [
                'order_id' => $order->id,
                'delivery_agent_user_id' => $data['delivery_agent_user_id'],
                'status' => DeliveryStatus::ASSIGNED,
                'pickup_address' => $order->children ? null : ($order->shop->address ?? null), // Only set pickup address for non-parent orders
                'delivery_address' => $order->shipping_address ?? null,
                'estimated_delivery_time' => now()->addHours(2), // Default 2 hours, can be customized
                'assigned_at' => now(),
                'assigned_by_user_id' => $assignedBy->id,
                'assignment_type' => 'MANUAL',
                'delivery_fee' => $data['delivery_fee'] ?? 0,
                'notes_by_admin' => $data['notes'] ?? null,
            ];

            $delivery = $this->create($deliveryData);

            // Create status log
            DeliveryStatusLog::create([
                'delivery_id' => $delivery->id,
                'status' => DeliveryStatus::ASSIGNED,
                'user_id' => $assignedBy->id,
                'notes' => 'Delivery assigned by admin',
                'created_at' => now(),
            ]);

            // Update order with delivery_id
            $order->delivery_id = $delivery->id;
            $order->save();

            // Dispatch event
            event(new DeliveryAssigned($delivery, $assignedBy));

            return $delivery;
        } catch (\Exception $e) {
            Log::error("DeliveryRepository error in assignDelivery: " . $e->getMessage(), ['exception' => $e]);
            if ($e instanceof DeliveryException || $e instanceof MarvelException) {
                throw $e;
            }
            Log::error("DeliveryRepository error in assignConsolidatedDelivery: " . $e->getMessage(), ['exception' => $e]);
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Get deliveries for an agent
     *
     * @param User $agent
     * @param array $params
     * @return mixed
     */
    public function getDeliveriesForAgent(User $agent, array $params = [])
    {
        $query = $this->model->where('delivery_agent_user_id', $agent->id)->with('order.products.shop');

        // Filter by status if provided
        if (isset($params['status']) && !empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        // Filter by date range if provided
        if (isset($params['date_from']) && !empty($params['date_from'])) {
            $query->whereDate('created_at', '>=', $params['date_from']);
        }

        if (isset($params['date_to']) && !empty($params['date_to'])) {
            $query->whereDate('created_at', '<=', $params['date_to']);
        }

        // Order by created_at desc by default
        $query->orderBy('created_at', 'desc');

        return $query->paginate($params['limit'] ?? 15);
    }

    /**
     * Get recent deliveries for an agent
     *
     * @param User $agent
     * @param int $limit
     * @return mixed A collection of recent deliveries.
     */
    public function getRecentDeliveriesForAgent(User $agent, int $limit = 5)
    {
        return $this->model->where('delivery_agent_user_id', $agent->id)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get delivery counts for an agent
     *
     * @param User $agent
     * @return array An array containing delivery counts by status and completed today count.
     */
    public function getDeliveryCountsForAgent(User $agent)
    {
        // Get counts by status
        $statusCounts = $this->model->where('delivery_agent_user_id', $agent->id)
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // Get completed today count separately
        $completedToday = $this->model->where('delivery_agent_user_id', $agent->id)
            ->where('status', DeliveryStatus::COMPLETED)
            ->whereDate('completed_at', now()->toDateString())
            ->count();

        // Add total count
        $total = array_sum($statusCounts);

        // Convert status keys to lowercase
        $counts = [];
        foreach ($statusCounts as $status => $count) {
            $counts[strtolower($status)] = $count;
        }

        // Add completed today and total
        $counts['completed_today'] = $completedToday;
        $counts['total'] = $total;

        return $counts;
    }

    /**
     * Update delivery status
     *
     * @param Delivery $delivery
     * @param string $status
     * @param User $user
     * @param array $data
     * @return Delivery
     * @throws DeliveryException
     */
    public function updateDeliveryStatus(Delivery $delivery, string $status, User $user, array $data = [])
    {
        try {
            // Ensure the delivery object has a status before proceeding with transition validation
            if (empty($delivery->status)) {
                DeliveryExceptionHelper::badRequest(
                    DeliveryConstants::INVALID_DELIVERY_STATE,
                    "Delivery object must have a current status to update."
                );
            }
            // Validate status transition
            $this->validateStatusTransition($delivery->status, $status);

            // Prepare update data
            $updateData = ['status' => $status];

            // Add timestamp based on status
            switch ($status) {
                case DeliveryStatus::ACCEPTED_BY_AGENT:
                    $updateData['accepted_at'] = now();
                    break;
                case DeliveryStatus::PICKED_UP:
                    $updateData['picked_up_at'] = now();
                    break;
                case DeliveryStatus::REACHED_DESTINATION:
                    $updateData['reached_destination_at'] = now();
                    break;
                case DeliveryStatus::DELIVERED:
                    $updateData['delivered_at'] = now();
                    break;
                case DeliveryStatus::COMPLETED:
                    $updateData['completed_at'] = now();
                    break;
                case DeliveryStatus::CANCELLED:
                    $updateData['cancelled_at'] = now();
                    $updateData['cancellation_reason'] = $data['reason'] ?? null;
                    break;
                case DeliveryStatus::FAILED_DELIVERY:
                    $updateData['failed_at'] = now();
                    $updateData['failure_reason'] = $data['reason'] ?? null;
                    break;
            }

            // Add notes if provided
            if (isset($data['notes']) && !empty($data['notes'])) {
                if ($user->hasPermissionTo(Permission::SUPER_ADMIN) || $user->hasPermissionTo(Permission::STORE_OWNER)) {
                    $updateData['notes_by_admin'] = $data['notes'];
                } else {
                    $updateData['notes_by_agent'] = $data['notes'];
                }
            }

            // Store the previous status for the event
            $previousStatus = $delivery->status;

            // Update delivery
            $delivery = $this->update($updateData, $delivery->id);

            // Create status log
            DeliveryStatusLog::create([
                'delivery_id' => $delivery->id,
                'status' => $status, // $status is already an enum value
                'user_id' => $user->id,
                'notes' => $data['notes'] ?? null,
                'location' => $data['location'] ?? null,
                'created_at' => now(),
            ]);

            // If agent is updating status, also update their current location
            if ($user->hasPermissionTo(Permission::DELIVERY_AGENT) && isset($data['location']) && !empty($data['location'])) {
                $profile = $user->delivery_agent_profile;
                if ($profile) {
                    $profile->current_location = $data['location'];
                    $profile->save();
                }
            }

            // Dispatch event for status change
            event(new DeliveryStatusChanged($delivery, $previousStatus, $user));

            return $delivery;
        } catch (\Exception $e) {
            Log::error("DeliveryRepository error in updateDeliveryStatus: " . $e->getMessage(), ['exception' => $e]);
            if ($e instanceof DeliveryException || $e instanceof MarvelException) {
                throw $e;
            }
            Log::error("DeliveryRepository error in assignSplitDeliveries: " . $e->getMessage(), ['exception' => $e]);
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Validate status transition
     *
     * @param string $currentStatus
     * @param string $newStatus
     * @throws DeliveryException
     */
    private function validateStatusTransition(string $currentStatus, string $newStatus)
    {
        $allowedTransitions = [
            DeliveryStatus::PENDING_ASSIGNMENT => [DeliveryStatus::ASSIGNED, DeliveryStatus::CANCELLED],
            DeliveryStatus::ASSIGNED => [DeliveryStatus::ACCEPTED_BY_AGENT, DeliveryStatus::REJECTED_BY_AGENT, DeliveryStatus::CANCELLED],
            DeliveryStatus::REJECTED_BY_AGENT => [DeliveryStatus::ASSIGNED, DeliveryStatus::CANCELLED],
            DeliveryStatus::ACCEPTED_BY_AGENT => [DeliveryStatus::PICKED_UP, DeliveryStatus::CANCELLED],
            DeliveryStatus::PICKED_UP => [DeliveryStatus::IN_TRANSIT, DeliveryStatus::CANCELLED, DeliveryStatus::FAILED_DELIVERY],
            DeliveryStatus::IN_TRANSIT => [DeliveryStatus::REACHED_DESTINATION, DeliveryStatus::CANCELLED, DeliveryStatus::FAILED_DELIVERY],
            DeliveryStatus::REACHED_DESTINATION => [DeliveryStatus::DELIVERED, DeliveryStatus::CANCELLED, DeliveryStatus::FAILED_DELIVERY],
            DeliveryStatus::DELIVERED => [DeliveryStatus::COMPLETED, DeliveryStatus::FAILED_DELIVERY],
            DeliveryStatus::COMPLETED => [],
            DeliveryStatus::CANCELLED => [],
            DeliveryStatus::FAILED_DELIVERY => [DeliveryStatus::ASSIGNED],
        ];

        if (!isset($allowedTransitions[$currentStatus]) || !in_array($newStatus, $allowedTransitions[$currentStatus])) {
            Log::warning("Invalid Delivery Status Transition Attempted: From {$currentStatus} to {$newStatus}");
            DeliveryExceptionHelper::badRequest(
                DeliveryConstants::INVALID_STATUS_TRANSITION,
                "Cannot transition from {$currentStatus} to {$newStatus}"
            );
        }
    }

    /**
     * Add proof of delivery
     *
     * @param Delivery $delivery
     * @param array $data
     * @return Delivery
     * @throws DeliveryException
     */
    public function addProofOfDelivery(Delivery $delivery, array $data)
    {
        try {
            // Validate current status
            if (!in_array($delivery->status, [DeliveryStatus::REACHED_DESTINATION, DeliveryStatus::DELIVERED])) {
                DeliveryExceptionHelper::badRequest(DeliveryConstants::INVALID_STATUS_FOR_POD);
            }

            // Update delivery with POD type
            $updateData = [
                'pod_type' => $data['pod_type'] ?? 'PHOTO',
            ];

            if (isset($data['proof_of_delivery']) && !empty($data['proof_of_delivery'])) {
                $updateData['proof_of_delivery'] = $data['proof_of_delivery'];
            }

            return $this->update($updateData, $delivery->id);
        } catch (\Exception $e) {
            Log::error("DeliveryRepository error in addProofOfDelivery: " . $e->getMessage(), ['exception' => $e]);
            if ($e instanceof DeliveryException || $e instanceof MarvelException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Confirm payment for cash on delivery
     *
     * @param Delivery $delivery
     * @param User $agent
     * @return Delivery
     * @throws DeliveryException
     */
    public function confirmPayment(Delivery $delivery, User $agent)
    {
        try {
            // Validate current status
            if ($delivery->status !== DeliveryStatus::DELIVERED) {
                DeliveryExceptionHelper::badRequest(DeliveryConstants::INVALID_STATUS_FOR_PAYMENT_CONFIRMATION);
            }

            // Validate agent
            if ($delivery->delivery_agent_user_id !== $agent->id) {
                DeliveryExceptionHelper::unauthorized(DeliveryConstants::NOT_AUTHORIZED);
            }

            // Get the order
            $order = $delivery->order;
            if (!$order) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::ORDER_NOT_FOUND);
            }

            // Update order payment status
            $order->payment_status = 'SUCCESS';
            $order->save();

            // Update delivery status to COMPLETED
            return $this->updateDeliveryStatus($delivery, DeliveryStatus::COMPLETED, $agent, [
                'notes' => 'Payment confirmed and delivery completed'
            ]);
        } catch (\Exception $e) {
            Log::error("DeliveryRepository error in confirmPayment: " . $e->getMessage(), ['exception' => $e]);
            if ($e instanceof DeliveryException || $e instanceof MarvelException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    // Method moved to RoutingCalculationService

    /**
     * Automatically assign delivery to the nearest available agent
     * Supports both single-vendor and multi-vendor orders
     *
     * @param Order $order
     * @param array $options
     * @return Delivery|array|null
     * @throws DeliveryException
     */
    public function autoAssignDelivery(Order $order, array $options = [])
    {
        try {
            // Check if this is a multi-vendor order
            if ($order->children && $order->children->count() > 0) {
                return $this->autoAssignMultiVendorDelivery($order, $options);
            }

            // Handle single-vendor order
            return $this->autoAssignSingleVendorDelivery($order, $options);
        } catch (\Exception $e) {
            Log::error("DeliveryRepository error in autoAssignDelivery: " . $e->getMessage(), ['exception' => $e]);
            if ($e instanceof DeliveryException || $e instanceof MarvelException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(
                DeliveryConstants::SOMETHING_WENT_WRONG,
                "Auto assignment failed: " . $e->getMessage()
            );
        }
    }

    /**
     * Automatically assign delivery for a single-vendor order
     *
     * @param Order $order
     * @param array $options
     * @return Delivery|null
     * @throws DeliveryException
     */
    private function autoAssignSingleVendorDelivery(Order $order, array $options = [])
    {
        $radius = $options['radius'] ?? config('delivery.auto_assignment_radius', 15);
        $trigger = $options['trigger'] ?? 'unknown';
        $attempt = $options['attempt'] ?? 1;

        // Find nearest available agents
        $agents = $this->agentDiscoveryService->findNearestAvailableAgents($order, $radius);

        if ($agents->isEmpty()) {
            // No agents available within radius
            if ($attempt < config('delivery.auto_assignment_retry_attempts', 3)) {
                // Try with larger radius on retry
                $expandedRadius = $radius * (1 + ($attempt * 0.5)); // Increase radius by 50% per attempt
                $agents = $this->agentDiscoveryService->findNearestAvailableAgents($order, $expandedRadius);
            }

            if ($agents->isEmpty()) {
                // Still no agents, create pending delivery
                return $this->createPendingDelivery($order, [
                    'reason' => "No delivery agents available within {$radius}km radius",
                    'trigger' => $trigger,
                    'attempt' => $attempt,
                ]);
            }
        }

        // Select the best agent using configured strategy
        $selectedAgent = $this->selectAgentForAutoAssignment($agents, $options);

        // Create delivery assignment
        $deliveryData = [
            'delivery_agent_user_id' => $selectedAgent->id,
            'delivery_fee' => $this->calculateDeliveryFee($order, $selectedAgent),
            'assignment_type' => 'AUTOMATIC',
            'notes' => "Automatically assigned to nearest agent (distance: {$selectedAgent->distance}km)",
        ];

        // Create a system user for automatic assignment
        $systemUser = $this->getSystemUser();

        return $this->assignDelivery($deliveryData, $order, $systemUser);
    }

    /**
     * Automatically assign delivery for a multi-vendor order
     *
     * @param Order $order
     * @param array $options
     * @return Delivery|array|null
     * @throws DeliveryException
     */
    private function autoAssignMultiVendorDelivery(Order $order, array $options = [])
    {
        $radius = $options['radius'] ?? config('delivery.auto_assignment_radius', 15);
        $trigger = $options['trigger'] ?? 'unknown';
        $attempt = $options['attempt'] ?? 1;

        // Get multi-vendor agent discovery results
        $result = $this->agentDiscoveryService->findNearestAgentsForMultiVendor($order, $radius);

        if (!isset($result['agents']) || (is_array($result['agents']) ? empty($result['agents']) : $result['agents']->isEmpty())) {
            // No agents available within radius
            if ($attempt < config('delivery.auto_assignment_retry_attempts', 3)) {
                // Try with larger radius on retry
                $expandedRadius = $radius * (1 + ($attempt * 0.5));
                $options['radius'] = $expandedRadius;
                $result = $this->agentDiscoveryService->findNearestAgentsForMultiVendor($order, $expandedRadius);
            }

            if (!isset($result['agents']) || (is_array($result['agents']) ? empty($result['agents']) : $result['agents']->isEmpty())) {
                // Still no agents, create pending delivery
                return $this->createPendingDelivery($order, [
                    'reason' => "No delivery agents available for multi-vendor order within {$radius}km radius",
                    'trigger' => $trigger,
                    'attempt' => $attempt,
                    'consolidation_attempted' => $result['should_consolidate'] ?? false,
                ]);
            }
        }

        // Create a system user for automatic assignment
        $systemUser = $this->getSystemUser();

        // Determine assignment strategy based on consolidation decision
        if ($result['should_consolidate']) {
            return $this->autoAssignConsolidatedDelivery($order, $result, $systemUser, $options);
        } else {
            return $this->autoAssignSplitDeliveries($order, $result, $systemUser, $options);
        }
    }

    /**
     * Automatically assign consolidated delivery for multi-vendor order
     *
     * @param Order $order
     * @param array $result
     * @param User $systemUser
     * @param array $options
     * @return Delivery
     * @throws DeliveryException
     */
    private function autoAssignConsolidatedDelivery(Order $order, array $result, User $systemUser, array $options = [])
    {
        $agents = $result['agents'];

        if ($agents->isEmpty()) {
            throw new DeliveryException('No agents available for consolidated delivery');
        }

        // Select the best agent using configured strategy
        $selectedAgent = $this->selectAgentForAutoAssignment($agents, $options);

        // Calculate consolidated delivery fee
        $baseFee = config('delivery.base_delivery_fee', 10.0);
        $multiPickupFee = config('delivery.multi_pickup_fee', 5.0);
        $consolidatedMultiplier = config('delivery.consolidated_delivery_fee_multiplier', 1.5);
        $pickupCount = $order->children->count();
        $deliveryFee = ($baseFee * $consolidatedMultiplier) + ($multiPickupFee * ($pickupCount - 1));

        // Create pickup sequence based on shop locations (optimize later)
        $pickupSequence = $order->children->pluck('shop_id')->toArray();

        // Create consolidated delivery assignment
        $deliveryData = [
            'delivery_agent_user_id' => $selectedAgent->id,
            'delivery_fee' => $deliveryFee,
            'assignment_type' => 'AUTOMATIC',
            'notes' => "Automatically assigned consolidated delivery (distance: {$selectedAgent->distance}km, {$pickupCount} pickups)",
            'pickup_sequence' => $pickupSequence,
        ];

        return $this->assignConsolidatedDelivery($deliveryData, $order, $systemUser);
    }

    /**
     * Automatically assign split deliveries for multi-vendor order
     *
     * @param Order $order
     * @param array $result
     * @param User $systemUser
     * @param array $options
     * @return array
     * @throws DeliveryException
     */
    private function autoAssignSplitDeliveries(Order $order, array $result, User $systemUser, array $options = [])
    {
        $agentsByShop = $result['agents'];
        $deliveries = [];

        // Handle case where agents is a collection (single shop scenario)
        if (!is_array($agentsByShop)) {
            // This shouldn't happen for multi-vendor, but handle gracefully
            throw new DeliveryException('Invalid agent data for split delivery assignment');
        }

        // Assign agents to each child order
        foreach ($order->children as $childOrder) {
            $shopId = $childOrder->shop_id;

            if (!isset($agentsByShop[$shopId]) || empty($agentsByShop[$shopId])) {
                // No agents available for this shop, create pending delivery
                $pendingDelivery = $this->createPendingDelivery($childOrder, [
                    'reason' => "No delivery agents available for shop {$shopId}",
                    'trigger' => $options['trigger'] ?? 'auto_assignment',
                    'attempt' => $options['attempt'] ?? 1,
                ]);
                $deliveries[] = $pendingDelivery;
                continue;
            }

            $shopAgents = collect($agentsByShop[$shopId]);
            $selectedAgent = $this->selectAgentForAutoAssignment($shopAgents, $options);

            // Create delivery assignment for this child order
            $deliveryData = [
                'delivery_agent_user_id' => $selectedAgent->id,
                'delivery_fee' => $this->calculateDeliveryFee($childOrder, $selectedAgent),
                'assignment_type' => 'AUTOMATIC',
                'notes' => "Automatically assigned split delivery (distance: {$selectedAgent->distance}km)",
            ];

            $delivery = $this->assignDelivery($deliveryData, $childOrder, $systemUser);
            $deliveries[] = $delivery;
        }

        return $deliveries;
    }

    /**
     * Create a pending delivery record for manual assignment
     *
     * @param Order $order
     * @param array $metadata
     * @return Delivery
     * @throws DeliveryException
     */
    public function createPendingDelivery(Order $order, array $metadata = [])
    {
        try {
            $systemUser = $this->getSystemUser();

            $delivery = new Delivery([
                'order_id' => $order->id,
                'delivery_agent_user_id' => null,
                'status' => DeliveryStatus::PENDING_ASSIGNMENT,
                'assignment_type' => 'AUTOMATIC',
                'delivery_fee' => 0,
                'assigned_by' => $systemUser->id,
                'assigned_at' => now(),
                'notes' => 'Pending automatic assignment - ' . ($metadata['reason'] ?? 'No agents available'),
                'metadata' => $metadata,
            ]);

            $delivery->save();

            // Update order with delivery reference
            $order->delivery_id = $delivery->id;
            $order->save();

            return $delivery;
        } catch (\Exception $e) {
            Log::error("DeliveryRepository error in createPendingDelivery: " . $e->getMessage(), ['exception' => $e]);
            DeliveryExceptionHelper::internalError(
                DeliveryConstants::SOMETHING_WENT_WRONG,
                "Failed to create pending delivery: " . $e->getMessage()
            );
        }
    }

    /**
     * Select the best agent for automatic assignment
     *
     * @param \Illuminate\Support\Collection $agents
     * @param array $options
     * @return User
     */
    private function selectAgentForAutoAssignment($agents, array $options = [])
    {
        $strategy = $options['selection_strategy'] ?? config('delivery.agent_selection_strategy', 'nearest');

        switch ($strategy) {
            case 'round_robin':
                return $this->selectAgentRoundRobin($agents);

            case 'least_busy':
                return $this->selectLeastBusyAgent($agents);

            case 'best_rating':
                return $this->selectBestRatedAgent($agents);

            case 'nearest':
            default:
                return $agents->first(); // Already sorted by distance
        }
    }

    /**
     * Select agent using round-robin strategy
     *
     * @param \Illuminate\Support\Collection $agents
     * @return User
     */
    private function selectAgentRoundRobin($agents)
    {
        // Simple round-robin based on agent ID modulo
        $agentCount = $agents->count();
        $index = (int) (microtime(true) * 1000) % $agentCount;
        return $agents->values()->get($index);
    }

    /**
     * Select the least busy agent (fewest active deliveries)
     *
     * @param \Illuminate\Support\Collection $agents
     * @return User
     */
    private function selectLeastBusyAgent($agents)
    {
        return $agents->sortBy(function ($agent) {
            return $agent->active_deliveries_count ?? 0;
        })->first();
    }

    /**
     * Select the best rated agent
     *
     * @param \Illuminate\Support\Collection $agents
     * @return User
     */
    private function selectBestRatedAgent($agents)
    {
        return $agents->sortByDesc(function ($agent) {
            return $agent->delivery_agent_profile->performance_rating ?? 0;
        })->first();
    }

    /**
     * Calculate delivery fee for automatic assignment
     *
     * @param Order $order
     * @param User $agent
     * @return float
     */
    private function calculateDeliveryFee(Order $order, User $agent)
    {
        // Use configured base fee or calculate based on distance
        $baseFee = config('delivery.base_delivery_fee', 10.0);
        $perKmFee = config('delivery.per_km_fee', 1.0);

        // Calculate distance-based fee
        $distanceFee = $agent->distance * $perKmFee;

        return $baseFee + $distanceFee;
    }

    /**
     * Get or create a system user for automatic operations
     *
     * @return User
     */
    private function getSystemUser()
    {
        // Try to get existing system user
        $systemUser = User::where('email', '<EMAIL>')->first();

        if (!$systemUser) {
            // Create system user if it doesn't exist
            $systemUser = User::create([
                'name' => 'System Auto Assignment',
                'email' => '<EMAIL>',
                'password' => bcrypt(\Illuminate\Support\Str::random(32)),
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            // Give system user admin permissions
            $systemUser->givePermissionTo(Permission::SUPER_ADMIN);
        }

        return $systemUser;
    }

    /**
     * Assign consolidated delivery to an agent
     *
     * @param array $data
     * @param Order $parentOrder
     * @param User $assignedBy
     * @return Delivery
     * @throws DeliveryException
     */
    public function assignConsolidatedDelivery(array $data, Order $parentOrder, User $assignedBy)
    {
        try {
            // Verify this is a multi-vendor order
            if (!$parentOrder->children || $parentOrder->children->count() === 0) {
                DeliveryExceptionHelper::badRequest(
                    DeliveryConstants::SOMETHING_WENT_WRONG,
                    'Not a multi-vendor order'
                );
            }

            // Check if agent exists and is a delivery agent
            $agent = User::find($data['delivery_agent_user_id']);
            if (!$agent || !$agent->hasPermissionTo(Permission::DELIVERY_AGENT)) {
                DeliveryExceptionHelper::badRequest(DeliveryConstants::INVALID_DELIVERY_AGENT);
            }

            // Check if agent has an approved profile
            $profile = $agent->delivery_agent_profile;
            if (!$profile || $profile->kyc_status !== 'APPROVED') {
                DeliveryExceptionHelper::badRequest(
                    DeliveryConstants::AGENT_KYC_NOT_APPROVED,
                    "Delivery agent's KYC is not approved"
                );
            }

            // Calculate base delivery fee
            $baseFee = config('delivery.base_delivery_fee', 10.0);
            $multiPickupFee = config('delivery.multi_pickup_fee', 5.0);
            $consolidatedMultiplier = config('delivery.consolidated_delivery_fee_multiplier', 1.5);

            // Calculate total fee based on number of pickup locations (child orders only)
            $pickupCount = $parentOrder->children->count();
            $deliveryFee = ($baseFee * $consolidatedMultiplier) + ($multiPickupFee * ($pickupCount - 1));

            // Collect pickup addresses from child orders
            $pickupAddresses = collect();
            foreach ($parentOrder->children as $childOrder) {
                if ($childOrder->shop && $childOrder->shop->address) {
                    $pickupAddresses->push($childOrder->shop->address);
                }
            }

            // Create consolidated delivery record
            $deliveryData = [
                'order_id' => $parentOrder->id,
                'delivery_agent_user_id' => $data['delivery_agent_user_id'],
                'status' => DeliveryStatus::ASSIGNED,
                'pickup_addresses' => $pickupAddresses->toArray(),
                'delivery_address' => $parentOrder->shipping_address,
                'delivery_fee' => $deliveryFee,
                'estimated_delivery_time' => now()->addHours(3), // Add extra hour for multiple pickups
                'assigned_at' => now(),
                'assigned_by_user_id' => $assignedBy->id,
                'notes' => $data['notes'] ?? null,
                'is_consolidated' => true,
                'metadata' => [
                    'child_order_ids' => $parentOrder->children->pluck('id')->toArray(),
                    'pickup_sequence' => $this->validatePickupSequence($data['pickup_sequence'] ?? null, $parentOrder->children),
                ]
            ];

            $delivery = $this->model->create($deliveryData);

            // Update all orders with delivery reference
            $parentOrder->delivery_id = $delivery->id;
            $parentOrder->save();

            foreach ($parentOrder->children as $childOrder) {
                $childOrder->delivery_id = $delivery->id;
                $childOrder->save();
            }

            // Create initial status log
            DeliveryStatusLog::create([
                'delivery_id' => $delivery->id,
                'status' => DeliveryStatus::ASSIGNED,
                'user_id' => $assignedBy->id,
                'notes' => 'Consolidated delivery assigned',
                'location' => $agent->delivery_agent_profile->current_location ?? null,
            ]);

            return $delivery;
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException || $e instanceof MarvelException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Assign split deliveries for a multi-vendor order
     *
     * @param array $data Array of delivery assignments [order_id => agent_id]
     * @param Order $parentOrder
     * @param User $assignedBy
     * @return array Array of created Delivery objects
     * @throws DeliveryException
     */
    public function assignSplitDeliveries(array $data, Order $parentOrder, User $assignedBy)
    {
        try {
            $deliveries = [];

            // Create delivery for parent order
            if (isset($data[$parentOrder->id])) {
                $deliveries[] = $this->assignDelivery([
                    'delivery_agent_user_id' => $data[$parentOrder->id],
                    'notes' => 'Split delivery - Parent order'
                ], $parentOrder, $assignedBy);
            }

            // Create deliveries for child orders
            foreach ($parentOrder->children as $childOrder) {
                if (isset($data[$childOrder->id])) {
                    $deliveries[] = $this->assignDelivery([
                        'delivery_agent_user_id' => $data[$childOrder->id],
                        'notes' => 'Split delivery - Child order'
                    ], $childOrder, $assignedBy);
                }
            }

            return $deliveries;
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException || $e instanceof MarvelException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Validate and normalize pickup sequence for child orders
     *
     * @param array|null $sequence
     * @param \Illuminate\Support\Collection $childOrders
     * @return array|null
     */
    private function validatePickupSequence(?array $sequence, $childOrders)
    {
        if (!$sequence) {
            // If no sequence provided, return child order IDs in original order
            return $childOrders->pluck('id')->toArray();
        }

        $childOrderIds = $childOrders->pluck('id')->toArray();

        // Validate that sequence only contains child order IDs
        $validSequence = array_filter($sequence, function($orderId) use ($childOrderIds) {
            return in_array($orderId, $childOrderIds);
        });

        // Validate that all child orders are included
        $missingOrderIds = array_diff($childOrderIds, $validSequence);
        
        if (!empty($missingOrderIds)) {
            // If some orders are missing from sequence, append them at the end
            $validSequence = array_merge($validSequence, $missingOrderIds);
        }

        return $validSequence;
    }
}
