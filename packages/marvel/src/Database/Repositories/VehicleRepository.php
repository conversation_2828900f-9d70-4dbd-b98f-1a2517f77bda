<?php

namespace Marvel\Database\Repositories;

use Marvel\Database\Models\Vehicle;
use Marvel\Exceptions\MarvelException;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Exceptions\RepositoryException;
use Marvel\Helpers\DeliveryConstants;

class VehicleRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'delivery_agent_user_id',
        'type',
        'registration_number',
        'is_verified',
        'is_active',
    ];

    /**
     * Configure the Model
     *
     * @return string
     */
    public function model()
    {
        return Vehicle::class;
    }

    /**
     * @throws RepositoryException
     */
    public function boot()
    {
        try {
            $this->pushCriteria(app(RequestCriteria::class));
        } catch (RepositoryException $e) {
            throw new RepositoryException($e);
        }
    }

    /**
     * Get vehicles by delivery agent user ID
     *
     * @param $userId
     * @return mixed
     */
    public function getVehiclesByDeliveryAgentId($userId)
    {
        return $this->findByField('delivery_agent_user_id', $userId);
    }

    /**
     * Create a new vehicle
     *
     * @param array $data
     * @return mixed
     * @throws MarvelException
     */
    public function storeVehicle(array $data)
    {
        try {
            return $this->create($data);
        } catch (\Exception $e) {
            throw new MarvelException(DeliveryConstants::SOMETHING_WENT_WRONG);
        }
    }

    /**
     * Update a vehicle
     *
     * @param array $data
     * @param $id
     * @return mixed
     * @throws MarvelException
     */
    public function updateVehicle(array $data, $id)
    {
        try {
            $vehicle = $this->findOrFail($id);
            return $this->update($data, $id);
        } catch (\Exception $e) {
            throw new MarvelException(DeliveryConstants::SOMETHING_WENT_WRONG);
        }
    }

    /**
     * Set a vehicle as active
     *
     * @param $vehicleId
     * @param $userId
     * @return mixed
     * @throws MarvelException
     */
    public function setActiveVehicle($vehicleId, $userId)
    {
        try {
            $vehicle = $this->findOrFail($vehicleId);

            // Check if the vehicle belongs to the user
            if ($vehicle->delivery_agent_user_id != $userId) {
                throw new MarvelException(DeliveryConstants::NOT_AUTHORIZED);
            }

            // Get the user's profile
            $profile = $vehicle->deliveryAgent->delivery_agent_profile;

            if (!$profile) {
                throw new MarvelException(DeliveryConstants::SOMETHING_WENT_WRONG);
            }

            // Update the profile with the active vehicle ID
            $profile->active_vehicle_id = $vehicleId;
            $profile->save();

            return $profile;
        } catch (\Exception $e) {
            throw new MarvelException(DeliveryConstants::SOMETHING_WENT_WRONG);
        }
    }
}
