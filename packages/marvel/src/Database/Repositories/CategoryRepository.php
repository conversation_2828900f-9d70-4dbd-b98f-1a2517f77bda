<?php


namespace Marvel\Database\Repositories;

use Illuminate\Http\Request;
use Marvel\Database\Models\Category;
use Marvel\Http\Requests\CategoryCreateRequest;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Exceptions\RepositoryException;



class CategoryRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'name'        => 'like',
        'parent',
        'language',
//        'type.slug',
    ];

    protected $dataArray = [
        'name',
        'slug',
        'type_id',
        'icon',
        'image',
        'details',
        'banner_image',
        'language',
        'parent',
    ];

    public function boot()
    {
        try {
            $this->pushCriteria(app(RequestCriteria::class));
        } catch (RepositoryException $e) {
            //
        }
    }


    /**
     * Configure the Model
     **/
    public function model()
    {
        return Category::class;
    }

    public function saveCategory(Request $request) {
        $data = $request->only($this->dataArray);
        $data['slug'] = $this->makeSlug($request);
        return $this->create($data);
    }

    public function updateCategory($request, $category)
    {
        $data = $request->only($this->dataArray);
        if (!empty($request->slug) &&  $request->slug != $category['slug']) {
            $data['slug'] = $this->makeSlug($request);
        }
        $category->update($data);
        return $this->findOrFail($category->id);
    }

    /**
     * Get categories from user's order history
     *
     * @param int $userId
     * @param int $limit
     * @param string $language
     * @param bool $paginate Whether to paginate the results
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Pagination\LengthAwarePaginator
     */
    public function getCategoriesFromUserOrders($userId, $limit = 5, $language = DEFAULT_LANGUAGE, $paginate = false)
    {
        $query = $this->model->select('categories.*')
            ->join('category_product', 'categories.id', '=', 'category_product.category_id')
            ->join('products', 'products.id', '=', 'category_product.product_id')
            ->join('order_product', 'products.id', '=', 'order_product.product_id')
            ->join('orders', 'orders.id', '=', 'order_product.order_id')
            ->where('orders.customer_id', $userId)
            ->where('categories.language', $language)
            ->groupBy('categories.id')
            ->orderByRaw('COUNT(orders.id) DESC');

        if ($paginate) {
            return $query->paginate($limit);
        }

        return $query->limit($limit)->get();
    }

    /**
     * Get trending categories based on recent orders
     *
     * @param int $limit
     * @param int $days
     * @param string $language
     * @param bool $paginate Whether to paginate the results
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Pagination\LengthAwarePaginator
     */
    public function getTrendingCategories($limit = 3, $days = 30, $language = DEFAULT_LANGUAGE, $paginate = false)
    {
        $query = $this->model->select('categories.*')
            ->join('category_product', 'categories.id', '=', 'category_product.category_id')
            ->join('products', 'products.id', '=', 'category_product.product_id')
            ->join('order_product', 'products.id', '=', 'order_product.product_id')
            ->join('orders', 'orders.id', '=', 'order_product.order_id')
            ->where('categories.language', $language)
            ->where('orders.created_at', '>=', now()->subDays($days))
            ->where('orders.order_status', 'order-completed')
            ->groupBy('categories.id')
            ->orderByRaw('COUNT(orders.id) DESC');

        if ($paginate) {
            return $query->paginate($limit);
        }

        return $query->limit($limit)->get();
    }

    /**
     * Get popular categories based on product count and orders
     *
     * @param int $limit
     * @param string $language
     * @param bool $paginate Whether to paginate the results
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Pagination\LengthAwarePaginator
     */
    public function getPopularCategories($limit = 10, $language = DEFAULT_LANGUAGE, $paginate = false)
    {
        $query = $this->model->select('categories.*')
            ->leftJoin('category_product', 'categories.id', '=', 'category_product.category_id')
            ->leftJoin('products', 'products.id', '=', 'category_product.product_id')
            ->where('categories.language', $language)
            ->groupBy('categories.id')
            ->orderByRaw('COUNT(products.id) DESC');

        if ($paginate) {
            return $query->paginate($limit);
        }

        return $query->limit($limit)->get();
    }
}
