<?php

namespace Marvel\Database\Repositories;

use Marvel\Database\Models\DeliveryAgentTransaction;
use Marvel\Database\Models\User;
use Marvel\Exceptions\DeliveryException; // Keep for type hinting
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Exceptions\RepositoryException;

class DeliveryAgentTransactionRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'delivery_agent_user_id',
        'transaction_type',
        'reference_type',
        'status',
        'payment_method',
        'provider',
    ];

    /**
     * Configure the Model
     *
     * @return string
     */
    public function model()
    {
        return DeliveryAgentTransaction::class;
    }

    /**
     * @throws RepositoryException
     */
    public function boot()
    {
        try {
            $this->pushCriteria(app(RequestCriteria::class));
        } catch (RepositoryException $e) {
            throw new RepositoryException($e);
        }
    }

    /**
     * Get transactions for a delivery agent
     *
     * @param User $agent
     * @param array $params
     * @return mixed
     */
    public function getTransactionsForAgent(User $agent, array $params = [])
    {
        $limit = $params['limit'] ?? 15;
        $transaction_type = $params['transaction_type'] ?? null;
        $status = $params['status'] ?? null;
        $date_from = $params['date_from'] ?? null;
        $date_to = $params['date_to'] ?? null;
        
        $query = $this->model->where('delivery_agent_user_id', $agent->id);
        
        if ($transaction_type) {
            $query->where('transaction_type', $transaction_type);
        }
        
        if ($status) {
            $query->where('status', $status);
        }
        
        if ($date_from) {
            $query->whereDate('created_at', '>=', $date_from);
        }
        
        if ($date_to) {
            $query->whereDate('created_at', '<=', $date_to);
        }
        
        return $query->orderBy('created_at', 'desc')
            ->paginate($limit);
    }

    /**
     * Create an earning transaction
     *
     * @param array $data
     * @return mixed
     * @throws DeliveryException
     */
    public function createEarningTransaction(array $data)
    {
        try {
            return $this->create([
                'delivery_agent_user_id' => $data['delivery_agent_user_id'],
                'transaction_type' => DeliveryAgentTransaction::TYPE_EARNING,
                'reference_type' => DeliveryAgentTransaction::REF_DELIVERY,
                'reference_id' => $data['delivery_id'],
                'amount' => $data['amount'],
                'status' => DeliveryAgentTransaction::STATUS_COMPLETED,
                'notes' => $data['notes'] ?? 'Earnings from delivery',
                'processed_at' => now(),
            ]);
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Create a withdrawal transaction
     *
     * @param array $data
     * @return mixed
     * @throws DeliveryException
     */
    public function createWithdrawalTransaction(array $data)
    {
        try {
            return $this->create([
                'delivery_agent_user_id' => $data['delivery_agent_user_id'],
                'transaction_type' => DeliveryAgentTransaction::TYPE_WITHDRAWAL,
                'reference_type' => DeliveryAgentTransaction::REF_WITHDRAWAL,
                'reference_id' => $data['withdrawal_id'],
                'amount' => -1 * abs($data['amount']), // Negative amount for withdrawals
                'status' => $data['status'] ?? DeliveryAgentTransaction::STATUS_PENDING,
                'payment_method' => $data['payment_method'] ?? null,
                'provider' => $data['provider'] ?? null,
                'payment_details' => $data['payment_details'] ?? null,
                'transaction_id' => $data['transaction_id'] ?? null,
                'notes' => $data['notes'] ?? 'Withdrawal request',
                'created_by_user_id' => $data['created_by_user_id'] ?? null,
                'processed_at' => $data['processed_at'] ?? null,
            ]);
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Update a transaction
     *
     * @param array $data
     * @param int $id
     * @return DeliveryAgentTransaction
     * @throws MarvelException
     */
    public function updateTransaction(array $data, $id)
    {
        try {
            $transaction = $this->findOrFail($id);
            return $this->update($data, $id);
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Create a manual adjustment transaction
     *
     * @param array $data
     * @return mixed
     * @throws DeliveryException
     */
    public function createAdjustmentTransaction(array $data)
    {
        try {
            return $this->create([
                'delivery_agent_user_id' => $data['delivery_agent_user_id'],
                'transaction_type' => DeliveryAgentTransaction::TYPE_ADJUSTMENT,
                'reference_type' => DeliveryAgentTransaction::REF_MANUAL,
                'amount' => $data['amount'],
                'status' => DeliveryAgentTransaction::STATUS_COMPLETED,
                'notes' => $data['notes'] ?? 'Manual adjustment',
                'created_by_user_id' => $data['created_by_user_id'],
                'processed_at' => now(),
            ]);
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }
}
