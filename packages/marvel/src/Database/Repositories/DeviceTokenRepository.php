<?php

namespace Marvel\Database\Repositories;

use Marvel\Database\Models\DeviceToken;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Exceptions\RepositoryException;

class DeviceTokenRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'user_id',
        'token',
        'device_type',
        'is_active',
    ];

    /**
     * Configure the Model
     *
     * @return string
     */
    public function model()
    {
        return DeviceToken::class;
    }

    /**
     * @throws RepositoryException
     */
    public function boot()
    {
        try {
            $this->pushCriteria(app(RequestCriteria::class));
        } catch (RepositoryException $e) {
            throw new RepositoryException($e);
        }
    }
}
