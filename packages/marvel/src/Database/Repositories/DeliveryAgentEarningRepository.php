<?php

namespace Marvel\Database\Repositories;

use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\DeliveryAgentEarning;
use Marvel\Database\Models\User;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;
use Marvel\Exceptions\DeliveryException;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Exceptions\RepositoryException;

class DeliveryAgentEarningRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'delivery_agent_user_id',
    ];

    /**
     * Configure the Model
     *
     * @return string
     */
    public function model()
    {
        return DeliveryAgentEarning::class;
    }

    /**
     * @throws RepositoryException
     */
    public function boot()
    {
        try {
            $this->pushCriteria(app(RequestCriteria::class));
        } catch (RepositoryException $e) {
            throw new RepositoryException($e);
        }
    }

    /**
     * Get earnings for a delivery agent
     *
     * @param User $agent
     * @return mixed
     * @throws DeliveryException
     */
    public function getEarningsForAgent(User $agent)
    {
        try {
            $earnings = $this->findByField('delivery_agent_user_id', $agent->id)->first();
            
            if (!$earnings) {
                // Create a new earnings record if it doesn't exist
                $earnings = $this->create([
                    'delivery_agent_user_id' => $agent->id,
                    'total_earnings' => 0,
                    'withdrawn_amount' => 0,
                    'current_balance' => 0,
                    'pending_withdrawal_amount' => 0,
                ]);
            }
            
            return $earnings;
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }



    /**
     * Get earnings summary (Today, Week, Month) for a delivery agent.
     *
     * @param User $agent
     * @return array An array containing 'today', 'week', and 'month' earnings.
     */
    public function getEarningsSummaryForAgent(User $agent): array
    {
        $agentId = $agent->id;

        // Base query for completed deliveries with a fee for the specific agent
        $baseQuery = Delivery::where('delivery_agent_user_id', $agentId)
            ->where('status', 'COMPLETED')
            ->whereNotNull('delivery_fee')
            ->where('delivery_fee', '>', 0);

        // Calculate Today's Earnings
        $todayEarnings = (clone $baseQuery)
            ->whereDate('completed_at', today())
            ->sum('delivery_fee');

        // Calculate This Week's Earnings
        $startOfWeek = now()->startOfWeek();
        $endOfWeek = now()->endOfWeek();
        $weekEarnings = (clone $baseQuery)
            ->whereBetween('completed_at', [$startOfWeek, $endOfWeek])
            ->sum('delivery_fee');

        // Calculate This Month's Earnings
        $monthEarnings = (clone $baseQuery)
            ->whereMonth('completed_at', now()->month)
            ->whereYear('completed_at', now()->year)
            ->sum('delivery_fee');

        return [
            'today' => $todayEarnings ?? 0.00,
            'week'  => $weekEarnings ?? 0.00,
            'month' => $monthEarnings ?? 0.00,
        ];
    }

    /**
     * Update payment info for a delivery agent
     *
     * @param array $data
     * @param User $agent
     * @return mixed
     * @throws DeliveryException
     */
    public function updatePaymentInfo(array $data, User $agent)
    {
        try {
            $earnings = $this->getEarningsForAgent($agent);
            
            return $this->update([
                'payment_info' => $data['payment_info']
            ], $earnings->id);
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Get earnings history for a delivery agent
     *
     * @param User $agent
     * @param array $params
     * @return mixed
     */
    public function getEarningsHistory(User $agent, array $params = [])
    {
        $limit = $params['limit'] ?? 15;
        $date_from = $params['date_from'] ?? null;
        $date_to = $params['date_to'] ?? null;
        
        $query = $agent->deliveries()
            ->where('status', 'COMPLETED')
            ->whereNotNull('delivery_fee')
            ->where('delivery_fee', '>', 0);
        
        if ($date_from) {
            $query->whereDate('completed_at', '>=', $date_from);
        }
        
        if ($date_to) {
            $query->whereDate('completed_at', '<=', $date_to);
        }
        
        return $query->orderBy('completed_at', 'desc')
            ->paginate($limit);
    }
}
