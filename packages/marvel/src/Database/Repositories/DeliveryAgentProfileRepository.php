<?php

namespace Marvel\Database\Repositories;

use Marvel\Database\Models\DeliveryAgentProfile;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Exceptions\RepositoryException;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;
use Marvel\Exceptions\DeliveryException;

class DeliveryAgentProfileRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'user_id',
        'availability_status',
        'kyc_status',
    ];

    /**
     * Configure the Model
     *
     * @return string
     */
    public function model()
    {
        return DeliveryAgentProfile::class;
    }

    /**
     * @throws RepositoryException
     */
    public function boot()
    {
        try {
            $this->pushCriteria(app(RequestCriteria::class));
        } catch (RepositoryException $e) {
            throw new RepositoryException($e);
        }
    }

    /**
     * Update delivery agent profile
     *
     * @param array $data
     * @param $id
     * @return mixed
     * @throws DeliveryException
     */
    public function updateProfile(array $data, $id)
    {
        try {
            $profile = $this->findOrFail($id);
            return $this->update($data, $id);
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Update KYC status
     *
     * @param array $data
     * @param $id
     * @return mixed
     * @throws DeliveryException
     */
    public function updateKycStatus(array $data, $id)
    {
        try {
            $profile = $this->findOrFail($id);
            return $this->update($data, $id);
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Update availability status
     *
     * @param array $data
     * @param $id
     * @return mixed
     * @throws DeliveryException
     */
    public function updateAvailabilityStatus(array $data, $id)
    {
        try {
            $profile = $this->findOrFail($id);
            return $this->update($data, $id);
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }
}
