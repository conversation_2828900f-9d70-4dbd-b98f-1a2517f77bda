<?php

namespace Marvel\Database\Repositories;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Marvel\Database\Models\DeliveryAgentEarning;
use Marvel\Database\Models\DeliveryAgentTransaction;
use Marvel\Database\Models\DeliveryAgentWithdrawal;
use Marvel\Database\Models\User;
use Marvel\Exceptions\DeliveryException; // Keep for type hinting
use Marvel\Helpers\DeliveryConstants;
use Prettus\Repository\Criteria\RequestCriteria;
use Marvel\Helpers\DeliveryExceptionHelper; // Add helper
use Prettus\Repository\Exceptions\RepositoryException;

class DeliveryAgentWithdrawalRepository extends BaseRepository
{
    /**
     * @var array
     */
    protected $fieldSearchable = [
        'delivery_agent_user_id',
        'status',
    ];

    /**
     * Configure the Model
     *
     * @return string
     */
    public function model()
    {
        return DeliveryAgentWithdrawal::class;
    }

    /**
     * @throws RepositoryException
     */
    public function boot()
    {
        try {
            $this->pushCriteria(app(RequestCriteria::class));
        } catch (RepositoryException $e) {
            throw new RepositoryException($e);
        }
    }

    /**
     * Get withdrawals for a delivery agent
     *
     * @param User $agent
     * @param array $params
     * @return mixed
     */
    public function getWithdrawalsForAgent(User $agent, array $params = [])
    {
        $limit = $params['limit'] ?? 15;
        $status = $params['status'] ?? null;

        $query = $this->model->where('delivery_agent_user_id', $agent->id);

        if ($status) {
            $query->where('status', $status);
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($limit);
    }

    /**
     * Request a withdrawal
     *
     * @param array $data
     * @param User $agent
     * @return mixed
     * @throws DeliveryException
     */
    public function requestWithdrawal(array $data, User $agent)
    {
        try {
            // Get agent's earnings
            $earnings = DeliveryAgentEarning::where('delivery_agent_user_id', $agent->id)->first();

            if (!$earnings) {
                DeliveryExceptionHelper::notFound(DeliveryConstants::NO_EARNINGS_FOUND);
            }

            // Check if amount is valid
            $amount = $data['amount'];
            if ($amount <= 0) {
                DeliveryExceptionHelper::badRequest(DeliveryConstants::INVALID_WITHDRAWAL_AMOUNT);
            }

            // Check if agent has enough balance
            if ($earnings->current_balance < $amount) {
                DeliveryExceptionHelper::badRequest(DeliveryConstants::INSUFFICIENT_BALANCE);
            }

            // Check if agent has payment info
            if (!$earnings->payment_info) {
                DeliveryExceptionHelper::badRequest(DeliveryConstants::NO_PAYMENT_INFO);
            }

            // Use a database transaction to ensure data consistency
            return DB::transaction(function () use ($agent, $data, $earnings, $amount) {
                // Create withdrawal request
                $withdrawal = $this->create([
                    'delivery_agent_user_id' => $agent->id,
                    'amount' => $amount,
                    'status' => 'PENDING',
                    'payment_method_details' => $earnings->payment_info,
                    'requested_at' => now(),
                    'notes' => $data['notes'] ?? null,
                ]);

                // Update earnings
                $earnings->current_balance -= $amount;
                $earnings->pending_withdrawal_amount += $amount;
                $earnings->save();

                // Create transaction record
                try {
                    $transactionRepository = app(DeliveryAgentTransactionRepository::class);
                    $transactionRepository->createWithdrawalTransaction([
                        'delivery_agent_user_id' => $agent->id,
                        'withdrawal_id' => $withdrawal->id,
                        'amount' => $amount,
                        'status' => DeliveryAgentTransaction::STATUS_PENDING,
                        'payment_method' => $earnings->payment_info['method'] ?? DeliveryAgentTransaction::METHOD_OTHER,
                        'provider' => $earnings->payment_info['provider'] ?? null,
                        'payment_details' => $earnings->payment_info,
                        'notes' => $data['notes'] ?? 'Withdrawal request',
                    ]);
                } catch (\Exception $e) {
                    // Log error but don't stop the process since it's within a transaction
                    // If we throw here, the entire transaction will be rolled back
                    Log::error('Failed to create transaction record: ' . $e->getMessage());
                }

                return $withdrawal;
            });
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Approve a withdrawal request
     *
     * @param DeliveryAgentWithdrawal $withdrawal
     * @param User $admin
     * @param array $data
     * @return mixed
     * @throws DeliveryException
     */
    public function approveWithdrawal(DeliveryAgentWithdrawal $withdrawal, User $admin, array $data = [])
    {
        try {
            // Check if withdrawal is in PENDING status
            if ($withdrawal->status !== 'PENDING') {
                DeliveryExceptionHelper::badRequest(DeliveryConstants::INVALID_WITHDRAWAL_STATUS);
            }

            // Update withdrawal
            $withdrawal = $this->update([
                'status' => 'APPROVED',
                'processed_by_user_id' => $admin->id,
                'processed_at' => now(),
                'notes' => $data['notes'] ?? $withdrawal->notes,
            ], $withdrawal->id);

            return $withdrawal;
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Process a withdrawal request
     *
     * @param DeliveryAgentWithdrawal $withdrawal
     * @param User $admin
     * @param array $data
     * @return mixed
     * @throws DeliveryException
     */
    public function processWithdrawal(DeliveryAgentWithdrawal $withdrawal, User $admin, array $data = [])
    {
        try {
            // Check if withdrawal is in APPROVED status
            if ($withdrawal->status !== 'APPROVED') {
                DeliveryExceptionHelper::badRequest(DeliveryConstants::INVALID_WITHDRAWAL_STATUS);
            }

            // Update withdrawal
            $withdrawal = $this->update([
                'status' => 'PROCESSING',
                'processed_by_user_id' => $admin->id,
                'processed_at' => now(),
                'notes' => $data['notes'] ?? $withdrawal->notes,
            ], $withdrawal->id);

            return $withdrawal;
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Complete a withdrawal request
     *
     * @param DeliveryAgentWithdrawal $withdrawal
     * @param User $admin
     * @param array $data
     * @return mixed
     * @throws DeliveryException
     */
    public function completeWithdrawal(DeliveryAgentWithdrawal $withdrawal, User $admin, array $data)
    {
        try {
            // Check if withdrawal is in PROCESSING status
            if ($withdrawal->status !== 'PROCESSING') {
                DeliveryExceptionHelper::badRequest(DeliveryConstants::INVALID_WITHDRAWAL_STATUS);
            }

            // Validate transaction reference
            if (empty($data['transaction_reference'])) {
                DeliveryExceptionHelper::badRequest(DeliveryConstants::TRANSACTION_REFERENCE_REQUIRED);
            }

            // Use a database transaction to ensure data consistency
            return DB::transaction(function () use ($withdrawal, $admin, $data) {
                // Update withdrawal
                $withdrawal = $this->update([
                    'status' => 'COMPLETED',
                    'processed_by_user_id' => $admin->id,
                    'processed_at' => now(),
                    'transaction_reference' => $data['transaction_reference'],
                    'notes' => $data['notes'] ?? $withdrawal->notes,
                ], $withdrawal->id);

                // Update earnings
                $earnings = DeliveryAgentEarning::where('delivery_agent_user_id', $withdrawal->delivery_agent_user_id)->first();
                if ($earnings) {
                    $earnings->pending_withdrawal_amount -= $withdrawal->amount;
                    $earnings->withdrawn_amount += $withdrawal->amount;
                    $earnings->save();
                }

                // Update transaction record
                try {
                    $transaction = DeliveryAgentTransaction::where('reference_type', DeliveryAgentTransaction::REF_WITHDRAWAL)
                        ->where('reference_id', $withdrawal->id)
                        ->first();

                    if ($transaction) {
                        $transaction->status = DeliveryAgentTransaction::STATUS_COMPLETED;
                        $transaction->transaction_id = $data['transaction_reference'];
                        $transaction->processed_at = now();
                        $transaction->save();
                    } else {
                        // Create a new transaction record if one doesn't exist
                        $transactionRepository = app(DeliveryAgentTransactionRepository::class);
                        $transactionRepository->createWithdrawalTransaction([
                            'delivery_agent_user_id' => $withdrawal->delivery_agent_user_id,
                            'withdrawal_id' => $withdrawal->id,
                            'amount' => -1 * abs($withdrawal->amount),
                            'status' => DeliveryAgentTransaction::STATUS_COMPLETED,
                            'payment_method' => $withdrawal->payment_method_details['method'] ?? DeliveryAgentTransaction::METHOD_OTHER,
                            'provider' => $withdrawal->payment_method_details['provider'] ?? null,
                            'payment_details' => $withdrawal->payment_method_details,
                            'transaction_id' => $data['transaction_reference'],
                            'notes' => $data['notes'] ?? 'Withdrawal completed',
                            'created_by_user_id' => $admin->id,
                            'processed_at' => now(),
                        ]);
                    }
                } catch (\Exception $e) {
                    // Log error but don't stop the process since it's within a transaction
                    // If we throw here, the entire transaction will be rolled back
                    Log::error('Failed to update transaction record: ' . $e->getMessage());
                }

                return $withdrawal;
            });
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Reject a withdrawal request
     *
     * @param DeliveryAgentWithdrawal $withdrawal
     * @param User $admin
     * @param array $data
     * @return mixed
     * @throws DeliveryException
     */
    public function rejectWithdrawal(DeliveryAgentWithdrawal $withdrawal, User $admin, array $data)
    {
        try {
            // Check if withdrawal is in PENDING or APPROVED status
            if (!in_array($withdrawal->status, ['PENDING', 'APPROVED', 'PROCESSING'])) {
                DeliveryExceptionHelper::badRequest(DeliveryConstants::INVALID_WITHDRAWAL_STATUS);
            }

            // Validate rejection reason
            if (empty($data['notes'])) {
                DeliveryExceptionHelper::badRequest(DeliveryConstants::REJECTION_REASON_REQUIRED);
            }

            // Use a database transaction to ensure data consistency
            return DB::transaction(function () use ($withdrawal, $admin, $data) {
                // Update withdrawal
                $withdrawal = $this->update([
                    'status' => 'REJECTED',
                    'processed_by_user_id' => $admin->id,
                    'processed_at' => now(),
                    'notes' => $data['notes'],
                ], $withdrawal->id);

                // Update earnings
                $earnings = DeliveryAgentEarning::where('delivery_agent_user_id', $withdrawal->delivery_agent_user_id)->first();
                if ($earnings) {
                    $earnings->pending_withdrawal_amount -= $withdrawal->amount;
                    $earnings->current_balance += $withdrawal->amount;
                    $earnings->save();
                }

                // Update transaction record if it exists
                try {
                    $transaction = DeliveryAgentTransaction::where('reference_type', DeliveryAgentTransaction::REF_WITHDRAWAL)
                        ->where('reference_id', $withdrawal->id)
                        ->first();

                    if ($transaction) {
                        $transaction->status = DeliveryAgentTransaction::STATUS_REJECTED;
                        $transaction->notes = $data['notes'];
                        $transaction->save();
                    }
                } catch (\Exception $e) {
                    // Log error but don't stop the process since it's within a transaction
                    Log::error('Failed to update transaction record for rejected withdrawal: ' . $e->getMessage());
                }

                return $withdrawal;
            });
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }
}
