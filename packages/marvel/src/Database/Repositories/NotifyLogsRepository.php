<?php

namespace Marvel\Database\Repositories;

use Illuminate\Support\Facades\Log;
use Marvel\Database\Models\NotifyLogs;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Exceptions\RepositoryException;


class NotifyLogsRepository extends BaseRepository
{

    protected $fieldSearchable = [
        'notify_type' => 'like',
        'language',
    ];

    public function boot()
    {
        try {
            $this->pushCriteria(app(RequestCriteria::class));
        } catch (RepositoryException $exception) {
            // Log the exception if needed
            Log::error('Error pushing criteria: ' . $exception->getMessage());
        }
    }


    /**
     * Configure the Model
     **/
    public function model()
    {
        return NotifyLogs::class;
    }

    /**
     * Store a notification in the database.
     *
     * @param array $data
     * @return \Marvel\Database\Models\NotifyLogs
     */
    public function storeNotification(array $data)
    {
        try {
            // Map the input data to the NotifyLogs model fields
            $notificationData = [
                'receiver' => $data['receiver'],
                'sender' => $data['sender'] ?? null,
                'notify_type' => $data['notify_type'],
                'notify_receiver_type' => $data['notify_receiver_type'] ?? 'user',
                'is_read' => false,
                'notify_tracker' => $data['notify_id'] ?? null,
                'notify_text' => $data['options']['message'] ?? null,
            ];

            // Create and return the notification log
            return $this->create($notificationData);
        } catch (\Exception $e) {
            Log::error('Failed to store notification: ' . $e->getMessage());
            return null;
        }
    }
}
