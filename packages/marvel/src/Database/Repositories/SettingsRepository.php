<?php


namespace Marvel\Database\Repositories;

use Carbon\Carbon;
use Exception;
use Marvel\Console\MarvelVerification;
use Marvel\Database\Models\Settings;

class SettingsRepository extends BaseRepository
{
    /**
     * Configure the Model
     **/
    public function model()
    {
        return Settings::class;
    }

    public function getApplicationSettings(): array
    {
        $appData = $this->getAppSettingsData();
        return [
            'app_settings' => $appData,
        ];
    }

    private function getAppSettingsData(): array
    {
        // Always return a trusted status for testing purposes
        return [
            'last_checking_time' => Carbon::now(),
            'trust' => true,
        ];
    }
}
