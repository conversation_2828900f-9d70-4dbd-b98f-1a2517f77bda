<?php

namespace Marvel\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

use Carbon\Carbon;

class DefaultPlanSeeder extends Seeder
{
    public function run(): void
    {
        $now = Carbon::now();

        DB::table('plans')->insert([
            [
                'name' => 'Free',
                'price' => 0,
                'currency' => 'XAF',
                'description' => 'Basic free plan for new vendors',
                'features' => json_encode([
                    'Access to one store',
                    'Product review management',
                    'Max 3 products uploads',
                    "Free Logistics",
                    "Referral Earnings",
                    "Training and on-boarding",
                    "Multi Language"
                ]),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Starter',
                'price' => 3000,
                'currency' => 'XAF',
                'description' => 'For growing vendors with more needs',
                'features' => json_encode([
                    'Access to one store',
                    'Product review management',
                    'Max 10 products uploads',
                    'Free Logistics',
                    'Referral Earnings',
                    'Training and webinars',
                    'Multi Language',
                ]),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Growth',
                'price' => 10000,
                'currency' => 'XAF',
                'description' => 'Good for established vendors',
                'features' => json_encode([
                    'Access to three stores',
                    'Product review management',
                    'Max 500 products uploads',
                    'Referral Earnings',
                    'Free Logistics',
                    'Chat with customers',
                    'Monthly sales reports',
                    'Discount and coupon generator',
                    'Training and webinars',
                    'Multi language',
                ]),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Pro',
                'price' => 25000,
                'currency' => 'XAF',
                'description' => 'Good for established vendors',
                'features' => json_encode([
                    'Access to six stores',
                    'Product review management',
                    'Max 3000 products uploads',
                    'Referral Earnings',
                    'Free Logistics',
                    'Chat with customers',
                    'Monthly sales reports',
                    'Discount and coupon generator',
                    'Training and webinars',
                    'Multi language',
                    'Dedicated support',
                    'AI access',
                ]),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Enterprise',
                'price' => 50000,
                'currency' => 'XAF',
                'description' => 'Good for established vendors',
                'features' => json_encode([
                    'Unlimited stores',
                    'Product review management',
                    'Max 10000 products uploads',
                    'Referral Earnings',
                    'Free Logistics',
                    'Chat with customers',
                    'Monthly sales reports',
                    'Discount and coupon generator',
                    'Training and webinars',
                    'Multi language',
                    'Dedicated support',
                    'Promo campaigns & flash sales access',
                    'Access to affiliate programs',
                    'AI access',
                ]),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Top',
                'price' => 80000,
                'currency' => 'XAF',
                'description' => 'Good for established vendors',
                'features' => json_encode([
                    'Unlimited stores',
                    'Product review management',
                    'Unlimited product uploads',
                    'Referral Earnings',
                    'Free Logistics',
                    'Chat with customers',
                    'Monthly sales reports',
                    'Discount and coupon generator',
                    'Training and webinars',
                    'Multi language',
                    'Dedicated support',
                    'Promo campaigns & flash sales access',
                    'Access to affiliate programs',
                    'AI access',
                ]),
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ]);
    }
}
