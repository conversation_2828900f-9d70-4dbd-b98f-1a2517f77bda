<?php

namespace Marvel\Database\Seeders;

use Illuminate\Database\Seeder;
use Marvel\Database\Models\User; // Corrected namespace if User is in Marvel\Database\Models
use Faker\Factory as Faker;
use Illuminate\Support\Facades\Hash;
use Marvel\Database\Models\DeliveryAgentProfile;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\DeliveryStatusLog;
use Marvel\Enums\Permission;
use Spatie\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role;

class DeliveryAgentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // It's good practice to clear the permission cache before seeding to avoid issues.
        app()->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();

        $faker = Faker::create();

        // Ensure permissions and roles exist
        $this->createPermissions();

        // Central point for Cairo
        $centerLat = 30.0444;
        $centerLon = 31.2357;

        // Define the spread for latitude and longitude (e.g., 0.1 degrees for a city area)
        $latSpread = 0.1; // Approximately 11 km
        $lonSpread = 0.1; // Approximately 10 km at this latitude

        // 1. Create Specific Delivery Agents for Nearest Agent Tests
        $this->command->info("Creating/updating specific delivery agents for tests...");

        // Agent for it_can_find_nearest_available_agents (nearby)
        $this->createDeliveryAgentWithLocation(
            ['lat' => 40.7138, 'lng' => -74.0070], // ~0.15km from NY shop
            'Nearby Agent',
            '<EMAIL>'
        );

        // Agent for it_can_find_nearest_available_agents (far)
        $this->createDeliveryAgentWithLocation(
            ['lat' => 40.8500, 'lng' => -73.8000], // ~25km away (Queens to Manhattan)
            'Far Agent',
            '<EMAIL>'
        );

        // Agent for it_filters_agents_by_availability_status (offline)
        $this->createDeliveryAgentWithLocation(
            ['lat' => 40.7118, 'lng' => -74.0050],
            'Offline Agent',
            '<EMAIL>',
            'APPROVED',
            'OFFLINE'
        );

        // Agent for it_filters_agents_with_rejected_kyc (rejected KYC)
        $this->createDeliveryAgentWithLocation(
            ['lat' => 40.7138, 'lng' => -74.0070],
            'Rejected KYC Agent',
            '<EMAIL>',
            'REJECTED'
        );

        // Agent for it_filters_inactive_agents (inactive)
        $this->createDeliveryAgentWithLocation(
            ['lat' => 40.7118, 'lng' => -74.0050],
            'Inactive Agent',
            '<EMAIL>',
            'APPROVED',
            'ONLINE',
            false // is_active = false
        );

        // Agent for it_filters_agents_without_location (no location)
        $this->createDeliveryAgentWithLocation(
            null, // No location
            'No Location Agent',
            '<EMAIL>'
        );

        // Agent for it_can_find_nearest_agents_for_consolidated_multi_vendor_order
        $this->createDeliveryAgentWithLocation(
            ['lat' => 34.0530, 'lng' => -118.2440], // Near LA shop
            'LA Agent',
            '<EMAIL>'
        );

        // Agent for it_can_find_nearest_agents_for_split_multi_vendor_order
        $this->createDeliveryAgentWithLocation(
            ['lat' => 37.7750, 'lng' => -122.4200], // Near SF shop
            'SF Agent',
            '<EMAIL>'
        );

        // Agent for it_can_find_agent_for_parent_order_with_a_single_child
        $this->createDeliveryAgentWithLocation(
            ['lat' => 4.0752566, 'lng' => 9.7912631],
            'Single Child Test Agent',
            '<EMAIL>'
        );

        // General active and approved agent with location for other tests
        $this->createDeliveryAgentWithLocation(
            ['lat' => 40.7200, 'lng' => -74.0000], // ~0.9km from NY shop
            'General Active Agent',
            '<EMAIL>'
        );

        $this->command->info("Created/updated specific delivery agents.");

        // 2. Create Specific Shops for Nearest Agent Tests
        $this->command->info("Creating/updating specific shops for tests...");

        // Shop for it_can_find_nearest_available_agents and others
        $this->createShopWithLocation(
            ['lat' => 40.7128, 'lng' => -74.0060, 'city' => 'New York', 'country' => 'USA'],
            'New York Test Shop'
        );

        // Shop 1 for multi-vendor consolidated order
        $this->createShopWithLocation(
            ['lat' => 34.0522, 'lng' => -118.2437], // Los Angeles
            'LA Shop 1'
        );

        // Shop 2 for multi-vendor consolidated order (very close to LA Shop 1)
        $this->createShopWithLocation(
            ['lat' => 34.0550, 'lng' => -118.2450],
            'LA Shop 2'
        );

        // Shop for multi-vendor split order (San Francisco)
        $this->createShopWithLocation(
            ['lat' => 37.7749, 'lng' => -122.4194], // San Francisco
            'SF Shop'
        );

        // Shop for single child order test
        $this->createShopWithLocation(
            ['lat' => 4.0432672, 'lng' => 9.7132527],
            'Single Child Test Shop'
        );

        $this->command->info("Created/updated specific shops.");

        // 3. Create a small number of random agents and shops for general data
        // NOTE: This part is not idempotent and assumes a fresh database for the random records.
        // It's generally better to keep specific/test data separate from random/dev data.
        $numberOfRandomAgents = 5;
        $this->command->info("Creating {$numberOfRandomAgents} random delivery agents...");
        User::factory($numberOfRandomAgents)->create()->each(function (User $user) use ($faker, $centerLat, $centerLon, $latSpread, $lonSpread) {
            $user->givePermissionTo(Permission::DELIVERY_AGENT);
            $user->is_active = $faker->boolean(90);
            $user->save();

            $kycStatus = $faker->boolean(80) ? 'APPROVED' : $faker->randomElement(['PENDING', 'REJECTED']);
            $availabilityStatus = $faker->boolean(85) ? 'ONLINE' : $faker->randomElement(['OFFLINE', 'BUSY']);

            $currentLocation = null;
            if ($faker->boolean(95)) {
                $latitude = $faker->randomFloat(6, $centerLat - $latSpread, $centerLat + $latSpread);
                $longitude = $faker->randomFloat(6, $centerLon - $lonSpread, $centerLon + $lonSpread);
                $currentLocation = ['lat' => $latitude, 'lng' => $longitude];
            }

            DeliveryAgentProfile::factory()->create([
                'user_id' => $user->id,
                'availability_status' => $availabilityStatus,
                'kyc_status' => $kycStatus,
                'current_location' => $currentLocation,
            ]);
        });
        $this->command->info("Created {$numberOfRandomAgents} random delivery agents.");

        $numberOfRandomShops = 5;
        $this->command->info("Creating {$numberOfRandomShops} random shops...");
        $randomShops = Shop::factory($numberOfRandomShops)->create()->each(function (Shop $shop) use ($faker, $centerLat, $centerLon, $latSpread, $lonSpread) {
            $latitude = $faker->randomFloat(6, $centerLat - $latSpread, $centerLat + $latSpread);
            $longitude = $faker->randomFloat(6, $centerLon - $lonSpread, $centerLon + $lonSpread);
            $shop->settings = array_merge($shop->settings ?? [], [
                'location' => [
                    'lat' => $latitude,
                    'lng' => $longitude,
                ],
            ]);
            $shop->is_active = true;
            $shop->save();
        });
        $this->command->info("Created {$numberOfRandomShops} random shops.");

        // 4. Create Orders (a mix of single and multi-vendor)
        // NOTE: This part is also not idempotent.
        $numberOfOrders = 10; // Reduced for faster seeding
        $this->command->info("Creating {$numberOfOrders} orders...");

        $allShops = Shop::all(); // Get all shops, including specific ones

        for ($i = 0; $i < $numberOfOrders; $i++) {
            $isMultiVendor = $faker->boolean(50); // Higher chance for multi-vendor for testing

            $customer = User::factory()->create();
            // Ensure the customer role exists and assign it
            $customerRole = Role::firstOrCreate(['name' => Permission::CUSTOMER, 'guard_name' => 'api']);
            if (!$customer->hasRole(Permission::CUSTOMER)) {
                $customer->assignRole(Permission::CUSTOMER);
            }

            $parentOrder = new Order([
                'tracking_number' => 'ORDER-' . rand(10000, 99999),
                'customer_id' => $customer->id,
                'shop_id' => null,
                'order_status' => 'order-processing',
                'payment_status' => 'payment-success',
                'amount' => $faker->randomFloat(2, 10, 500),
                'requires_delivery' => true,
                'delivery_id' => null,
                'customer_contact' => $faker->phoneNumber,
                'parent_id' => null,
            ]);
            $parentOrder->save();

            if ($isMultiVendor && $allShops->count() >= 2) {
                $numChildOrders = $faker->numberBetween(2, min(3, $allShops->count()));
                $selectedShops = $faker->randomElements($allShops->all(), $numChildOrders);

                foreach ($selectedShops as $shop) {
                    $childOrder = new Order([
                        'tracking_number' => 'ORDER-' . rand(10000, 99999),
                        'customer_id' => $parentOrder->customer_id,
                        'shop_id' => $shop->id,
                        'order_status' => 'order-processing',
                        'payment_status' => 'payment-success',
                        'amount' => $faker->randomFloat(2, 10, 200),
                        'requires_delivery' => true,
                        'delivery_id' => null,
                        'customer_contact' => $faker->phoneNumber,
                        'parent_id' => $parentOrder->id,
                    ]);
                    $childOrder->save();
                    $this->createDeliveryStatusLogs($childOrder, $faker);
                }
            } else {
                $shop = $faker->randomElement($allShops);
                $childOrder = new Order([
                    'tracking_number' => 'ORDER-' . rand(10000, 99999),
                    'customer_id' => $parentOrder->customer_id,
                    'shop_id' => $shop->id,
                    'order_status' => 'order-processing',
                    'payment_status' => 'payment-success',
                    'amount' => $faker->randomFloat(2, 10, 200),
                    'requires_delivery' => true,
                    'delivery_id' => null,
                    'customer_contact' => $faker->phoneNumber,
                    'parent_id' => $parentOrder->id,
                ]);
                $childOrder->save();
                $this->createDeliveryStatusLogs($childOrder, $faker);
            }
        }

        $this->command->info("Created {$numberOfOrders} orders, including multi-vendor scenarios.");
    }

    /**
     * Create permissions needed for seeding.
     *
     * @return void
     */
    protected function createPermissions()
    {
        // Create permissions if they don't exist
        $permissions = [
            Permission::STORE_OWNER,
            Permission::STAFF,
            Permission::CUSTOMER,
            Permission::DELIVERY_AGENT,
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }

        // Create roles and assign permissions
        $deliveryAgentRole = Role::firstOrCreate(['name' => 'delivery_agent', 'guard_name' => 'api']);
        if (!$deliveryAgentRole->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            $deliveryAgentRole->givePermissionTo(Permission::DELIVERY_AGENT);
        }
        
        $customerRole = Role::firstOrCreate(['name' => Permission::CUSTOMER, 'guard_name' => 'api']);
        if (!$customerRole->hasPermissionTo(Permission::CUSTOMER)) {
            $customerRole->givePermissionTo(Permission::CUSTOMER);
        }
    }

    /**
     * Helper to create a shop with a specific location.
     * Made idempotent using updateOrCreate.
     *
     * @param array $location
     * @param string $name
     * @return Shop
     */
    protected function createShopWithLocation(array $location, string $name): Shop
    {
        $slug = \Illuminate\Support\Str::slug($name);
        // Using firstOrCreate and then updating to handle complex factory dependencies like owner_id.
        $shop = Shop::firstOrCreate(
            ['slug' => $slug],
            Shop::factory()->make(['name' => $name, 'slug' => $slug])->toArray()
        );

        // Ensure settings are updated on subsequent runs
        $shop->settings = array_merge($shop->settings ?? [], ['location' => $location]);
        $shop->is_active = true;
        $shop->save();

        return $shop;
    }

    /**
     * Helper to create a delivery agent with a specific location and status.
     * Made idempotent to prevent errors on re-running the seeder.
     *
     * @param array|null $location
     * @param string $name
     * @param string $email
     * @param string $kycStatus
     * @param string $availabilityStatus
     * @param bool $isActive
     * @return User
     */
    protected function createDeliveryAgentWithLocation(
        ?array $location,
        string $name,
        string $email,
        string $kycStatus = 'APPROVED',
        string $availabilityStatus = 'ONLINE',
        bool $isActive = true
    ): User {
        // Use updateOrCreate to find user by email or create them if they don't exist.
        $agent = User::updateOrCreate(
            ['email' => $email],
            [
                'name' => $name,
                'password' => Hash::make('password'),
                'is_active' => $isActive,
            ]
        );

        // Assign permission only if the user doesn't already have it.
        if (!$agent->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            $agent->givePermissionTo(Permission::DELIVERY_AGENT);
        }

        // Also make profile creation/update idempotent.
        DeliveryAgentProfile::updateOrCreate(
            ['user_id' => $agent->id],
            [
                'availability_status' => $availabilityStatus,
                'kyc_status' => $kycStatus,
                'current_location' => $location,
            ]
        );
        return $agent;
    }

    /**
     * Create delivery status logs for an order.
     *
     * @param Order $order
     * @param \Faker\Generator $faker
     * @return void
     */
    protected function createDeliveryStatusLogs(Order $order, \Faker\Generator $faker)
    {
        if ($faker->boolean(70)) { // 70% of orders get status logs
            $statuses = ['PENDING_ASSIGNMENT', 'ASSIGNED', 'ACCEPTED_BY_AGENT', 'PICKED_UP', 'IN_TRANSIT', 'REACHED_DESTINATION', 'DELIVERED', 'COMPLETED'];
            $numLogs = $faker->numberBetween(1, count($statuses));
            $selectedStatuses = $faker->randomElements($statuses, $numLogs, false);
            sort($selectedStatuses); // Ensure chronological order

            $deliveryAgent = User::permission(Permission::DELIVERY_AGENT)->inRandomOrder()->first();
            if (!$deliveryAgent) {
                return; // Skip if no delivery agents exist
            }

            // Ensure the order has a delivery_id before creating logs
            if (!$order->delivery_id) {
                // Create a dummy delivery record if not already assigned
                $delivery = \Marvel\Database\Models\Delivery::create([
                    'order_id' => $order->id,
                    'delivery_agent_user_id' => $deliveryAgent->id,
                    'status' => 'PENDING_ASSIGNMENT',
                    'assignment_type' => 'AUTOMATIC',
                    'delivery_fee' => 0,
                    'assigned_by_user_id' => $deliveryAgent->id, // Assign to agent for simplicity
                    'assigned_at' => now(),
                    'notes_by_admin' => 'Dummy delivery for status logs',
                ]);
                $order->delivery_id = $delivery->id;
                $order->save();
            }

            foreach ($selectedStatuses as $status) {
                DeliveryStatusLog::create([
                    'delivery_id' => $order->delivery_id,
                    'status' => $status,
                    'user_id' => $deliveryAgent->id,
                    'notes' => "Order status changed to {$status}",
                    'created_at' => $faker->dateTimeBetween('-1 month', 'now'),
                ]);
            }
        }
    }
}