<?php

namespace Marvel\Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Marvel\Database\Models\AccountBalance;
use Marvel\Database\Models\User;

class AccountBalanceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
         $users = User::all();

        foreach ($users as $user) {
            AccountBalance::firstOrCreate(
                ['user_id' => $user->id],
                [
                    'total_earnings' => 0.00,
                    'current_balance' => 0.00,
                ]
            );
        }

        $this->command->info('Account balances created for all users.');
    }
}
