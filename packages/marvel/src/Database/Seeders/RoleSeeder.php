<?php

namespace Marvel\Database\Seeders;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
         $now = Carbon::now();

        $roles = [
            ['name' => 'admin', 'guard_name' => 'api', 'created_at' => $now, 'updated_at' => $now],
            ['name' => 'super_admin', 'guard_name' => 'api', 'created_at' => $now, 'updated_at' => $now],
            ['name' => 'store_owner', 'guard_name' => 'api', 'created_at' => $now, 'updated_at' => $now],
        ];

        DB::table('roles')->insert($roles);
    }
}
