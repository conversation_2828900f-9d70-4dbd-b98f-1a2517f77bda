<?php

namespace Marvel\Exceptions;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Throwable;

class DeliveryException extends HttpException
{
    /**
     * @var string|null
     */
    protected $reason;

    /**
     * Create a new delivery exception instance.
     *
     * @param  string  $message
     * @param  string|null  $reason
     * @param  int  $statusCode
     * @param  \Throwable|null  $previous
     * @param  array  $headers
     * @param  int  $code
     * @return void
     */
    public function __construct(
        $message = 'Delivery operation failed',
        $reason = null,
        $statusCode = Response::HTTP_BAD_REQUEST,
        ?Throwable $previous = null,
        array $headers = [],
        $code = 0
    ) {
        parent::__construct($statusCode, $message, $previous, $headers, $code);
        $this->reason = $reason;
    }

    /**
     * Get the reason for the exception.
     *
     * @return string|null
     */
    public function getReason()
    {
        return $this->reason;
    }
}
