<?php

namespace Marvel\Providers;

use Illuminate\Support\ServiceProvider;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\Order;
use Marvel\Observers\DeliveryObserver;
use Marvel\Observers\OrderObserver;

class DeliveryServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(): void
    {
        Delivery::observe(DeliveryObserver::class);
        Order::observe(OrderObserver::class);
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register(): void
    {
        //
    }
}
