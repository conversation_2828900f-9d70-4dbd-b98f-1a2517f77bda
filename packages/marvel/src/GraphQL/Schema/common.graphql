input ConnectTypeBelongsTo {
    connect: ID! @rules(apply: ["exists:types,id"])
}

input ConnectProductOrderPivot {
    product_id: ID! @rules(apply: ["exists:products,id"])
    variation_option_id: ID
    order_quantity: Int
    unit_price: Float
    subtotal: Float
}

type UserAddress {
    street_address: String
    country: String
    city: String
    state: String
    zip: String
}

input UserAddressInput {
    street_address: String
    country: String
    city: String
    state: String
    zip: String
}

type GoogleMapLocation {
    lat: Float
    lng: Float
    street_number: String
    route: String
    street_address: String
    city: String
    state: String
    country: String
    zip: String
    formattedAddress: String
}

input GoogleMapLocationInput {
    lat: Float
    lng: Float
    street_number: String
    route: String
    street_address: String
    city: String
    state: String
    country: String
    zip: String
    formattedAddress: String
}

input ConnectBelongsTo {
    connect: ID @rules(apply: ["exists:users,id"])
}

type Attachment {
    thumbnail: String
    original: String
    id: ID
}
input AttachmentInput {
    thumbnail: String
    original: String
    id: ID
}

input SocialInput {
    type: String
    link: String
}

type Feedback {
    id: ID
    user_id: ID
    model_type: String
    model_id: ID
    positive: Boolean
    negative: Boolean
    created_at: String
    updated_at: String
}

type Notifications {
    email: String
    enable: <PERSON><PERSON>an
}
