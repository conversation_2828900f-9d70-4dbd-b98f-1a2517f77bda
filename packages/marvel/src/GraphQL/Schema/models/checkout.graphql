type VerifiedCheckoutData {
    total_tax: Float!
    shipping_charge: Float!
    unavailable_products: [ID]
    wallet_currency: Float
    wallet_amount: Int
}

input CheckoutVerificationInput {
    amount: Float!
    customer_id: ID
    products: [ConnectProductOrderPivot!]!
    billing_address: UserAddressInput
    shipping_address: UserAddressInput
}

extend type Mutation {
    verifyCheckout(
        input: CheckoutVerificationInput! @spread
    ): VerifiedCheckoutData @field(resolver: "CheckoutMutator@verify")
}
