extend type Query {
    attributeValues(shop_id: ID @eq): [AttributeValue!]!
        @field(resolver: "Marvel\\GraphQL\\Queries\\AttributesValueQuery@fetchAttributesValue")
    attributeValue(id: ID! @eq): AttributeValue @find
}

type AttributeValue {
    id: ID!
    value: String!
    meta: String
    attribute_id: ID!
    attribute: Attribute @belongsTo
    language: String!
}

input AttributeBelongTo {
    connect: ID! @rules(apply: ["exists:attributes,id"])
}

input CreateAttributeValueInput {
    value: String!
    meta: String
    attribute_id: ID! @rules(apply: ["exists:attributes,id"])
}
input UpdateAttributeValueInput {
    id: ID!
    value: String!
    meta: String
    attribute_id: ID! @rules(apply: ["exists:attributes,id"])
}

extend type Mutation {
    createAttributeValue(
        input: CreateAttributeValueInput! @spread
    ): AttributeValue @field(resolver: "AttributeValueMutator@store")
    updateAttributeValue(
        input: UpdateAttributeValueInput! @spread
    ): AttributeValue @field(resolver: "AttributeValueMutator@update")
    deleteAttributeValue(id: ID!): AttributeValue
        @field(resolver: "AttributeValueMutator@destroy")
}
