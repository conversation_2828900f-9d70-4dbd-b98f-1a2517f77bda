type Shipping {
    id: ID!
    name: String!
    amount: Float!
    is_global: Boolean
    type: ShippingType!
}

extend type Query {
    shippingClasses(
        text: String @where(operator: "like", key: "name")
        orderBy: _
            @orderBy(
                columns: [
                    "created_at"
                    "updated_at"
                    "name"
                    "amount"
                    "is_global"
                    "type"
                ]
            )
    ): [Shipping!]! @all
    shippingClass(id: ID! @eq): Shipping @find
}

input CreateShippingInput {
    name: String!
    amount: Float
    is_global: Boolean
    type: ShippingType!
}

input UpdateShippingInput {
    id: ID!
    name: String!
    amount: Float!
    is_global: Boolean
    type: ShippingType!
}

extend type Mutation {
    createShipping(input: CreateShippingInput! @spread): Shipping
        @create
        @can(ability: "super_admin")
    updateShipping(input: UpdateShippingInput! @spread): Shipping
        @update
        @can(ability: "super_admin")
    deleteShipping(id: ID!): Shipping @delete @can(ability: "super_admin")
}
