extend type Query {
    categories(
        orderBy: _ @orderBy(columns: ["created_at", "name", "updated_at", "slug"])
        language: String @eq
        name: String @where(operator: "LIKE")
        text: String @where(operator: "like", key: "name")
        parent: ID @eq
        hasType: _ @whereHasConditions(columns: ["slug"])
    ): [Category!]! @paginate(defaultCount: 15)
    # categoriesAlongChildren(
    #     orderBy: _ @orderBy(columns: ["created_at", "name", "updated_at"])
    #     hasType: _ @whereHasConditions(columns: ["slug"])
    # ): [Category] @all
    category(id: ID @eq, slug: String @eq, language: String @eq): Category @find
}

type Category {
    id: ID!
    name: String!
    slug: String!
    parent_id: ID
    language: String
    translated_languages: [String]
    parent: Category @belongsTo
    children: [Category] @hasMany
    sub_categories: [Category] @hasMany
    products_count: Int @count(relation: "products")
    details: String
    image: Attachment
    icon: String
    type: Type! @belongsTo
    products: [Product] @belongsToMany
    created_at: DateTime
    updated_at: DateTime
}

input CreateCategoryInput {
    name: String! @rules(apply: ["required", "max:255"])
    type_id: ID!
    language: String
    parent: ID
    details: String
    image: AttachmentInput
    icon: String
    slug: String @rules(apply: ["required", "max:255"])
}

input UpdateCategoryInput {
    id: ID! @rules(apply: ["required"])
    name: String! @rules(apply: ["max:255"])
    slug: String! @rules(apply: ["max:255"])
    type_id: ID!
    language: String
    parent: ID
    details: String
    image: AttachmentInput
    icon: String
}

extend type Mutation {
    deleteCategory(id: ID!): Category @delete @can(ability: "super_admin")
    createCategory(input: CreateCategoryInput! @spread): Category
        @field(resolver: "CategoryMutator@storeCategory")
        @can(ability: "super_admin")
    updateCategory(input: UpdateCategoryInput! @spread): Category
        @field(resolver: "CategoryMutator@updateCategory")
        @can(ability: "super_admin")
}
