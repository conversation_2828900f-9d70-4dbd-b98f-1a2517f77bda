extend type Query {
    manufacturers(
        text: String @where(operator: "like", key: "name")
        language: String @eq
        is_approved: Boolean @eq
        hasType: _ @whereHasConditions(columns: ["slug"])
        orderBy: _ @orderBy(columns: ["updated_at", "created_at", "name", "id"])
    ): [Manufacturer!]! @paginate(defaultCount: 15)
    manufacturer(slug: String @eq, language: String @eq): Manufacturer @find
    topManufacturers(limit: Int = 15, language: String): [Manufacturer!]!
        @field(resolver: "ManufacturerQuery@topManufacturer")
}

type Manufacturer {
    id: ID!
    name: String!
    slug: String
    type_id: ID
    language: String
    translated_languages: [String]
    products_count: Int
    type: Type! @belongsTo
    is_approved: Boolean
    description: String
    website: String
    socials: [ShopSocials]
    image: Attachment
    cover_image: Attachment
    created_at: DateTime
    updated_at: DateTime
}

input CreateManufacturerInput {
    name: String!
    description: String
        @rules(
            apply: ["max:10000"]
            messages: { max: "Description should be max 10000 character" }
        )
    type_id: ID! @rules(apply: ["exists:types,id"])
    is_approved: Boolean
    website: String
    language: String
    socials: [ShopSocialInput]
    shop_id: ID @rules(apply: ["exists:shops,id"])
    image: AttachmentInput
    cover_image: AttachmentInput
    slug: String
}

input UpdateManufacturerInput {
    id: ID!
    name: String!
    slug: String!
    type_id: ID! @rules(apply: ["exists:types,id"])
    shop_id: ID @rules(apply: ["exists:shops,id"])
    is_approved: Boolean
    website: String
    language: String
    socials: [ShopSocialInput]
    description: String
        @rules(
            apply: ["max:10000"]
            messages: { max: "Description should be max 10000 character" }
        )
    image: AttachmentInput
    cover_image: AttachmentInput
}

extend type Mutation {
    updateManufacturer(input: UpdateManufacturerInput! @spread): Manufacturer
        @field(resolver: "ManufacturerMutator@updateManufacturer")
    createManufacturer(input: CreateManufacturerInput! @spread): Manufacturer
        @field(resolver: "ManufacturerMutator@storeManufacturer")
    deleteManufacturer(id: ID!): Manufacturer
        @field(resolver: "ManufacturerMutator@deleteManufacturer")
}
