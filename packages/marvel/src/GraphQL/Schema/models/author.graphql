extend type Query {
    authors(
        text: String @where(operator: "like", key: "name")
        is_approved: Boolean @eq
        language: String @eq
        orderBy: _ @orderBy(columns: ["updated_at", "created_at", "name", "id"])
    ): [Author!]! @paginate(defaultCount: 15)
    topAuthors(limit: Int = 15, language: String): [Author!]!
        @field(resolver: "AuthorQuery@topAuthor")
    author(slug: String @eq, language: String @eq): Author @find
}

type Author {
    id: ID!
    name: String!
    is_approved: Boolean
    language: String
    translated_languages: [String]
    slug: String
    bio: String
    quote: String
    products_count: Int
    born: String
    death: String
    languages: String
    socials: [ShopSocials]
    image: Attachment
    cover_image: Attachment
    created_at: DateTime
    updated_at: DateTime
}

input CreateAuthorInput {
    name: String!
    is_approved: Boolean
    language: String
    bio: String
    quote: String
    born: String
    death: String
    languages: String
    socials: [ShopSocialInput]
    shop_id: ID @rules(apply: ["exists:shops,id"])
    image: AttachmentInput
    cover_image: AttachmentInput
    slug: String
}
input UpdateAuthorInput {
    id: ID!
    name: String!
    slug: String!
    language: String
    is_approved: Boolean
    shop_id: ID @rules(apply: ["exists:shops,id"])
    bio: String
    quote: String
    born: String
    death: String
    languages: String
    socials: [ShopSocialInput]
    image: AttachmentInput
    cover_image: AttachmentInput
}

extend type Mutation {
    createAuthor(input: CreateAuthorInput! @spread): Author
        @field(resolver: "AuthorMutator@storeAuthor")
    updateAuthor(input: UpdateAuthorInput! @spread): Author
        @field(resolver: "AuthorMutator@updateAuthor")
    deleteAuthor(id: ID!): Author @field(resolver: "AuthorMutator@deleteAuthor")
}
