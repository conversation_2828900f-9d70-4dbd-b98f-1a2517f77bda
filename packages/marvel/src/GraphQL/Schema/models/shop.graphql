extend type Query {
    shops(
        text: String @where(operator: "like", key: "name")
        is_active: Boolean @eq
        orderBy: String
        sortedBy: String
        hasUsers: _ @whereHasConditions(columns: ["name"])
    ): [Shop]
        @paginate(
            builder: "Marvel\\GraphQL\\Queries\\ShopQuery@fetchShops"
            defaultCount: 15
        )
    staffs(shop_id: ID!, orderBy: String, sortedBy: String): [User]
        @paginate(
            builder: "Marvel\\GraphQL\\Queries\\UserQuery@fetchStaff"
            defaultCount: 15
        )
    shop(id: ID @eq, slug: String @eq): Shop @find
    followedShopsPopularProducts(limit: Int): [Product]
        @field(resolver: "ShopQuery@followedShopsPopularProducts")

    findShopDistance(lat: String, lng: String): [Shop]
        @field(resolver: "ShopQuery@findShopDistance")
}

type Shop {
    id: ID!
    owner_id: ID!
    owner: User @belongsTo
    staffs: [User] @hasMany
    refunds: [Refund] @hasMany
    is_active: Boolean!
    orders_count: Int @count(relation: "orders")
    products_count: Int @count(relation: "products")
    balance: Balance @hasOne
    name: String!
    slug: String!
    description: String
    cover_image: Attachment
    logo: Attachment
    address: UserAddress
    settings: ShopSettings
    distance: Float
    lat: Float
    lng: Float
    ownership_history: OwnershipTransfer @hasOne
    created_at: DateTime
    updated_at: DateTime
}

type ShopMaintenance {
    start: String
    title: String
    until: String
    description: String
    image: Attachment
}

type ShopSettings {
    socials: [ShopSocials]
    contact: String
    location: Location
    website: String
    notifications: Notifications
    isShopUnderMaintenance: Boolean
    shopMaintenance: ShopMaintenance
}

type Location {
    lat: Float
    lng: Float
    city: String
    state: String
    country: String
    street_address: String
    zip: String
    formattedAddress: String
}

type ShopSocials {
    icon: String
    url: String
}

type Balance {
    id: ID!
    admin_commission_rate: Float
    shop: Shop @belongsTo
    total_earnings: Float
    withdrawn_amount: Float
    current_balance: Float
    payment_info: PaymentInfo
}

type FollowedShop {
    attached: [ID]
    detached: [ID]
    updated: [ID]
}

input BalanceInput {
    id: ID
    payment_info: PaymentInfoInput
}

input PaymentInfoInput {
    account: Float
    name: String
    email: String
    bank: String
}

type PaymentInfo {
    account: String
    name: String
    email: String
    bank: String
}

input CreateShopInput {
    name: String!
    description: String
        @rules(
            apply: ["max:10000"]
            messages: { max: "Description should be max 10000 character" }
        )
    cover_image: AttachmentInput
    logo: AttachmentInput
    address: UserAddressInput
    settings: String
    categories: [ID]
    balance: BalanceInput
    settings: ShopSettingsInput
}
input UpdateShopInput {
    id: ID!
    name: String!
    slug: String!
    description: String
        @rules(
            apply: ["max:10000"]
            messages: { max: "Description should be max 10000 character" }
        )
    cover_image: AttachmentInput
    logo: AttachmentInput
    address: UserAddressInput
    settings: String
    categories: [ID]
    balance: BalanceInput
    settings: ShopSettingsInput
}
input TransferShopOwnershipInput {
    shop_id: ID
    vendor_id: ID
    message: String
}

input ShopMaintenanceInput {
    start: String
    title: String
    until: String
    description: String
    image: AttachmentInput
}


input ShopSettingsInput {
    socials: [ShopSocialInput]
    contact: String
    location: LocationInput
    website: String
    notifications: NotificationsInput
    isShopUnderMaintenance: Boolean
    shopMaintenance: ShopMaintenanceInput
}

input NotificationsInput {
    email: String
    enable: Boolean
}

input LocationInput {
    lat: Float
    lng: Float
    city: String
    state: String
    country: String
    zip: String
    street_address: String
    formattedAddress: String
}

input ShopSocialInput {
    icon: String
    url: String
}

input ApproveShopInput {
    id: ID!
    admin_commission_rate: Float!
}

input AddStaffInput {
    email: String!
        @rules(
            apply: ["unique:users,email"]
            messages: { unique: "Sorry! this email has been already taken." }
        )
    password: String!
    name: String!
    shop_id: ID! @rules(apply: ["exists:shops,id"])
}

input FollowShopInput {
    shop_id: ID! @rules(apply: ["required", "numeric"])
}

input CreateShopMaintenanceEventInput {
    shop_id: ID! @rules(apply: ["exists:shops,id"])
    isShopUnderMaintenance: Boolean
    isMaintenance: Boolean
}

extend type Mutation {
    approveShop(input: ApproveShopInput! @spread): Shop
        @field(resolver: "ShopMutator@approveShop")
        @can(ability: "super_admin")
    disapproveShop(id: ID!): Shop
        @field(resolver: "ShopMutator@disApproveShop")
        @can(ability: "super_admin")
    addStaff(input: AddStaffInput! @spread): Boolean
        @field(resolver: "ShopMutator@addStaff")
    removeStaff(id: ID!): User @field(resolver: "ShopMutator@removeStaff")
    deleteShop(id: ID!): Shop
        @field(resolver: "ShopMutator@deleteShop")
        @can(ability: "store_owner")
    createShop(input: CreateShopInput! @spread): Shop
        @field(resolver: "ShopMutator@createShop")
        @can(ability: "store_owner")
    updateShop(input: UpdateShopInput! @spread): Shop
        @field(resolver: "ShopMutator@updateShop")
        @can(ability: "store_owner")
    transferShopOwnership(input: TransferShopOwnershipInput! @spread): Shop
        @field(resolver: "ShopMutator@transferShopOwnership")

    followShop(input: FollowShopInput! @spread): FollowedShop
        @field(resolver: "ShopMutator@followShop")

    createShopMaintenanceEvent(input: CreateShopMaintenanceEventInput! @spread): Shop
        @field(resolver: "ShopMutator@shopMaintenanceEvent")
}
