<?php

namespace Marvel\Listeners;

use Marvel\Events\DeliveryAssigned;
use Marvel\Notifications\DeliveryAssignedNotification;
use Marvel\Database\Models\User;
use Marvel\Database\Repositories\NotifyLogsRepository as NotifyLogRepository;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendDeliveryAssignedNotification implements ShouldQueue
{
    /**
     * The notify log repository instance.
     *
     * @var \Marvel\Database\Repositories\NotifyLogRepository
     */
    protected $notifyLogRepository;

    /**
     * Create the event listener.
     *
     * @param  \Marvel\Database\Repositories\NotifyLogRepository  $notifyLogRepository
     * @return void
     */
    public function __construct(NotifyLogRepository $notifyLogRepository)
    {
        $this->notifyLogRepository = $notifyLogRepository;
    }

    /**
     * Handle the event.
     *
     * @param  \Marvel\Events\DeliveryAssigned  $event
     * @return void
     */
    public function handle(DeliveryAssigned $event)
    {
        // Get the delivery agent
        $agent = User::find($event->delivery->delivery_agent_user_id);

        if (!$agent) {
            return;
        }

        // Send notification to the delivery agent
        $agent->notify(new DeliveryAssignedNotification(
            $event->delivery,
            $event->isAutomatic,
            $event->assignedBy
        ));

        // Prepare notification data
        $message = 'You have been assigned a new delivery for order #' . $event->delivery->order->tracking_number;
        if ($event->isAutomatic) {
            $message .= ' (automatically assigned)';
        } elseif ($event->assignedBy) {
            $message .= ' by ' . $event->assignedBy->name;
        }

        // Log the notification
        $this->notifyLogRepository->storeNotification([
            'user_id' => $agent->id,
            'notify_type' => 'delivery_assigned',
            'notify_id' => $event->delivery->id,
            'sender' => $event->assignedBy ? $event->assignedBy->id : null,
            'receiver' => $agent->id,
            'options' => [
                'delivery_id' => $event->delivery->id,
                'order_id' => $event->delivery->order_id,
                'is_automatic' => $event->isAutomatic,
                'message' => $message,
            ],
        ]);
    }
}
