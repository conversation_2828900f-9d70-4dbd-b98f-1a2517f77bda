<?php

namespace Marvel\Listeners;

use Marvel\Events\DeliveryCancelled;
use Marvel\Notifications\DeliveryCancelledNotification;
use Marvel\Database\Repositories\NotifyLogsRepository as NotifyLogRepository;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendDeliveryCancelledNotification implements ShouldQueue
{
    /**
     * The notify log repository instance.
     *
     * @var \Marvel\Database\Repositories\NotifyLogRepository
     */
    protected $notifyLogRepository;

    /**
     * Create the event listener.
     *
     * @param  \Marvel\Database\Repositories\NotifyLogRepository  $notifyLogRepository
     * @return void
     */
    public function __construct(NotifyLogRepository $notifyLogRepository)
    {
        $this->notifyLogRepository = $notifyLogRepository;
    }

    /**
     * Handle the event.
     *
     * @param  \Marvel\Events\DeliveryCancelled  $event
     * @return void
     */
    public function handle(DeliveryCancelled $event)
    {
        // Notify both the delivery agent and the customer
        $this->notifyDeliveryAgent($event);
        $this->notifyCustomer($event);
    }
    
    /**
     * Notify the delivery agent about the cancellation.
     *
     * @param  \Marvel\Events\DeliveryCancelled  $event
     * @return void
     */
    protected function notifyDeliveryAgent($event)
    {
        $agent = $event->delivery->deliveryAgent;
        
        if (!$agent) {
            return;
        }
        
        // Send notification to the delivery agent
        $agent->notify(new DeliveryCancelledNotification($event->delivery, $event->reason));
        
        // Log the notification
        $this->notifyLogRepository->storeNotification([
            'user_id' => $agent->id,
            'notify_type' => 'delivery_cancelled',
            'notify_id' => $event->delivery->id,
            'sender' => $event->cancelledBy->id,
            'receiver' => $agent->id,
            'options' => [
                'delivery_id' => $event->delivery->id,
                'order_id' => $event->delivery->order_id,
                'reason' => $event->reason,
                'message' => 'Delivery for order #' . $event->delivery->order->tracking_number . ' has been cancelled' . 
                             ($event->reason ? ': ' . $event->reason : '.'),
            ],
        ]);
    }
    
    /**
     * Notify the customer about the cancellation.
     *
     * @param  \Marvel\Events\DeliveryCancelled  $event
     * @return void
     */
    protected function notifyCustomer($event)
    {
        $customer = $event->delivery->order->customer;
        
        if (!$customer) {
            return;
        }
        
        // Send notification to the customer
        $customer->notify(new DeliveryCancelledNotification($event->delivery, $event->reason));
        
        // Log the notification
        $this->notifyLogRepository->storeNotification([
            'user_id' => $customer->id,
            'notify_type' => 'delivery_cancelled',
            'notify_id' => $event->delivery->id,
            'sender' => $event->cancelledBy->id,
            'receiver' => $customer->id,
            'options' => [
                'delivery_id' => $event->delivery->id,
                'order_id' => $event->delivery->order_id,
                'reason' => $event->reason,
                'message' => 'Delivery for order #' . $event->delivery->order->tracking_number . ' has been cancelled' . 
                             ($event->reason ? ': ' . $event->reason : '.'),
            ],
        ]);
    }
}
