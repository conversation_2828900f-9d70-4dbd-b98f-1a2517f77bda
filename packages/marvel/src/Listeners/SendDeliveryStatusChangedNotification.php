<?php

namespace Marvel\Listeners;

use Marvel\Events\DeliveryStatusChanged;
use Marvel\Notifications\DeliveryStatusChangedNotification;
use Marvel\Database\Repositories\NotifyLogsRepository as NotifyLogRepository;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendDeliveryStatusChangedNotification implements ShouldQueue
{
    /**
     * The notify log repository instance.
     *
     * @var \Marvel\Database\Repositories\NotifyLogRepository
     */
    protected $notifyLogRepository;

    /**
     * Create the event listener.
     *
     * @param  \Marvel\Database\Repositories\NotifyLogRepository  $notifyLogRepository
     * @return void
     */
    public function __construct(NotifyLogRepository $notifyLogRepository)
    {
        $this->notifyLogRepository = $notifyLogRepository;
    }

    /**
     * Handle the event.
     *
     * @param  \Marvel\Events\DeliveryStatusChanged  $event
     * @return void
     */
    public function handle(DeliveryStatusChanged $event)
    {
        // Get the order customer
        $customer = $event->delivery->order->customer;
        
        if (!$customer) {
            return;
        }
        
        // Only send notifications for important status changes
        $notifiableStatuses = ['ACCEPTED', 'PICKED_UP', 'IN_TRANSIT', 'DELIVERED', 'FAILED'];
        
        if (!in_array($event->delivery->status, $notifiableStatuses)) {
            return;
        }
        
        // Send notification to the customer
        $customer->notify(new DeliveryStatusChangedNotification(
            $event->delivery,
            $event->previousStatus
        ));
        
        // Get status message
        $statusMessage = $this->getStatusMessage($event->delivery);
        
        // Log the notification
        $this->notifyLogRepository->storeNotification([
            'user_id' => $customer->id,
            'notify_type' => 'delivery_status_changed',
            'notify_id' => $event->delivery->id,
            'sender' => $event->updatedBy->id,
            'receiver' => $customer->id,
            'options' => [
                'delivery_id' => $event->delivery->id,
                'order_id' => $event->delivery->order_id,
                'previous_status' => $event->previousStatus,
                'current_status' => $event->delivery->status,
                'message' => $statusMessage,
            ],
        ]);
        
        // Also notify shop owner if the delivery is completed or failed
        if (in_array($event->delivery->status, ['DELIVERED', 'FAILED'])) {
            $shopOwner = $event->delivery->order->shop->owner;
            
            if ($shopOwner && $shopOwner->id !== $customer->id) {
                $shopOwner->notify(new DeliveryStatusChangedNotification(
                    $event->delivery,
                    $event->previousStatus
                ));
                
                // Log the notification for shop owner
                $this->notifyLogRepository->storeNotification([
                    'user_id' => $shopOwner->id,
                    'notify_type' => 'delivery_status_changed',
                    'notify_id' => $event->delivery->id,
                    'sender' => $event->updatedBy->id,
                    'receiver' => $shopOwner->id,
                    'options' => [
                        'delivery_id' => $event->delivery->id,
                        'order_id' => $event->delivery->order_id,
                        'previous_status' => $event->previousStatus,
                        'current_status' => $event->delivery->status,
                        'message' => $statusMessage,
                    ],
                ]);
            }
        }
    }
    
    /**
     * Get the appropriate status message based on the current status.
     *
     * @param  \Marvel\Database\Models\Delivery  $delivery
     * @return string
     */
    protected function getStatusMessage($delivery)
    {
        $order = $delivery->order;
        $trackingNumber = $order->tracking_number;
        
        switch ($delivery->status) {
            case 'ACCEPTED':
                return "Your delivery for order #{$trackingNumber} has been accepted by the delivery agent.";
            case 'PICKED_UP':
                return "Your order #{$trackingNumber} has been picked up by the delivery agent.";
            case 'IN_TRANSIT':
                return "Your order #{$trackingNumber} is now in transit.";
            case 'DELIVERED':
                return "Your order #{$trackingNumber} has been successfully delivered.";
            case 'FAILED':
                return "Delivery for order #{$trackingNumber} has failed. Our team will contact you shortly.";
            default:
                return "The status of your delivery for order #{$trackingNumber} has been updated to {$delivery->status}.";
        }
    }
}
