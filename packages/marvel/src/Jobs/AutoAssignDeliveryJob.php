<?php

namespace Marvel\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Marvel\Database\Models\Order;
use Marvel\Database\Repositories\DeliveryRepository;
use Marvel\Exceptions\DeliveryException;
use Marvel\Exceptions\MarvelException;

class AutoAssignDeliveryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The order to assign delivery for.
     *
     * @var Order
     */
    public $order;

    /**
     * The trigger that initiated this job.
     *
     * @var string
     */
    public $trigger;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * Create a new job instance.
     *
     * @param  Order  $order
     * @param  string  $trigger
     * @return void
     */
    public function __construct(Order $order, string $trigger = 'unknown')
    {
        $this->order = $order;
        $this->trigger = $trigger;
        $this->tries = config('delivery.auto_assignment_retry_attempts', 3);

        // Set queue name from config
        $this->onQueue(config('delivery.auto_assignment_queue', 'default'));
    }

    /**
     * Execute the job.
     *
     * @param  DeliveryRepository  $deliveryRepository
     * @return void
     */
    public function handle(DeliveryRepository $deliveryRepository)
    {
        try {
            Log::info('Starting auto assignment job', [
                'order_id' => $this->order->id,
                'tracking_number' => $this->order->tracking_number,
                'trigger' => $this->trigger,
                'attempt' => $this->attempts(),
            ]);

            // Refresh the order to get the latest state
            $this->order->refresh();

            // Double-check if assignment is still needed
            if (!$this->shouldProceedWithAssignment()) {
                Log::info('Auto assignment skipped - conditions no longer met', [
                    'order_id' => $this->order->id,
                    'tracking_number' => $this->order->tracking_number,
                    'delivery_id' => $this->order->delivery_id,
                    'requires_delivery' => $this->order->requires_delivery,
                ]);
                return;
            }

            // Attempt automatic assignment
            $delivery = $deliveryRepository->autoAssignDelivery($this->order, [
                'trigger' => $this->trigger,
                'attempt' => $this->attempts(),
            ]);

            if ($delivery) {
                Log::info('Auto assignment successful', [
                    'order_id' => $this->order->id,
                    'tracking_number' => $this->order->tracking_number,
                    'delivery_id' => $delivery->id,
                    'agent_id' => $delivery->delivery_agent_user_id,
                    'assignment_type' => $delivery->assignment_type,
                    'trigger' => $this->trigger,
                ]);
            } else {
                Log::warning('Auto assignment returned null delivery', [
                    'order_id' => $this->order->id,
                    'tracking_number' => $this->order->tracking_number,
                    'trigger' => $this->trigger,
                ]);
            }
        } catch (DeliveryException $e) {
            $this->handleAssignmentFailure($e, 'DeliveryException');
        } catch (MarvelException $e) {
            $this->handleAssignmentFailure($e, 'MarvelException');
        } catch (\Exception $e) {
            $this->handleAssignmentFailure($e, 'GeneralException');
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('Auto assignment job failed permanently', [
            'order_id' => $this->order->id,
            'tracking_number' => $this->order->tracking_number,
            'trigger' => $this->trigger,
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);

        // Optionally, create a pending delivery record for manual assignment
        try {
            $deliveryRepository = app(DeliveryRepository::class);
            $deliveryRepository->createPendingDelivery($this->order, [
                'reason' => 'Auto assignment failed after ' . $this->tries . ' attempts',
                'last_error' => $exception->getMessage(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create pending delivery after auto assignment failure', [
                'order_id' => $this->order->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Check if we should proceed with the assignment.
     *
     * @return bool
     */
    private function shouldProceedWithAssignment(): bool
    {
        // Order must still require delivery
        if (!$this->order->requires_delivery) {
            return false;
        }

        // Order must not already have a delivery assigned
        if ($this->order->delivery_id) {
            return false;
        }

        // Auto assignment must still be enabled
        if (!config('delivery.auto_assignment_enabled', true)) {
            return false;
        }

        return true;
    }

    /**
     * Handle assignment failure and determine if we should retry.
     *
     * @param  \Exception  $exception
     * @param  string  $exceptionType
     * @return void
     */
    private function handleAssignmentFailure(\Exception $exception, string $exceptionType): void
    {
        Log::warning('Auto assignment attempt failed', [
            'order_id' => $this->order->id,
            'tracking_number' => $this->order->tracking_number,
            'trigger' => $this->trigger,
            'attempt' => $this->attempts(),
            'exception_type' => $exceptionType,
            'error' => $exception->getMessage(),
        ]);

        // Determine if we should retry based on the exception type
        $shouldRetry = $this->shouldRetryForException($exception, $exceptionType);

        if ($shouldRetry && $this->attempts() < $this->tries) {
            // Calculate retry delay (exponential backoff)
            $baseDelay = config('delivery.auto_assignment_retry_delay', 5); // minutes
            $delay = $baseDelay * pow(2, $this->attempts() - 1); // Exponential backoff

            Log::info('Scheduling auto assignment retry', [
                'order_id' => $this->order->id,
                'tracking_number' => $this->order->tracking_number,
                'next_attempt' => $this->attempts() + 1,
                'delay_minutes' => $delay,
            ]);

            $this->release(now()->addMinutes($delay));
        } else {
            // Don't retry, let the job fail
            throw $exception;
        }
    }

    /**
     * Determine if we should retry based on the exception type.
     *
     * @param  \Exception  $exception
     * @param  string  $exceptionType
     * @return bool
     */
    private function shouldRetryForException(\Exception $exception, string $exceptionType): bool
    {
        // Don't retry for certain types of errors
        $nonRetryableMessages = [
            'Shop location data is missing',
            'Invalid shop coordinates',
            'Order does not have an associated shop',
        ];

        foreach ($nonRetryableMessages as $message) {
            if (str_contains($exception->getMessage(), $message)) {
                return false;
            }
        }

        // Retry for most other exceptions (no agents available, network issues, etc.)
        return true;
    }
}
