<?php

namespace Marvel\Ai;

use Exception;
use <PERSON>\Ai\AiInterface;
use Marvel\Ai\Base;
use OpenAI as OpenAIClient;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Support\Facades\Http;

class Openai extends Base implements AiInterface
{
    private $openAiClient;

    public function __construct()
    {
        $this->openAiClient = OpenAIClient::client(config('shop.openai.secret_Key'));
        parent::__construct();
    }
    /**
     * createCustomer
     *
     * @param  mixed  $request
     * @return array
     */
    // public function generateDescription($request): mixed
    // {
    //     try {
    //         $response = $this->openAiClient->chat()->create([
    //             'model' => 'gpt-4o',
    //             'messages' => [
    //                 [
    //                     'role' => 'user', 'content' => $request->prompt
    //                 ],
    //             ],
    //         ]);

    //         foreach ($response->choices as $result) {
    //             $result->index; // 0
    //             $result->message->role; // 'assistant'
    //             $result->message->content; // '\n\nHello there! How can I assist you today?'
    //             $result->finishReason; // 'stop'
    //         }

    //         return ['status' => 'success', 'result' => $result->message->content];
    //     } catch (Exception $e) {
    //         throw new HttpException(400, $e->getMessage());
    //     }
    // }


public function generateDescription($request): mixed
{
    try {
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])->post(
            'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=' . config('shop.openai.secret_Key'),
            [
                'contents' => [
                    [
                        'parts' => [
                            ['text' => $request->prompt . 'Answer to the question directly, i need you to respond with only the answer (title, description, more info, etc) Not more than 150 words.'],
                        ],
                    ],
                ],
            ]
        );

        $data = $response->json();

        if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            $content = $data['candidates'][0]['content']['parts'][0]['text'];
            return ['status' => 'success', 'result' => $content];
        }

        throw new \Exception('No response content returned from Gemini API');
    } catch (\Exception $e) {
        throw new HttpException(400, 'Something went wrong: ' . $e->getMessage());
    }
}
}
