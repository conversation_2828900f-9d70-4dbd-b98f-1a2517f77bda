<?php

namespace Marvel\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Marvel\Database\Models\Delivery;

class DeliveryStatusChangedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The delivery instance.
     *
     * @var \Marvel\Database\Models\Delivery
     */
    protected $delivery;

    /**
     * The previous status.
     *
     * @var string
     */
    protected $previousStatus;

    /**
     * Create a new notification instance.
     *
     * @param  \Marvel\Database\Models\Delivery  $delivery
     * @param  string  $previousStatus
     * @return void
     */
    public function __construct(Delivery $delivery, string $previousStatus)
    {
        $this->delivery = $delivery;
        $this->previousStatus = $previousStatus;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        $channels = ['mail', 'database'];

        // Add FCM channel if it's configured
        if (config('services.fcm.server_key')) {
            $channels[] = \Marvel\Notifications\Channels\FcmChannel::class;
        }

        return $channels;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $order = $this->delivery->order;
        $url = config('shop.dashboard_url') . '/orders/' . $order->id;
        $statusMessage = $this->getStatusMessage();

        return (new MailMessage)
            ->subject('Delivery Status Update: ' . $this->delivery->status)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line($statusMessage)
            ->line('Order #: ' . $order->tracking_number)
            ->action('View Order Details', $url)
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $order = $this->delivery->order;

        return [
            'delivery_id' => $this->delivery->id,
            'order_id' => $order->id,
            'order_tracking' => $order->tracking_number,
            'type' => 'delivery_status_changed',
            'previous_status' => $this->previousStatus,
            'current_status' => $this->delivery->status,
            'message' => $this->getStatusMessage(),
        ];
    }

    /**
     * Get the FCM representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toFcm($notifiable)
    {
        $order = $this->delivery->order;
        $message = $this->getStatusMessage();
        $title = 'Delivery Update: ' . $this->delivery->status;

        return [
            'title' => $title,
            'body' => $message,
            'data' => [
                'delivery_id' => $this->delivery->id,
                'order_id' => $order->id,
                'order_tracking' => $order->tracking_number,
                'type' => 'delivery_status_changed',
                'previous_status' => $this->previousStatus,
                'current_status' => $this->delivery->status,
                'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                'sound' => 'default',
            ],
        ];
    }

    /**
     * Get the appropriate status message based on the current status.
     *
     * @return string
     */
    protected function getStatusMessage()
    {
        $order = $this->delivery->order;
        $trackingNumber = $order->tracking_number;

        switch ($this->delivery->status) {
            case 'ACCEPTED':
                return "Your delivery for order #{$trackingNumber} has been accepted by the delivery agent.";
            case 'PICKED_UP':
                return "Your order #{$trackingNumber} has been picked up by the delivery agent.";
            case 'IN_TRANSIT':
                return "Your order #{$trackingNumber} is now in transit.";
            case 'DELIVERED':
                return "Your order #{$trackingNumber} has been successfully delivered.";
            case 'FAILED':
                return "Delivery for order #{$trackingNumber} has failed. Our team will contact you shortly.";
            default:
                return "The status of your delivery for order #{$trackingNumber} has been updated to {$this->delivery->status}.";
        }
    }
}
