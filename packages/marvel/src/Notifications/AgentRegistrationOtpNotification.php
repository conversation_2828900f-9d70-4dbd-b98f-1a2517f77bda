<?php

namespace Marvel\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AgentRegistrationOtpNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The OTP code.
     *
     * @var string
     */
    protected $otp;

    /**
     * The registration data.
     *
     * @var array
     */
    protected $registrationData;

    /**
     * Create a new notification instance.
     *
     * @param string $otp
     * @param array $registrationData
     * @return void
     */
    public function __construct(string $otp, array $registrationData)
    {
        $this->otp = $otp;
        $this->registrationData = $registrationData;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Delivery Agent Registration OTP Verification')
            ->greeting('Hello ' . $this->registrationData['name'] . '!')
            ->line('Thank you for registering as a delivery agent. Please use the following OTP code to verify your email address and complete your registration.')
            ->line('Your OTP code is: ' . $this->otp)
            ->line('This code will expire in 10 minutes.')
            ->line('If you did not request this registration, no further action is required.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'otp' => $this->otp,
            'email' => $this->registrationData['email'],
        ];
    }
}
