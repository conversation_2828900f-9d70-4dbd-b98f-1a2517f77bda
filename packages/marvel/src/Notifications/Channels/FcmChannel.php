<?php

namespace Marvel\Notifications\Channels;

use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Marvel\Database\Models\DeviceToken;

class FcmChannel
{
    /**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return void
     */
    public function send($notifiable, Notification $notification)
    {
        if (!method_exists($notification, 'toFcm')) {
            return;
        }

        $message = $notification->toFcm($notifiable);

        if (empty($message)) {
            return;
        }

        // Get device tokens for the user
        $tokens = DeviceToken::where('user_id', $notifiable->id)
            ->where('is_active', true)
            ->pluck('token')
            ->toArray();

        if (empty($tokens)) {
            return;
        }

        $this->sendNotification($tokens, $message);
    }

    /**
     * Send notification to FCM
     *
     * @param array $tokens
     * @param array $message
     * @return void
     */
    protected function sendNotification(array $tokens, array $message)
    {
        $fcmServerKey = config('services.fcm.server_key');

        if (empty($fcmServerKey)) {
            Log::warning('FCM server key is not configured.');
            return;
        }

        $data = [
            'registration_ids' => $tokens,
            'notification' => [
                'title' => $message['title'] ?? '',
                'body' => $message['body'] ?? '',
                'sound' => 'default',
                'badge' => '1',
            ],
            'data' => $message['data'] ?? [],
            'priority' => 'high',
        ];

        try {
            $response = Http::withHeaders([
                'Authorization' => 'key=' . $fcmServerKey,
                'Content-Type' => 'application/json',
            ])->post('https://fcm.googleapis.com/fcm/send', $data);

            if (!$response->successful()) {
                Log::error('FCM notification failed: ' . $response->body());
            }
        } catch (\Exception $e) {
            Log::error('FCM notification exception: ' . $e->getMessage());
        }
    }
}
