<?php

namespace Marvel\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Marvel\Database\Models\Delivery;

class DeliveryCancelledNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The delivery instance.
     *
     * @var \Marvel\Database\Models\Delivery
     */
    protected $delivery;

    /**
     * The reason for cancellation.
     *
     * @var string|null
     */
    protected $reason;

    /**
     * Create a new notification instance.
     *
     * @param  \Marvel\Database\Models\Delivery  $delivery
     * @param  string|null  $reason
     * @return void
     */
    public function __construct(Delivery $delivery, ?string $reason = null)
    {
        $this->delivery = $delivery;
        $this->reason = $reason;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        $channels = ['mail', 'database'];

        // Add FCM channel if it's configured
        if (config('services.fcm.server_key')) {
            $channels[] = \Marvel\Notifications\Channels\FcmChannel::class;
        }

        return $channels;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $order = $this->delivery->order;
        $url = config('shop.dashboard_url') . '/orders/' . $order->id;

        $mail = (new MailMessage)
            ->subject('Delivery Cancelled')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('The delivery for your order #' . $order->tracking_number . ' has been cancelled.')
            ->action('View Order Details', $url);

        if ($this->reason) {
            $mail->line('Reason: ' . $this->reason);
        }

        return $mail->line('Our team will be in touch with you regarding next steps.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $order = $this->delivery->order;

        return [
            'delivery_id' => $this->delivery->id,
            'order_id' => $order->id,
            'order_tracking' => $order->tracking_number,
            'type' => 'delivery_cancelled',
            'reason' => $this->reason,
            'message' => 'Delivery for order #' . $order->tracking_number . ' has been cancelled' .
                         ($this->reason ? ': ' . $this->reason : '.'),
        ];
    }

    /**
     * Get the FCM representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toFcm($notifiable)
    {
        $order = $this->delivery->order;
        $message = 'Delivery for order #' . $order->tracking_number . ' has been cancelled' .
                   ($this->reason ? ': ' . $this->reason : '.');

        return [
            'title' => 'Delivery Cancelled',
            'body' => $message,
            'data' => [
                'delivery_id' => $this->delivery->id,
                'order_id' => $order->id,
                'order_tracking' => $order->tracking_number,
                'type' => 'delivery_cancelled',
                'reason' => $this->reason,
                'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                'sound' => 'default',
            ],
        ];
    }
}
