<?php

namespace Marvel\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\User;

class DeliveryAssignedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The delivery instance.
     *
     * @var \Marvel\Database\Models\Delivery
     */
    protected $delivery;

    /**
     * Whether this was an automatic assignment.
     *
     * @var bool
     */
    protected $isAutomatic;

    /**
     * The user who assigned the delivery, or null if automatic.
     *
     * @var \Marvel\Database\Models\User|null
     */
    protected $assignedBy;

    /**
     * Create a new notification instance.
     *
     * @param  \Marvel\Database\Models\Delivery  $delivery
     * @param  bool  $isAutomatic
     * @param  \Marvel\Database\Models\User|null  $assignedBy
     * @return void
     */
    public function __construct(Delivery $delivery, bool $isAutomatic = false, ?User $assignedBy = null)
    {
        $this->delivery = $delivery;
        $this->isAutomatic = $isAutomatic;
        $this->assignedBy = $assignedBy;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        $channels = ['mail', 'database'];

        // Add FCM channel if it's configured
        if (config('services.fcm.server_key')) {
            $channels[] = \Marvel\Notifications\Channels\FcmChannel::class;
        }

        return $channels;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $order = $this->delivery->order;
        $url = config('shop.dashboard_url') . '/deliveries/' . $this->delivery->id;

        $message = 'You have been assigned a new delivery.';

        if ($this->isAutomatic) {
            $message .= ' This delivery was automatically assigned to you based on your location and availability.';
        } elseif ($this->assignedBy) {
            $message .= ' This delivery was assigned to you by ' . $this->assignedBy->name . '.';
        }

        return (new MailMessage)
            ->subject('New Delivery Assignment')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line($message)
            ->line('Order #: ' . $order->tracking_number)
            ->line('Pickup Location: ' . $this->getPickupAddress())
            ->line('Delivery Location: ' . $this->getDeliveryAddress())
            ->action('View Delivery Details', $url)
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $order = $this->delivery->order;
        $message = 'You have been assigned a new delivery for order #' . $order->tracking_number;

        if ($this->isAutomatic) {
            $message .= ' (automatically assigned)';
        } elseif ($this->assignedBy) {
            $message .= ' by ' . $this->assignedBy->name;
        }

        return [
            'delivery_id' => $this->delivery->id,
            'order_id' => $order->id,
            'order_tracking' => $order->tracking_number,
            'type' => 'delivery_assigned',
            'is_automatic' => $this->isAutomatic,
            'assigned_by' => $this->assignedBy ? [
                'id' => $this->assignedBy->id,
                'name' => $this->assignedBy->name,
            ] : null,
            'message' => $message,
        ];
    }

    /**
     * Get the FCM representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toFcm($notifiable)
    {
        $order = $this->delivery->order;
        $message = 'You have been assigned a new delivery for order #' . $order->tracking_number;

        if ($this->isAutomatic) {
            $message .= ' (automatically assigned)';
        } elseif ($this->assignedBy) {
            $message .= ' by ' . $this->assignedBy->name;
        }

        return [
            'title' => 'New Delivery Assignment',
            'body' => $message,
            'data' => [
                'delivery_id' => $this->delivery->id,
                'order_id' => $order->id,
                'order_tracking' => $order->tracking_number,
                'type' => 'delivery_assigned',
                'is_automatic' => $this->isAutomatic,
                'assigned_by' => $this->assignedBy ? [
                    'id' => $this->assignedBy->id,
                    'name' => $this->assignedBy->name,
                ] : null,
                'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                'sound' => 'default',
            ],
        ];
    }

    /**
     * Get the pickup address formatted as a string.
     *
     * @return string
     */
    protected function getPickupAddress()
    {
        $shop = $this->delivery->order->shop;
        $address = $shop->address ?? [];

        if (empty($address)) {
            return 'Not specified';
        }

        return implode(', ', array_filter([
            $address['street_address'] ?? '',
            $address['city'] ?? '',
            $address['state'] ?? '',
            $address['zip'] ?? '',
            $address['country'] ?? '',
        ]));
    }

    /**
     * Get the delivery address formatted as a string.
     *
     * @return string
     */
    protected function getDeliveryAddress()
    {
        $address = $this->delivery->order->shipping_address ?? [];

        if (empty($address)) {
            return 'Not specified';
        }

        return implode(', ', array_filter([
            $address['street_address'] ?? '',
            $address['city'] ?? '',
            $address['state'] ?? '',
            $address['zip'] ?? '',
            $address['country'] ?? '',
        ]));
    }
}
