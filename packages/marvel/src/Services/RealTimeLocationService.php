<?php

namespace Marvel\Services;

use Illuminate\Support\Facades\Broadcast;
use Marvel\Database\Models\User;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\Order;
use Marvel\Events\AgentLocationUpdated;
use Marvel\Events\DeliveryStatusChanged;
use Marvel\Services\LocationDataSanitizer;
use Marvel\Services\AgentLocationCacheService;

class RealTimeLocationService
{
    private LocationDataSanitizer $sanitizer;
    private AgentLocationCacheService $cacheService;
    private DynamicETAService $etaService;

    public function __construct(
        LocationDataSanitizer $sanitizer,
        AgentLocationCacheService $cacheService,
        DynamicETAService $etaService
    ) {
        $this->sanitizer = $sanitizer;
        $this->cacheService = $cacheService;
        $this->etaService = $etaService;
    }

    /**
     * Broadcast agent location update to relevant channels
     *
     * @param int $agentId
     * @param array $location
     * @param string $status
     * @return void
     */
    public function broadcastAgentLocationUpdate(int $agentId, array $location, string $status): void
    {
        $agent = User::with('delivery_agent_profile')->find($agentId);
        if (!$agent) {
            return;
        }

        // Get active deliveries for this agent
        $activeDeliveries = Delivery::where('delivery_agent_user_id', $agentId)
            ->whereIn('status', ['ASSIGNED', 'ACCEPTED', 'PICKED_UP', 'IN_TRANSIT'])
            ->with(['order.customer'])
            ->get();

        foreach ($activeDeliveries as $delivery) {
            $this->broadcastLocationToCustomer($delivery, $agent, $location);
            $this->broadcastLocationToAdmin($delivery, $agent, $location);
            $this->updateDeliveryETA($delivery, $location);
        }

        // Broadcast to admin dashboard for all agents
        $this->broadcastLocationToAdminDashboard($agent, $location, $status);
    }

    /**
     * Broadcast location update to customer
     *
     * @param Delivery $delivery
     * @param User $agent
     * @param array $location
     * @return void
     */
    private function broadcastLocationToCustomer(Delivery $delivery, User $agent, array $location): void
    {
        $customer = $delivery->order->customer;
        if (!$customer) {
            return;
        }

        // Sanitize location data for customer view
        $sanitizedAgent = $this->sanitizer->sanitizeAgentData(
            $agent->toArray(),
            $customer,
            'tracking'
        );

        $data = [
            'delivery_id' => $delivery->id,
            'agent' => $sanitizedAgent,
            'estimated_arrival' => $this->etaService->calculateETA($delivery, $location),
            'timestamp' => now()->toISOString(),
        ];

        // Broadcast to customer's private channel
        Broadcast::channel("customer.{$customer->id}.delivery.{$delivery->id}")
            ->send(new AgentLocationUpdated($data));
    }

    /**
     * Broadcast location update to admin
     *
     * @param Delivery $delivery
     * @param User $agent
     * @param array $location
     * @return void
     */
    private function broadcastLocationToAdmin(Delivery $delivery, User $agent, array $location): void
    {
        // Get shop owner for this delivery
        $shop = $delivery->order->shop;
        if (!$shop || !$shop->owner) {
            return;
        }

        // Admin gets precise location data
        $agentData = $agent->toArray();
        $agentData['delivery_agent_profile']['current_location'] = $location;

        $data = [
            'delivery_id' => $delivery->id,
            'agent' => $agentData,
            'estimated_arrival' => $this->etaService->calculateETA($delivery, $location),
            'timestamp' => now()->toISOString(),
        ];

        // Broadcast to shop owner's admin channel
        Broadcast::channel("admin.{$shop->owner->id}.deliveries")
            ->send(new AgentLocationUpdated($data));
    }

    /**
     * Broadcast location to admin dashboard
     *
     * @param User $agent
     * @param array $location
     * @param string $status
     * @return void
     */
    private function broadcastLocationToAdminDashboard(User $agent, array $location, string $status): void
    {
        $data = [
            'agent_id' => $agent->id,
            'agent_name' => $agent->name,
            'location' => $location,
            'status' => $status,
            'active_deliveries_count' => $agent->deliveries()
                ->whereIn('status', ['ASSIGNED', 'ACCEPTED', 'PICKED_UP', 'IN_TRANSIT'])
                ->count(),
            'timestamp' => now()->toISOString(),
        ];

        // Broadcast to admin dashboard channel
        Broadcast::channel('admin.dashboard.agents')
            ->send(new AgentLocationUpdated($data));
    }

    /**
     * Update delivery ETA based on current location
     *
     * @param Delivery $delivery
     * @param array $agentLocation
     * @return void
     */
    private function updateDeliveryETA(Delivery $delivery, array $agentLocation): void
    {
        $newETA = $this->etaService->calculateETA($delivery, $agentLocation);
        
        // Only update if ETA changed significantly (more than 5 minutes)
        if ($delivery->estimated_delivery_time) {
            $timeDiff = abs($newETA->diffInMinutes($delivery->estimated_delivery_time));
            if ($timeDiff < 5) {
                return;
            }
        }

        $delivery->update(['estimated_delivery_time' => $newETA]);

        // Broadcast ETA update
        $this->broadcastETAUpdate($delivery, $newETA);
    }

    /**
     * Broadcast ETA update to relevant parties
     *
     * @param Delivery $delivery
     * @param \Carbon\Carbon $newETA
     * @return void
     */
    private function broadcastETAUpdate(Delivery $delivery, $newETA): void
    {
        $customer = $delivery->order->customer;
        if ($customer) {
            $data = [
                'delivery_id' => $delivery->id,
                'estimated_arrival' => $newETA->toISOString(),
                'timestamp' => now()->toISOString(),
            ];

            Broadcast::channel("customer.{$customer->id}.delivery.{$delivery->id}")
                ->send(new DeliveryStatusChanged($delivery, 'ETA_UPDATED', $data));
        }
    }

    /**
     * Start live tracking for a delivery
     *
     * @param int $deliveryId
     * @param int $customerId
     * @return array
     */
    public function startLiveTracking(int $deliveryId, int $customerId): array
    {
        $delivery = Delivery::with(['order.customer', 'deliveryAgent.delivery_agent_profile'])
            ->find($deliveryId);

        if (!$delivery || $delivery->order->customer_id !== $customerId) {
            throw new \Exception('Delivery not found or access denied');
        }

        $agent = $delivery->deliveryAgent;
        if (!$agent || !$agent->delivery_agent_profile) {
            throw new \Exception('Delivery agent not found');
        }

        // Get current agent location from cache
        $cachedLocation = $this->cacheService->getCachedAgentLocation($agent->id);
        $currentLocation = $cachedLocation ?: $agent->delivery_agent_profile->current_location;

        // Sanitize data for customer
        $sanitizedAgent = $this->sanitizer->sanitizeAgentData(
            $agent->toArray(),
            $delivery->order->customer,
            'tracking'
        );

        return [
            'delivery_id' => $delivery->id,
            'tracking_channel' => "customer.{$customerId}.delivery.{$deliveryId}",
            'agent' => $sanitizedAgent,
            'current_location' => $currentLocation,
            'estimated_arrival' => $delivery->estimated_delivery_time,
            'status' => $delivery->status,
            'last_updated' => $cachedLocation['updated_at'] ?? now()->timestamp,
        ];
    }

    /**
     * Stop live tracking for a delivery
     *
     * @param int $deliveryId
     * @param int $customerId
     * @return void
     */
    public function stopLiveTracking(int $deliveryId, int $customerId): void
    {
        // Leave the tracking channel
        Broadcast::channel("customer.{$customerId}.delivery.{$deliveryId}")
            ->leave();
    }

    /**
     * Get real-time delivery status for admin dashboard
     *
     * @param array $filters
     * @return array
     */
    public function getRealtimeDeliveryStatus(array $filters = []): array
    {
        $query = Delivery::with(['order', 'deliveryAgent.delivery_agent_profile'])
            ->whereIn('status', ['ASSIGNED', 'ACCEPTED', 'PICKED_UP', 'IN_TRANSIT']);

        if (isset($filters['shop_id'])) {
            $query->whereHas('order', function ($q) use ($filters) {
                $q->where('shop_id', $filters['shop_id']);
            });
        }

        $deliveries = $query->get();

        $realTimeData = [];
        foreach ($deliveries as $delivery) {
            $agent = $delivery->deliveryAgent;
            if (!$agent) continue;

            // Get cached location for real-time data
            $cachedLocation = $this->cacheService->getCachedAgentLocation($agent->id);
            
            $realTimeData[] = [
                'delivery_id' => $delivery->id,
                'order_id' => $delivery->order_id,
                'tracking_number' => $delivery->order->tracking_number,
                'agent' => [
                    'id' => $agent->id,
                    'name' => $agent->name,
                    'current_location' => $cachedLocation ?: $agent->delivery_agent_profile->current_location,
                    'status' => $cachedLocation['status'] ?? $agent->delivery_agent_profile->availability_status,
                ],
                'status' => $delivery->status,
                'estimated_arrival' => $delivery->estimated_delivery_time,
                'last_location_update' => $cachedLocation['updated_at'] ?? null,
            ];
        }

        return [
            'deliveries' => $realTimeData,
            'total_active' => count($realTimeData),
            'last_updated' => now()->toISOString(),
        ];
    }

    /**
     * Broadcast delivery status change
     *
     * @param Delivery $delivery
     * @param string $previousStatus
     * @return void
     */
    public function broadcastDeliveryStatusChange(Delivery $delivery, string $previousStatus): void
    {
        $customer = $delivery->order->customer;
        if ($customer) {
            $data = [
                'delivery_id' => $delivery->id,
                'status' => $delivery->status,
                'previous_status' => $previousStatus,
                'timestamp' => now()->toISOString(),
                'message' => $this->getStatusChangeMessage($delivery->status),
            ];

            Broadcast::channel("customer.{$customer->id}.delivery.{$delivery->id}")
                ->send(new DeliveryStatusChanged($delivery, $delivery->status, $data));
        }

        // Also broadcast to admin
        $shop = $delivery->order->shop;
        if ($shop && $shop->owner) {
            Broadcast::channel("admin.{$shop->owner->id}.deliveries")
                ->send(new DeliveryStatusChanged($delivery, $delivery->status));
        }
    }

    /**
     * Get user-friendly status change message
     *
     * @param string $status
     * @return string
     */
    private function getStatusChangeMessage(string $status): string
    {
        $messages = [
            'ASSIGNED' => 'Your delivery has been assigned to an agent',
            'ACCEPTED' => 'Your delivery agent has accepted the delivery',
            'PICKED_UP' => 'Your order has been picked up and is on the way',
            'IN_TRANSIT' => 'Your order is in transit',
            'DELIVERED' => 'Your order has been delivered successfully',
            'FAILED' => 'Delivery attempt failed. We will contact you shortly',
        ];

        return $messages[$status] ?? 'Delivery status updated';
    }
}
