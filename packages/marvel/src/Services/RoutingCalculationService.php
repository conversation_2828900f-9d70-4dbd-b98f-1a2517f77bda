<?php

namespace Marvel\Services;

class RoutingCalculationService
{
    /**
     * Calculate the distance between two points using the Haversine formula
     *
     * @param float $lat1 Latitude of first point
     * @param float $lng1 Longitude of first point
     * @param float $lat2 Latitude of second point
     * @param float $lng2 Longitude of second point
     * @return float Distance in kilometers
     */
    public function calculateDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6371; // Earth's radius in kilometers
        
        $lat1 = deg2rad($lat1);
        $lng1 = deg2rad($lng1);
        $lat2 = deg2rad($lat2);
        $lng2 = deg2rad($lng2);
        
        $latDelta = $lat2 - $lat1;
        $lngDelta = $lng2 - $lng1;
        
        $angle = 2 * asin(sqrt(
            pow(sin($latDelta / 2), 2) +
            cos($lat1) * cos($lat2) * pow(sin($lngDelta / 2), 2)
        ));
        
        return $angle * $earthRadius;
    }

    /**
     * Calculate bounding box for efficient pre-filtering
     *
     * @param float $centerLat Center latitude
     * @param float $centerLng Center longitude
     * @param float $radiusKm Radius in kilometers
     * @return array Bounding box coordinates
     */
    public function calculateBoundingBox($centerLat, $centerLng, $radiusKm)
    {
        $latRange = $radiusKm / 111; // Approximate km per degree latitude
        $lngRange = $radiusKm / (111 * cos(deg2rad($centerLat)));
        
        return [
            'minLat' => $centerLat - $latRange,
            'maxLat' => $centerLat + $latRange,
            'minLng' => $centerLng - $lngRange,
            'maxLng' => $centerLng + $lngRange
        ];
    }

    /**
     * Calculate central point for consolidated pickup
     *
     * @param array $locations Array of locations with 'lat' and 'lng' keys
     * @return array|null ['lat' => float, 'lng' => float] or null if no valid locations
     */
    public function calculateCentralPoint(array $locations)
    {
        if (empty($locations)) {
            return null;
        }

        $totalLat = 0;
        $totalLng = 0;
        $count = 0;

        foreach ($locations as $location) {
            if (!isset($location['lat'], $location['lng'])) {
                continue;
            }
            
            $totalLat += (float) $location['lat'];
            $totalLng += (float) $location['lng'];
            $count++;
        }

        if ($count === 0) {
            return null;
        }

        return [
            'lat' => $totalLat / $count,
            'lng' => $totalLng / $count
        ];
    }

    /**
     * Calculate maximum distance between any two points in a set of locations
     *
     * @param array $locations Array of locations with 'lat' and 'lng' keys
     * @return float Maximum distance in kilometers
     */
    public function calculateMaxDistanceBetweenShops(array $locations)
    {
        $maxDistance = 0;
        $count = count($locations);

        for ($i = 0; $i < $count; $i++) {
            $loc1 = $locations[$i];
            if (!isset($loc1['lat'], $loc1['lng'])) {
                continue;
            }

            for ($j = $i + 1; $j < $count; $j++) {
                $loc2 = $locations[$j];
                if (!isset($loc2['lat'], $loc2['lng'])) {
                    continue;
                }

                $distance = $this->calculateDistance(
                    (float) $loc1['lat'],
                    (float) $loc1['lng'],
                    (float) $loc2['lat'],
                    (float) $loc2['lng']
                );

                $maxDistance = max($maxDistance, $distance);
            }
        }

        return $maxDistance;
    }
}
