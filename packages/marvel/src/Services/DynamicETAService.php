<?php

namespace Marvel\Services;

use Carbon\Carbon;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\Order;
use Marvel\Services\RoutingCalculationService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class DynamicETAService
{
    private RoutingCalculationService $routingService;

    public function __construct(RoutingCalculationService $routingService)
    {
        $this->routingService = $routingService;
    }

    /**
     * Calculate dynamic ETA based on current agent location and traffic conditions
     *
     * @param Delivery $delivery
     * @param array $agentLocation
     * @return Carbon
     */
    public function calculateETA(Delivery $delivery, array $agentLocation): Carbon
    {
        $order = $delivery->order;
        $deliveryAddress = $order->shipping_address;

        // Get destination coordinates
        $destinationCoords = $this->getAddressCoordinates($deliveryAddress);
        if (!$destinationCoords) {
            // Fallback to static estimate
            return $this->getStaticETA($delivery);
        }

        // Calculate base travel time
        $travelTime = $this->calculateTravelTime(
            $agentLocation,
            $destinationCoords,
            $delivery->status
        );

        // Add buffer time based on delivery status
        $bufferTime = $this->getBufferTime($delivery->status);

        // Consider traffic conditions
        $trafficMultiplier = $this->getTrafficMultiplier($agentLocation, $destinationCoords);

        // Calculate final ETA
        $totalMinutes = ($travelTime * $trafficMultiplier) + $bufferTime;
        
        return now()->addMinutes($totalMinutes);
    }

    /**
     * Calculate travel time between two points
     *
     * @param array $origin
     * @param array $destination
     * @param string $deliveryStatus
     * @return float Travel time in minutes
     */
    private function calculateTravelTime(array $origin, array $destination, string $deliveryStatus): float
    {
        // Use external routing API if available
        $routingTime = $this->getRoutingAPITravelTime($origin, $destination);
        if ($routingTime !== null) {
            return $routingTime;
        }

        // Fallback to Haversine distance calculation
        $distance = $this->routingService->calculateDistance(
            $origin['lat'],
            $origin['lng'],
            $destination['lat'],
            $destination['lng']
        );

        // Estimate speed based on delivery status and area type
        $averageSpeed = $this->getAverageSpeed($deliveryStatus, $origin);

        // Convert to minutes
        return ($distance / $averageSpeed) * 60;
    }

    /**
     * Get travel time from external routing API
     *
     * @param array $origin
     * @param array $destination
     * @return float|null Travel time in minutes or null if API unavailable
     */
    private function getRoutingAPITravelTime(array $origin, array $destination): ?float
    {
        $apiKey = config('services.google_maps.api_key');
        if (!$apiKey) {
            return null;
        }

        $cacheKey = "routing_time:" . md5(
            $origin['lat'] . ',' . $origin['lng'] . '-' . 
            $destination['lat'] . ',' . $destination['lng']
        );

        return Cache::remember($cacheKey, 300, function () use ($origin, $destination, $apiKey) {
            try {
                $response = Http::timeout(5)->get('https://maps.googleapis.com/maps/api/directions/json', [
                    'origin' => $origin['lat'] . ',' . $origin['lng'],
                    'destination' => $destination['lat'] . ',' . $destination['lng'],
                    'mode' => 'driving',
                    'departure_time' => 'now',
                    'traffic_model' => 'best_guess',
                    'key' => $apiKey,
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    if (isset($data['routes'][0]['legs'][0]['duration_in_traffic']['value'])) {
                        return $data['routes'][0]['legs'][0]['duration_in_traffic']['value'] / 60; // Convert to minutes
                    }
                }
            } catch (\Exception $e) {
                Log::warning('Failed to get routing API travel time', [
                    'error' => $e->getMessage(),
                    'origin' => $origin,
                    'destination' => $destination,
                ]);
            }

            return null;
        });
    }

    /**
     * Get average speed based on delivery status and location
     *
     * @param string $status
     * @param array $location
     * @return float Speed in km/h
     */
    private function getAverageSpeed(string $status, array $location): float
    {
        $baseSpeed = $this->getAreaSpeed($location);

        // Adjust speed based on delivery status
        $speedMultipliers = [
            'ASSIGNED' => 1.0,     // Normal speed to pickup
            'ACCEPTED' => 1.0,     // Normal speed to pickup
            'PICKED_UP' => 0.9,    // Slightly slower with package
            'IN_TRANSIT' => 0.9,   // Slower with package
        ];

        return $baseSpeed * ($speedMultipliers[$status] ?? 1.0);
    }

    /**
     * Get average speed for area type
     *
     * @param array $location
     * @return float Speed in km/h
     */
    private function getAreaSpeed(array $location): float
    {
        // This could be enhanced with actual area type detection
        // For now, use time-based speed estimation
        $hour = now()->hour;

        if ($hour >= 7 && $hour <= 9) {
            return 20; // Morning rush hour
        } elseif ($hour >= 17 && $hour <= 19) {
            return 25; // Evening rush hour
        } elseif ($hour >= 22 || $hour <= 6) {
            return 45; // Night time
        } else {
            return 35; // Normal daytime
        }
    }

    /**
     * Get buffer time based on delivery status
     *
     * @param string $status
     * @return int Buffer time in minutes
     */
    private function getBufferTime(string $status): int
    {
        $bufferTimes = [
            'ASSIGNED' => 15,   // Time to reach pickup + pickup time
            'ACCEPTED' => 10,   // Time to reach pickup
            'PICKED_UP' => 5,   // Small buffer for delivery
            'IN_TRANSIT' => 5,  // Small buffer for delivery
        ];

        return $bufferTimes[$status] ?? 10;
    }

    /**
     * Get traffic multiplier based on current conditions
     *
     * @param array $origin
     * @param array $destination
     * @return float Traffic multiplier (1.0 = normal, >1.0 = slower)
     */
    private function getTrafficMultiplier(array $origin, array $destination): float
    {
        // Simple time-based traffic estimation
        $hour = now()->hour;
        $dayOfWeek = now()->dayOfWeek;

        // Weekend traffic is generally lighter
        if ($dayOfWeek == 0 || $dayOfWeek == 6) {
            return 0.9;
        }

        // Weekday traffic patterns
        if ($hour >= 7 && $hour <= 9) {
            return 1.4; // Morning rush hour
        } elseif ($hour >= 17 && $hour <= 19) {
            return 1.3; // Evening rush hour
        } elseif ($hour >= 12 && $hour <= 14) {
            return 1.1; // Lunch hour
        } else {
            return 1.0; // Normal traffic
        }
    }

    /**
     * Get coordinates for an address
     *
     * @param array $address
     * @return array|null
     */
    private function getAddressCoordinates(array $address): ?array
    {
        // If coordinates are already provided
        if (isset($address['lat'], $address['lng'])) {
            return [
                'lat' => (float) $address['lat'],
                'lng' => (float) $address['lng'],
            ];
        }

        // Try geocoding the address
        return $this->geocodeAddress($address);
    }

    /**
     * Geocode an address to get coordinates
     *
     * @param array $address
     * @return array|null
     */
    private function geocodeAddress(array $address): ?array
    {
        $apiKey = config('services.google_maps.api_key');
        if (!$apiKey) {
            return null;
        }

        $addressString = $this->formatAddressForGeocoding($address);
        $cacheKey = "geocode:" . md5($addressString);

        return Cache::remember($cacheKey, 3600, function () use ($addressString, $apiKey) {
            try {
                $response = Http::timeout(5)->get('https://maps.googleapis.com/maps/api/geocode/json', [
                    'address' => $addressString,
                    'key' => $apiKey,
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    if (isset($data['results'][0]['geometry']['location'])) {
                        $location = $data['results'][0]['geometry']['location'];
                        return [
                            'lat' => $location['lat'],
                            'lng' => $location['lng'],
                        ];
                    }
                }
            } catch (\Exception $e) {
                Log::warning('Failed to geocode address', [
                    'error' => $e->getMessage(),
                    'address' => $addressString,
                ]);
            }

            return null;
        });
    }

    /**
     * Format address for geocoding
     *
     * @param array $address
     * @return string
     */
    private function formatAddressForGeocoding(array $address): string
    {
        $parts = [];

        if (isset($address['address'])) {
            $parts[] = $address['address'];
        }
        if (isset($address['city'])) {
            $parts[] = $address['city'];
        }
        if (isset($address['state'])) {
            $parts[] = $address['state'];
        }
        if (isset($address['zip'])) {
            $parts[] = $address['zip'];
        }
        if (isset($address['country'])) {
            $parts[] = $address['country'];
        }

        return implode(', ', array_filter($parts));
    }

    /**
     * Get static ETA fallback
     *
     * @param Delivery $delivery
     * @return Carbon
     */
    private function getStaticETA(Delivery $delivery): Carbon
    {
        // Use existing estimated delivery time or calculate basic estimate
        if ($delivery->estimated_delivery_time) {
            return $delivery->estimated_delivery_time;
        }

        // Basic estimate: 30-60 minutes depending on status
        $baseMinutes = match ($delivery->status) {
            'ASSIGNED' => 60,
            'ACCEPTED' => 45,
            'PICKED_UP' => 30,
            'IN_TRANSIT' => 20,
            default => 45,
        };

        return now()->addMinutes($baseMinutes);
    }

    /**
     * Calculate ETA for multiple deliveries (route optimization)
     *
     * @param array $deliveries
     * @param array $agentLocation
     * @return array
     */
    public function calculateMultiDeliveryETAs(array $deliveries, array $agentLocation): array
    {
        if (empty($deliveries)) {
            return [];
        }

        // For single delivery, use standard calculation
        if (count($deliveries) === 1) {
            return [
                $deliveries[0]->id => $this->calculateETA($deliveries[0], $agentLocation)
            ];
        }

        // For multiple deliveries, calculate optimized route
        $locations = [$agentLocation]; // Start with agent location
        
        foreach ($deliveries as $delivery) {
            $coords = $this->getAddressCoordinates($delivery->order->shipping_address);
            if ($coords) {
                $locations[] = $coords;
            }
        }

        // Simple route optimization (nearest neighbor)
        $optimizedRoute = $this->optimizeRoute($locations);
        
        $etas = [];
        $currentTime = now();
        $currentLocation = $agentLocation;

        foreach ($optimizedRoute as $index => $locationIndex) {
            if ($locationIndex === 0) continue; // Skip agent starting location
            
            $delivery = $deliveries[$locationIndex - 1];
            $destination = $locations[$locationIndex];
            
            $travelTime = $this->calculateTravelTime($currentLocation, $destination, $delivery->status);
            $currentTime = $currentTime->addMinutes($travelTime + 5); // 5 min delivery time
            
            $etas[$delivery->id] = $currentTime->copy();
            $currentLocation = $destination;
        }

        return $etas;
    }

    /**
     * Simple route optimization using nearest neighbor algorithm
     *
     * @param array $locations
     * @return array Optimized route indices
     */
    private function optimizeRoute(array $locations): array
    {
        if (count($locations) <= 2) {
            return array_keys($locations);
        }

        $route = [0]; // Start with agent location
        $unvisited = range(1, count($locations) - 1);
        $currentLocation = 0;

        while (!empty($unvisited)) {
            $nearestIndex = null;
            $nearestDistance = PHP_FLOAT_MAX;

            foreach ($unvisited as $index) {
                $distance = $this->routingService->calculateDistance(
                    $locations[$currentLocation]['lat'],
                    $locations[$currentLocation]['lng'],
                    $locations[$index]['lat'],
                    $locations[$index]['lng']
                );

                if ($distance < $nearestDistance) {
                    $nearestDistance = $distance;
                    $nearestIndex = $index;
                }
            }

            $route[] = $nearestIndex;
            $currentLocation = $nearestIndex;
            $unvisited = array_diff($unvisited, [$nearestIndex]);
        }

        return $route;
    }
}
