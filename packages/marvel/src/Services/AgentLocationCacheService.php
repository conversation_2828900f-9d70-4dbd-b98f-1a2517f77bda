<?php

namespace Marvel\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Marvel\Database\Models\User;
use Marvel\Database\Models\DeliveryAgentProfile;

class AgentLocationCacheService
{
    private const CACHE_PREFIX = 'agent_location:';
    private const CACHE_TTL = 300; // 5 minutes
    private const NEARBY_AGENTS_PREFIX = 'nearby_agents:';
    private const NEARBY_AGENTS_TTL = 60; // 1 minute

    /**
     * Cache agent location for fast retrieval
     *
     * @param int $agentId
     * @param array $location
     * @param string $status
     * @return void
     */
    public function cacheAgentLocation(int $agentId, array $location, string $status = 'ONLINE'): void
    {
        $cacheKey = self::CACHE_PREFIX . $agentId;
        $data = [
            'lat' => (float) $location['lat'],
            'lng' => (float) $location['lng'],
            'status' => $status,
            'updated_at' => now()->timestamp,
        ];

        Cache::put($cacheKey, $data, self::CACHE_TTL);

        // Also store in Redis sorted set for spatial queries if Redis is available
        if ($this->isRedisAvailable()) {
            $this->storeInRedisSpatialIndex($agentId, $location, $status);
        }
    }

    /**
     * Get cached agent location
     *
     * @param int $agentId
     * @return array|null
     */
    public function getCachedAgentLocation(int $agentId): ?array
    {
        $cacheKey = self::CACHE_PREFIX . $agentId;
        return Cache::get($cacheKey);
    }

    /**
     * Get nearby agents from cache
     *
     * @param float $lat
     * @param float $lng
     * @param float $radiusKm
     * @return array|null
     */
    public function getCachedNearbyAgents(float $lat, float $lng, float $radiusKm): ?array
    {
        $cacheKey = $this->getNearbyAgentsCacheKey($lat, $lng, $radiusKm);
        return Cache::get($cacheKey);
    }

    /**
     * Cache nearby agents result
     *
     * @param float $lat
     * @param float $lng
     * @param float $radiusKm
     * @param array $agents
     * @return void
     */
    public function cacheNearbyAgents(float $lat, float $lng, float $radiusKm, array $agents): void
    {
        $cacheKey = $this->getNearbyAgentsCacheKey($lat, $lng, $radiusKm);
        Cache::put($cacheKey, $agents, self::NEARBY_AGENTS_TTL);
    }

    /**
     * Invalidate agent location cache
     *
     * @param int $agentId
     * @return void
     */
    public function invalidateAgentLocation(int $agentId): void
    {
        $cacheKey = self::CACHE_PREFIX . $agentId;
        Cache::forget($cacheKey);

        // Remove from Redis spatial index
        if ($this->isRedisAvailable()) {
            $this->removeFromRedisSpatialIndex($agentId);
        }

        // Invalidate nearby agents cache (simplified approach - clear all)
        $this->invalidateNearbyAgentsCache();
    }

    /**
     * Invalidate all nearby agents cache
     *
     * @return void
     */
    public function invalidateNearbyAgentsCache(): void
    {
        // Clear all nearby agents cache entries
        $pattern = self::NEARBY_AGENTS_PREFIX . '*';
        $keys = Cache::getStore()->getRedis()->keys($pattern);
        
        if (!empty($keys)) {
            Cache::getStore()->getRedis()->del($keys);
        }
    }

    /**
     * Get agents within radius using Redis geospatial commands
     *
     * @param float $lat
     * @param float $lng
     * @param float $radiusKm
     * @return array
     */
    public function getAgentsWithinRadiusFromRedis(float $lat, float $lng, float $radiusKm): array
    {
        if (!$this->isRedisAvailable()) {
            return [];
        }

        try {
            $redis = Redis::connection();
            $key = 'agent_locations_geo';
            
            // Use GEORADIUS to find nearby agents
            $results = $redis->georadius(
                $key,
                $lng, // Redis uses lng, lat order
                $lat,
                $radiusKm,
                'km',
                ['WITHCOORD', 'WITHDIST', 'ASC']
            );

            $agents = [];
            foreach ($results as $result) {
                $agentId = $result[0];
                $distance = $result[1];
                $coordinates = $result[2];

                // Get additional agent data from cache
                $agentData = $this->getCachedAgentLocation($agentId);
                if ($agentData && $agentData['status'] === 'ONLINE') {
                    $agents[] = [
                        'agent_id' => $agentId,
                        'distance' => $distance,
                        'lat' => $coordinates[1],
                        'lng' => $coordinates[0],
                        'status' => $agentData['status'],
                    ];
                }
            }

            return $agents;
        } catch (\Exception $e) {
            // Fall back to database query if Redis fails
            return [];
        }
    }

    /**
     * Store agent location in Redis spatial index
     *
     * @param int $agentId
     * @param array $location
     * @param string $status
     * @return void
     */
    private function storeInRedisSpatialIndex(int $agentId, array $location, string $status): void
    {
        if ($status !== 'ONLINE') {
            $this->removeFromRedisSpatialIndex($agentId);
            return;
        }

        try {
            $redis = Redis::connection();
            $key = 'agent_locations_geo';
            
            // Add to geospatial index (Redis uses lng, lat order)
            $redis->geoadd($key, $location['lng'], $location['lat'], $agentId);
            
            // Set expiration on the key
            $redis->expire($key, self::CACHE_TTL);
        } catch (\Exception $e) {
            // Silently fail if Redis is not available
        }
    }

    /**
     * Remove agent from Redis spatial index
     *
     * @param int $agentId
     * @return void
     */
    private function removeFromRedisSpatialIndex(int $agentId): void
    {
        try {
            $redis = Redis::connection();
            $key = 'agent_locations_geo';
            $redis->zrem($key, $agentId);
        } catch (\Exception $e) {
            // Silently fail if Redis is not available
        }
    }

    /**
     * Generate cache key for nearby agents
     *
     * @param float $lat
     * @param float $lng
     * @param float $radiusKm
     * @return string
     */
    private function getNearbyAgentsCacheKey(float $lat, float $lng, float $radiusKm): string
    {
        // Round coordinates to reduce cache key variations
        $roundedLat = round($lat, 3);
        $roundedLng = round($lng, 3);
        $roundedRadius = round($radiusKm, 1);
        
        return self::NEARBY_AGENTS_PREFIX . "{$roundedLat}:{$roundedLng}:{$roundedRadius}";
    }

    /**
     * Check if Redis is available
     *
     * @return bool
     */
    private function isRedisAvailable(): bool
    {
        try {
            Redis::connection()->ping();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Warm up cache with active agents
     *
     * @return void
     */
    public function warmUpCache(): void
    {
        $activeAgents = User::whereHas('delivery_agent_profile', function ($query) {
            $query->where('availability_status', 'ONLINE')
                  ->where('kyc_status', 'APPROVED')
                  ->whereNotNull('current_location');
        })
        ->with('delivery_agent_profile')
        ->get();

        foreach ($activeAgents as $agent) {
            $profile = $agent->delivery_agent_profile;
            if ($profile && $profile->current_location) {
                $this->cacheAgentLocation(
                    $agent->id,
                    $profile->current_location,
                    $profile->availability_status
                );
            }
        }
    }
}
