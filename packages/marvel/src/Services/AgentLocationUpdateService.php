<?php

namespace Marvel\Services;

use Marvel\Database\Models\User;
use Marvel\Database\Models\DeliveryAgentProfile;
use Marvel\Database\Models\DeliveryAgentLocationHistory;
use Marvel\Exceptions\DeliveryException;
use Marvel\Helpers\DeliveryExceptionHelper;
use Marvel\Constants\DeliveryConstants;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AgentLocationUpdateService
{
    private AgentLocationCacheService $cacheService;
    private RoutingCalculationService $routingService;

    public function __construct(
        AgentLocationCacheService $cacheService,
        RoutingCalculationService $routingService
    ) {
        $this->cacheService = $cacheService;
        $this->routingService = $routingService;
    }

    /**
     * Update agent location with validation and caching
     *
     * @param int $agentId
     * @param array $location
     * @param string|null $status
     * @return bool
     * @throws DeliveryException
     */
    public function updateAgentLocation(int $agentId, array $location, ?string $status = null): bool
    {
        // Validate input
        if (!$this->isValidLocation($location)) {
            DeliveryExceptionHelper::badRequest(
                DeliveryConstants::SOMETHING_WENT_WRONG,
                'Invalid location coordinates'
            );
        }

        try {
            DB::beginTransaction();

            // Get agent profile
            $profile = DeliveryAgentProfile::where('user_id', $agentId)->first();
            if (!$profile) {
                DeliveryExceptionHelper::notFound(
                    DeliveryConstants::SOMETHING_WENT_WRONG,
                    'Delivery agent profile not found'
                );
            }

            // Check if location update is significant enough
            $shouldUpdate = $this->shouldUpdateLocation($profile, $location);
            $shouldSaveHistory = $this->shouldSaveLocationHistory($profile, $location);

            if ($shouldUpdate) {
                // Update current location
                $updateData = ['current_location' => $location];
                if ($status) {
                    $updateData['availability_status'] = $status;
                }

                $profile->update($updateData);

                // Update cache
                $this->cacheService->cacheAgentLocation(
                    $agentId,
                    $location,
                    $status ?? $profile->availability_status
                );

                // Invalidate nearby agents cache since this agent moved
                $this->cacheService->invalidateNearbyAgentsCache();
            }

            // Save location history if significant movement
            if ($shouldSaveHistory) {
                $this->saveLocationHistory($agentId, $location);
            }

            DB::commit();

            Log::info('Agent location updated', [
                'agent_id' => $agentId,
                'location' => $location,
                'status' => $status,
                'updated_profile' => $shouldUpdate,
                'saved_history' => $shouldSaveHistory,
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to update agent location', [
                'agent_id' => $agentId,
                'location' => $location,
                'error' => $e->getMessage(),
            ]);

            throw new DeliveryException(
                'Failed to update agent location: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Update agent availability status
     *
     * @param int $agentId
     * @param string $status
     * @return bool
     * @throws DeliveryException
     */
    public function updateAgentStatus(int $agentId, string $status): bool
    {
        $validStatuses = ['ONLINE', 'OFFLINE', 'BUSY'];
        if (!in_array($status, $validStatuses)) {
            DeliveryExceptionHelper::badRequest(
                DeliveryConstants::SOMETHING_WENT_WRONG,
                'Invalid availability status'
            );
        }

        try {
            $profile = DeliveryAgentProfile::where('user_id', $agentId)->first();
            if (!$profile) {
                DeliveryExceptionHelper::notFound(
                    DeliveryConstants::SOMETHING_WENT_WRONG,
                    'Delivery agent profile not found'
                );
            }

            $profile->update(['availability_status' => $status]);

            // Update cache with current location and new status
            if ($profile->current_location) {
                $this->cacheService->cacheAgentLocation(
                    $agentId,
                    $profile->current_location,
                    $status
                );
            }

            // If agent goes offline, remove from spatial cache
            if ($status === 'OFFLINE') {
                $this->cacheService->invalidateAgentLocation($agentId);
            }

            Log::info('Agent status updated', [
                'agent_id' => $agentId,
                'status' => $status,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to update agent status', [
                'agent_id' => $agentId,
                'status' => $status,
                'error' => $e->getMessage(),
            ]);

            throw new DeliveryException(
                'Failed to update agent status: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Validate location data
     *
     * @param array $location
     * @return bool
     */
    private function isValidLocation(array $location): bool
    {
        if (!isset($location['lat'], $location['lng'])) {
            return false;
        }

        $lat = (float) $location['lat'];
        $lng = (float) $location['lng'];

        return is_numeric($lat) && is_numeric($lng) &&
               abs($lat) <= 90 && abs($lng) <= 180 &&
               $lat !== 0.0 && $lng !== 0.0; // Exclude null island
    }

    /**
     * Check if location update is significant enough to warrant database update
     *
     * @param DeliveryAgentProfile $profile
     * @param array $newLocation
     * @return bool
     */
    private function shouldUpdateLocation(DeliveryAgentProfile $profile, array $newLocation): bool
    {
        if (!$profile->current_location) {
            return true; // First location update
        }

        $currentLat = (float) $profile->current_location['lat'];
        $currentLng = (float) $profile->current_location['lng'];
        $newLat = (float) $newLocation['lat'];
        $newLng = (float) $newLocation['lng'];

        // Calculate distance moved
        $distance = $this->routingService->calculateDistance(
            $currentLat,
            $currentLng,
            $newLat,
            $newLng
        );

        // Convert to meters
        $distanceMeters = $distance * 1000;

        // Update if moved more than configured threshold
        $threshold = config('delivery.location_update_threshold_meters', 10);
        return $distanceMeters >= $threshold;
    }

    /**
     * Check if location should be saved to history
     *
     * @param DeliveryAgentProfile $profile
     * @param array $newLocation
     * @return bool
     */
    private function shouldSaveLocationHistory(DeliveryAgentProfile $profile, array $newLocation): bool
    {
        // Get last history entry
        $lastHistory = DeliveryAgentLocationHistory::where('agent_user_id', $profile->user_id)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$lastHistory) {
            return true; // First history entry
        }

        // Check time threshold
        $timeThreshold = config('delivery.history_save_threshold_minutes', 5);
        $timeDiff = now()->diffInMinutes($lastHistory->created_at);
        
        if ($timeDiff >= $timeThreshold) {
            return true;
        }

        // Check distance threshold
        $lastLat = (float) $lastHistory->location['lat'];
        $lastLng = (float) $lastHistory->location['lng'];
        $newLat = (float) $newLocation['lat'];
        $newLng = (float) $newLocation['lng'];

        $distance = $this->routingService->calculateDistance(
            $lastLat,
            $lastLng,
            $newLat,
            $newLng
        );

        $distanceMeters = $distance * 1000;
        $distanceThreshold = config('delivery.history_save_threshold_meters', 50);
        
        return $distanceMeters >= $distanceThreshold;
    }

    /**
     * Save location to history
     *
     * @param int $agentId
     * @param array $location
     * @return void
     */
    private function saveLocationHistory(int $agentId, array $location): void
    {
        DeliveryAgentLocationHistory::create([
            'agent_user_id' => $agentId,
            'location' => $location,
            'recorded_at' => now(),
        ]);
    }
}
