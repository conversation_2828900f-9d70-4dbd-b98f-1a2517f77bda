<?php

namespace Marvel\Services;

use Marvel\Database\Models\User;
use Marvel\Enums\Permission;

class LocationDataSanitizer
{
    /**
     * Sanitize agent location data based on user permissions and context
     *
     * @param array $agentData
     * @param User|null $requestingUser
     * @param string $context
     * @return array
     */
    public function sanitizeAgentData(array $agentData, ?User $requestingUser = null, string $context = 'public'): array
    {
        if (!isset($agentData['delivery_agent_profile'])) {
            return $agentData;
        }

        $profile = $agentData['delivery_agent_profile'];
        $canViewPreciseLocation = $this->canViewPreciseLocation($requestingUser, $agentData, $context);
        $canViewLocation = $this->canViewLocation($requestingUser, $agentData, $context);

        // Sanitize location data based on permissions
        if (isset($profile['current_location'])) {
            if (!$canViewLocation) {
                // Remove location entirely
                unset($agentData['delivery_agent_profile']['current_location']);
            } elseif (!$canViewPreciseLocation) {
                // Reduce precision for privacy
                $agentData['delivery_agent_profile']['current_location'] = $this->reduceLocationPrecision(
                    $profile['current_location']
                );
            }
        }

        // Remove sensitive profile information
        $sensitiveFields = [
            'phone_number',
            'emergency_contact',
            'bank_details',
            'identity_documents',
            'vehicle_registration',
        ];

        foreach ($sensitiveFields as $field) {
            if (isset($agentData['delivery_agent_profile'][$field])) {
                if (!$this->canViewSensitiveData($requestingUser, $context)) {
                    unset($agentData['delivery_agent_profile'][$field]);
                }
            }
        }

        // Sanitize personal information
        if (isset($agentData['email']) && !$this->canViewPersonalData($requestingUser, $context)) {
            $agentData['email'] = $this->maskEmail($agentData['email']);
        }

        return $agentData;
    }

    /**
     * Sanitize location history data
     *
     * @param array $locationHistory
     * @param User|null $requestingUser
     * @param string $context
     * @return array
     */
    public function sanitizeLocationHistory(array $locationHistory, ?User $requestingUser = null, string $context = 'public'): array
    {
        if (!$this->canViewLocationHistory($requestingUser, $context)) {
            return [];
        }

        $canViewPrecise = $this->canViewPreciseLocation($requestingUser, [], $context);

        return array_map(function ($entry) use ($canViewPrecise) {
            if (!$canViewPrecise && isset($entry['location'])) {
                $entry['location'] = $this->reduceLocationPrecision($entry['location']);
            }
            return $entry;
        }, $locationHistory);
    }

    /**
     * Check if user can view precise location data
     *
     * @param User|null $user
     * @param array $agentData
     * @param string $context
     * @return bool
     */
    private function canViewPreciseLocation(?User $user, array $agentData, string $context): bool
    {
        if (!$user) {
            return false;
        }

        // Super admin can view everything
        if ($user->hasPermissionTo(Permission::SUPER_ADMIN)) {
            return true;
        }

        // Agent can view their own precise location
        if (isset($agentData['id']) && $agentData['id'] === $user->id) {
            return true;
        }

        // Admin context allows precise location for assignment purposes
        if ($context === 'admin' && $user->hasPermissionTo(Permission::STORE_OWNER)) {
            return true;
        }

        // Delivery system can view precise location for assignment
        if ($context === 'assignment' && $user->hasPermissionTo(Permission::DELIVERY_SYSTEM)) {
            return true;
        }

        return false;
    }

    /**
     * Check if user can view any location data
     *
     * @param User|null $user
     * @param array $agentData
     * @param string $context
     * @return bool
     */
    private function canViewLocation(?User $user, array $agentData, string $context): bool
    {
        if (!$user) {
            return false;
        }

        // Any authenticated user can view approximate location for delivery tracking
        if ($context === 'tracking') {
            return true;
        }

        // Admin users can view location
        if ($user->hasPermissionTo(Permission::STORE_OWNER) || 
            $user->hasPermissionTo(Permission::SUPER_ADMIN)) {
            return true;
        }

        // Agent can view their own location
        if (isset($agentData['id']) && $agentData['id'] === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Check if user can view sensitive data
     *
     * @param User|null $user
     * @param string $context
     * @return bool
     */
    private function canViewSensitiveData(?User $user, string $context): bool
    {
        if (!$user) {
            return false;
        }

        return $user->hasPermissionTo(Permission::SUPER_ADMIN) ||
               ($context === 'admin' && $user->hasPermissionTo(Permission::STORE_OWNER));
    }

    /**
     * Check if user can view personal data
     *
     * @param User|null $user
     * @param string $context
     * @return bool
     */
    private function canViewPersonalData(?User $user, string $context): bool
    {
        if (!$user) {
            return false;
        }

        return $user->hasPermissionTo(Permission::SUPER_ADMIN) ||
               $user->hasPermissionTo(Permission::STORE_OWNER) ||
               $context === 'admin';
    }

    /**
     * Check if user can view location history
     *
     * @param User|null $user
     * @param string $context
     * @return bool
     */
    private function canViewLocationHistory(?User $user, string $context): bool
    {
        if (!$user) {
            return false;
        }

        return $user->hasPermissionTo(Permission::SUPER_ADMIN) ||
               ($context === 'admin' && $user->hasPermissionTo(Permission::STORE_OWNER));
    }

    /**
     * Reduce location precision for privacy
     *
     * @param array $location
     * @param int $precision
     * @return array
     */
    private function reduceLocationPrecision(array $location, int $precision = 3): array
    {
        return [
            'lat' => round((float) $location['lat'], $precision),
            'lng' => round((float) $location['lng'], $precision),
        ];
    }

    /**
     * Mask email address for privacy
     *
     * @param string $email
     * @return string
     */
    private function maskEmail(string $email): string
    {
        $parts = explode('@', $email);
        if (count($parts) !== 2) {
            return $email;
        }

        $username = $parts[0];
        $domain = $parts[1];

        if (strlen($username) <= 2) {
            return $email;
        }

        $maskedUsername = substr($username, 0, 2) . str_repeat('*', strlen($username) - 2);
        return $maskedUsername . '@' . $domain;
    }

    /**
     * Sanitize delivery data for customer view
     *
     * @param array $deliveryData
     * @param User|null $requestingUser
     * @return array
     */
    public function sanitizeDeliveryDataForCustomer(array $deliveryData, ?User $requestingUser = null): array
    {
        // Remove sensitive agent information
        if (isset($deliveryData['delivery_agent'])) {
            $deliveryData['delivery_agent'] = $this->sanitizeAgentData(
                $deliveryData['delivery_agent'],
                $requestingUser,
                'tracking'
            );
        }

        // Keep only necessary delivery information for customer
        $allowedFields = [
            'id',
            'status',
            'estimated_delivery_time',
            'delivery_fee',
            'tracking_number',
            'delivery_agent' => [
                'name',
                'current_location', // Will be sanitized above
            ],
        ];

        return $this->filterArrayByAllowedFields($deliveryData, $allowedFields);
    }

    /**
     * Filter array by allowed fields
     *
     * @param array $data
     * @param array $allowedFields
     * @return array
     */
    private function filterArrayByAllowedFields(array $data, array $allowedFields): array
    {
        $filtered = [];

        foreach ($allowedFields as $key => $value) {
            if (is_numeric($key)) {
                // Simple field
                if (isset($data[$value])) {
                    $filtered[$value] = $data[$value];
                }
            } else {
                // Nested field
                if (isset($data[$key]) && is_array($data[$key])) {
                    $filtered[$key] = $this->filterArrayByAllowedFields($data[$key], $value);
                }
            }
        }

        return $filtered;
    }
}
