<?php

namespace Marvel\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use Marvel\Database\Models\User;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\DeliveryAgentProfile;

class DeliverySecurityAuditService
{
    /**
     * Log security-related delivery events
     *
     * @param string $event
     * @param array $data
     * @param User|null $user
     * @param string $severity
     * @return void
     */
    public function logSecurityEvent(string $event, array $data = [], ?User $user = null, string $severity = 'info'): void
    {
        $logData = [
            'event' => $event,
            'user_id' => $user?->id,
            'user_email' => $user?->email,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'timestamp' => now()->toISOString(),
            'data' => $data,
        ];

        Log::channel('security')->$severity('Delivery Security Event', $logData);
    }

    /**
     * Audit delivery assignment for suspicious activity
     *
     * @param Delivery $delivery
     * @param User $assignedBy
     * @return array
     */
    public function auditDeliveryAssignment(Delivery $delivery, User $assignedBy): array
    {
        $issues = [];

        // Check for rapid assignments by the same user
        $recentAssignments = Delivery::where('assigned_by_user_id', $assignedBy->id)
            ->where('created_at', '>=', now()->subMinutes(5))
            ->count();

        if ($recentAssignments > 10) {
            $issues[] = 'RAPID_ASSIGNMENT_PATTERN';
            $this->logSecurityEvent('rapid_assignment_detected', [
                'assigned_by' => $assignedBy->id,
                'recent_count' => $recentAssignments,
                'delivery_id' => $delivery->id,
            ], $assignedBy, 'warning');
        }

        // Check for assignments outside business hours
        $hour = now()->hour;
        if ($hour < 6 || $hour > 22) {
            $issues[] = 'OFF_HOURS_ASSIGNMENT';
            $this->logSecurityEvent('off_hours_assignment', [
                'hour' => $hour,
                'delivery_id' => $delivery->id,
            ], $assignedBy, 'info');
        }

        // Check for unusual delivery fee
        if ($delivery->delivery_fee > 1000 || $delivery->delivery_fee < 0) {
            $issues[] = 'UNUSUAL_DELIVERY_FEE';
            $this->logSecurityEvent('unusual_delivery_fee', [
                'fee' => $delivery->delivery_fee,
                'delivery_id' => $delivery->id,
            ], $assignedBy, 'warning');
        }

        return $issues;
    }

    /**
     * Audit agent location updates for suspicious patterns
     *
     * @param int $agentId
     * @param array $location
     * @param array $previousLocation
     * @return array
     */
    public function auditLocationUpdate(int $agentId, array $location, ?array $previousLocation = null): array
    {
        $issues = [];

        // Check for impossible movement speed
        if ($previousLocation) {
            $distance = $this->calculateDistance(
                $previousLocation['lat'],
                $previousLocation['lng'],
                $location['lat'],
                $location['lng']
            );

            // Assume max speed of 120 km/h (33.33 m/s)
            $maxSpeed = 33.33; // meters per second
            $timeDiff = 30; // Assume 30 seconds between updates
            $maxDistance = $maxSpeed * $timeDiff; // meters

            if ($distance * 1000 > $maxDistance) {
                $issues[] = 'IMPOSSIBLE_MOVEMENT_SPEED';
                $this->logSecurityEvent('impossible_movement_detected', [
                    'agent_id' => $agentId,
                    'distance_km' => $distance,
                    'time_diff_seconds' => $timeDiff,
                    'calculated_speed_kmh' => ($distance / $timeDiff) * 3600,
                ], null, 'warning');
            }
        }

        // Check for suspicious coordinate patterns
        if ($this->hasSuspiciousCoordinatePattern($location)) {
            $issues[] = 'SUSPICIOUS_COORDINATE_PATTERN';
            $this->logSecurityEvent('suspicious_coordinates', [
                'agent_id' => $agentId,
                'location' => $location,
            ], null, 'warning');
        }

        // Check for location spoofing indicators
        if ($this->hasLocationSpoofingIndicators($location)) {
            $issues[] = 'POTENTIAL_LOCATION_SPOOFING';
            $this->logSecurityEvent('potential_location_spoofing', [
                'agent_id' => $agentId,
                'location' => $location,
            ], null, 'error');
        }

        return $issues;
    }

    /**
     * Audit agent profile for security issues
     *
     * @param DeliveryAgentProfile $profile
     * @return array
     */
    public function auditAgentProfile(DeliveryAgentProfile $profile): array
    {
        $issues = [];

        // Check for incomplete KYC
        if ($profile->kyc_status !== 'APPROVED') {
            $issues[] = 'INCOMPLETE_KYC';
        }

        // Check for missing required documents
        $requiredDocs = ['identity', 'vehicle_registration', 'license'];
        $documents = $profile->kyc_documents ?? [];
        foreach ($requiredDocs as $doc) {
            if (!isset($documents[$doc])) {
                $issues[] = "MISSING_DOCUMENT_{$doc}";
            }
        }

        // Check for suspicious profile changes (rapid KYC approval)
        if ($profile->created_at && $profile->kyc_status === 'APPROVED') {
            $timeSinceCreation = $profile->created_at->diffInHours(now());
            if ($timeSinceCreation < 24) { // Less than 24 hours
                $issues[] = 'RAPID_KYC_APPROVAL';
                $this->logSecurityEvent('rapid_kyc_approval', [
                    'agent_id' => $profile->user_id,
                    'created_at' => $profile->created_at,
                    'hours_since_creation' => $timeSinceCreation,
                ], null, 'warning');
            }
        }

        return $issues;
    }

    /**
     * Check for suspicious coordinate patterns
     *
     * @param array $location
     * @return bool
     */
    private function hasSuspiciousCoordinatePattern(array $location): bool
    {
        $lat = (string) $location['lat'];
        $lng = (string) $location['lng'];

        // Check for repeated digits (e.g., 11.111111, 22.222222)
        if (preg_match('/(\d)\1{4,}/', str_replace('.', '', $lat)) ||
            preg_match('/(\d)\1{4,}/', str_replace('.', '', $lng))) {
            return true;
        }

        // Check for round numbers (e.g., 10.000000, 20.000000)
        if (preg_match('/^\d+\.0+$/', $lat) || preg_match('/^\d+\.0+$/', $lng)) {
            return true;
        }

        // Check for common test coordinates
        $testCoordinates = [
            ['lat' => 0.0, 'lng' => 0.0], // Null Island
            ['lat' => 37.7749, 'lng' => -122.4194], // San Francisco (common test location)
            ['lat' => 40.7128, 'lng' => -74.0060], // New York (common test location)
        ];

        foreach ($testCoordinates as $testCoord) {
            if (abs($location['lat'] - $testCoord['lat']) < 0.001 &&
                abs($location['lng'] - $testCoord['lng']) < 0.001) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for location spoofing indicators
     *
     * @param array $location
     * @return bool
     */
    private function hasLocationSpoofingIndicators(array $location): bool
    {
        // Check if coordinates are exactly on grid lines (common in spoofing)
        $lat = $location['lat'];
        $lng = $location['lng'];

        // Check for coordinates that are exactly on degree boundaries
        if (fmod($lat, 1.0) === 0.0 || fmod($lng, 1.0) === 0.0) {
            return true;
        }

        // Check for coordinates with suspicious precision patterns
        $latStr = (string) $lat;
        $lngStr = (string) $lng;

        // Check for alternating digit patterns
        if (preg_match('/(\d)(\d)\1\2/', str_replace('.', '', $latStr)) ||
            preg_match('/(\d)(\d)\1\2/', str_replace('.', '', $lngStr))) {
            return true;
        }

        return false;
    }

    /**
     * Calculate distance between two coordinates (Haversine formula)
     *
     * @param float $lat1
     * @param float $lng1
     * @param float $lat2
     * @param float $lng2
     * @return float Distance in kilometers
     */
    private function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $lat1 = deg2rad($lat1);
        $lng1 = deg2rad($lng1);
        $lat2 = deg2rad($lat2);
        $lng2 = deg2rad($lng2);

        $latDelta = $lat2 - $lat1;
        $lngDelta = $lng2 - $lng1;

        $angle = 2 * asin(sqrt(
            pow(sin($latDelta / 2), 2) +
            cos($lat1) * cos($lat2) * pow(sin($lngDelta / 2), 2)
        ));

        return $angle * $earthRadius;
    }

    /**
     * Generate security report for delivery operations
     *
     * @param array $filters
     * @return array
     */
    public function generateSecurityReport(array $filters = []): array
    {
        $startDate = $filters['start_date'] ?? now()->subDays(7);
        $endDate = $filters['end_date'] ?? now();

        // Get security events from logs (this would need log parsing implementation)
        $securityEvents = $this->getSecurityEventsFromLogs($startDate, $endDate);

        // Analyze patterns
        $report = [
            'period' => [
                'start' => $startDate,
                'end' => $endDate,
            ],
            'summary' => [
                'total_events' => count($securityEvents),
                'high_severity' => count(array_filter($securityEvents, fn($e) => $e['severity'] === 'error')),
                'medium_severity' => count(array_filter($securityEvents, fn($e) => $e['severity'] === 'warning')),
                'low_severity' => count(array_filter($securityEvents, fn($e) => $e['severity'] === 'info')),
            ],
            'top_issues' => $this->getTopSecurityIssues($securityEvents),
            'recommendations' => $this->generateSecurityRecommendations($securityEvents),
        ];

        return $report;
    }

    /**
     * Get security events from logs (placeholder implementation)
     *
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return array
     */
    private function getSecurityEventsFromLogs($startDate, $endDate): array
    {
        // This would parse security logs and return events
        // For now, return empty array as placeholder
        return [];
    }

    /**
     * Get top security issues from events
     *
     * @param array $events
     * @return array
     */
    private function getTopSecurityIssues(array $events): array
    {
        $issues = [];
        foreach ($events as $event) {
            $type = $event['event'] ?? 'unknown';
            $issues[$type] = ($issues[$type] ?? 0) + 1;
        }

        arsort($issues);
        return array_slice($issues, 0, 10, true);
    }

    /**
     * Generate security recommendations based on events
     *
     * @param array $events
     * @return array
     */
    private function generateSecurityRecommendations(array $events): array
    {
        $recommendations = [];

        $eventTypes = array_column($events, 'event');
        $eventCounts = array_count_values($eventTypes);

        if (($eventCounts['rapid_assignment_detected'] ?? 0) > 5) {
            $recommendations[] = 'Consider implementing stricter rate limiting for delivery assignments';
        }

        if (($eventCounts['impossible_movement_detected'] ?? 0) > 3) {
            $recommendations[] = 'Review agent location validation rules and consider GPS accuracy requirements';
        }

        if (($eventCounts['potential_location_spoofing'] ?? 0) > 1) {
            $recommendations[] = 'Implement additional location verification measures';
        }

        return $recommendations;
    }
}
