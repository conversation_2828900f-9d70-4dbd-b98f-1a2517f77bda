<?php

namespace Marvel\Services;

use Marvel\Database\Models\Order;
use Marvel\Database\Models\User;
use Marvel\Enums\Permission;
use Marvel\Exceptions\DeliveryException;
use Marvel\Helpers\DeliveryConstants;
use Marvel\Helpers\DeliveryExceptionHelper;

class AgentDiscoveryService
{
    private RoutingCalculationService $routingService;

    public function __construct(RoutingCalculationService $routingService)
    {
        $this->routingService = $routingService;
    }

    /**
     * Find nearest available delivery agents for an order
     *
     * @param Order|int $order Order object or order ID
     * @param float $radiusKm Search radius in kilometers (default: 15km)
     * @return \Illuminate\Support\Collection Collection of available agents sorted by distance
     * @throws DeliveryException
     */
    public function findNearestAvailableAgents($order, float $radiusKm = 15)
    {
        try {
            if (is_numeric($order)) {
                $order = Order::with(['shop', 'children'])->find($order); // Eager load children
                if (!$order) {
                    DeliveryExceptionHelper::notFound(DeliveryConstants::ORDER_NOT_FOUND);
                }
            }

            // If the order has children, it's a multi-vendor scenario.
            if ($order->children && $order->children->count() > 0) {
                // Let the multi-vendor method handle it.
                $result = $this->findNearestAgentsForMultiVendor($order, $radiusKm);
                return $result['agents'];
            }

            // For all other cases (standard single orders), use the new SAFE private method.
            return $this->findNearestAvailableAgentsForSingleOrder($order, $radiusKm);
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, $e->getMessage());
        }
    }

    /**
     * Find nearest available delivery agents for a multi-vendor order with optimized distance calculations
     *
     * @param Order $order Parent order with possible child orders
     * @param float $radiusKm Search radius in kilometers (default: 15km)
     * @return array Contains available agents and consolidation info
     * @throws DeliveryException
     */
    public function findNearestAgentsForMultiVendor(Order $order, float $radiusKm = 15)
    {
        try {
            $childOrderCount = $order->children ? $order->children->count() : 0;
            if ($childOrderCount <= 1) {
                $orderToSearch = ($childOrderCount === 1) ? $order->children->first() : $order;
                
                $agents = $this->findNearestAvailableAgentsForSingleOrder($orderToSearch, $radiusKm);

                return [
                    'agents' => $agents,
                    'should_consolidate' => false
                ];
            }

            // Validate child orders and their shops
            $shops = $order->children->map(function ($child) {
                if (!$child->shop) {
                    DeliveryExceptionHelper::badRequest(DeliveryConstants::SOMETHING_WENT_WRONG, 'Child order missing shop information');
                }
                return $child->shop;
            });

            // Extract and validate shop locations
            $shopLocations = $shops->map(function ($shop) {
                $location = $shop->settings['location'] ?? null;
                if (!$this->isValidLocation($location)) {
                    DeliveryExceptionHelper::badRequest(DeliveryConstants::SOMETHING_WENT_WRONG, "Invalid or missing location for shop ID: {$shop->id}");
                }
                return ['shop_id' => $shop->id, 'location' => $location];
            });

            $maxConsolidationDistance = config('delivery.max_consolidation_distance', 20.0);
            $maxShopToCenterDistance = config('delivery.max_shop_to_center_distance', 10.0);

            $centralPoint = $this->routingService->calculateCentralPoint($order->children->pluck('shop')->map(fn ($shop) => $shop->settings['location'] ?? null)->filter()->toArray());
            if (!$centralPoint) {
                return ['agents' => $this->findSplitDeliveryAgents($shopLocations->toArray(), $radiusKm), 'should_consolidate' => false, 'reason' => 'Failed to calculate central point'];
            }

            $maxDistanceBetweenShops = $this->routingService->calculateMaxDistanceBetweenShops($order->children->pluck('shop')->map(fn ($shop) => $shop->settings['location'] ?? null)->filter()->toArray());

            foreach ($shopLocations as $shop) {
                $distanceToCenter = $this->routingService->calculateDistance((float) $centralPoint['lat'], (float) $centralPoint['lng'], (float) $shop['location']['lat'], (float) $shop['location']['lng']);
                if ($distanceToCenter > $maxShopToCenterDistance) {
                    return ['agents' => $this->findSplitDeliveryAgents($shopLocations->toArray(), $radiusKm), 'should_consolidate' => false, 'max_distance_between_shops' => $maxDistanceBetweenShops, 'reason' => "Shop {$shop['shop_id']} too far from central point ({$distanceToCenter}km)"];
                }
            }

            if ($maxDistanceBetweenShops > $maxConsolidationDistance) {
                return ['agents' => $this->findSplitDeliveryAgents($shopLocations->toArray(), $radiusKm), 'should_consolidate' => false, 'max_distance_between_shops' => $maxDistanceBetweenShops, 'reason' => "Maximum distance between shops ({$maxDistanceBetweenShops}km) exceeds consolidation threshold ({$maxConsolidationDistance}km)"];
            }

            $bbox = $this->routingService->calculateBoundingBox($centralPoint['lat'], $centralPoint['lng'], $radiusKm);

            $centralAgents = $this->getAvailableAgentsBaseQuery()->get()->filter(function ($agent) use ($centralPoint, $radiusKm, $bbox) {
                $location = $agent->delivery_agent_profile->current_location;
                $lat = (float) $location['lat'];
                $lng = (float) $location['lng'];
                if ($lat < $bbox['minLat'] || $lat > $bbox['maxLat'] || $lng < $bbox['minLng'] || $lng > $bbox['maxLng']) {
                    return false;
                }
                $distance = $this->routingService->calculateDistance($centralPoint['lat'], $centralPoint['lng'], $lat, $lng);
                $agent->distance = $distance;
                return $distance <= $radiusKm;
            })->sortBy('distance')->values();

            return ['agents' => $centralAgents, 'should_consolidate' => true, 'central_point' => $centralPoint, 'max_distance_between_shops' => $maxDistanceBetweenShops, 'bounding_box' => $bbox];
        } catch (\Exception $e) {
            if ($e instanceof DeliveryException) {
                throw $e;
            }
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, "Failed to find multi-vendor delivery agents: " . $e->getMessage());
        }
    }

    /**
     * [PRIVATE & SAFE] Finds agents for a single, specific order location.
     *
     * @param Order $order
     * @param float $radiusKm
     * @return \Illuminate\Support\Collection
     * @throws DeliveryException
     */
    private function findNearestAvailableAgentsForSingleOrder(Order $order, float $radiusKm)
    {
        if (!$order->requires_delivery) {
            DeliveryExceptionHelper::badRequest(DeliveryConstants::ORDER_DOES_NOT_REQUIRE_DELIVERY);
        }
        if (!$order->shop) {
            DeliveryExceptionHelper::badRequest(DeliveryConstants::SOMETHING_WENT_WRONG, 'Order does not have an associated shop');
        }
        $shopSettings = $order->shop->settings;
        if (!$shopSettings || !isset($shopSettings['location']) || !isset($shopSettings['location']['lat']) || !isset($shopSettings['location']['lng'])) {
            DeliveryExceptionHelper::badRequest(DeliveryConstants::SOMETHING_WENT_WRONG, 'Shop location data is missing or invalid');
        }
        $shopLat = (float) $shopSettings['location']['lat'];
        $shopLng = (float) $shopSettings['location']['lng'];
        if (!is_numeric($shopLat) || !is_numeric($shopLng) || abs($shopLat) > 90 || abs($shopLng) > 180) {
            DeliveryExceptionHelper::badRequest(DeliveryConstants::SOMETHING_WENT_WRONG, 'Invalid shop coordinates');
        }

        $availableAgents = $this->getAvailableAgentsBaseQuery()->get();
        $filteredAgents = collect();
        foreach ($availableAgents as $agent) {
            $profile = $agent->delivery_agent_profile;
            if (!$profile || !$profile->current_location || !isset($profile->current_location['lat']) || !isset($profile->current_location['lng'])) {
                continue;
            }
            $agentLat = (float) $profile->current_location['lat'];
            $agentLng = (float) $profile->current_location['lng'];
            if (!is_numeric($agentLat) || !is_numeric($agentLng) || abs($agentLat) > 90 || abs($agentLng) > 180) {
                continue;
            }
            $distance = $this->routingService->calculateDistance($shopLat, $shopLng, $agentLat, $agentLng);
            $agent->distance = $distance;
            if ($distance <= $radiusKm) {
                $filteredAgents->push($agent);
            }
        }
        return $filteredAgents->sortBy('distance')->values();
    }
    
    /**
     * Get base query for available delivery agents
     *
     * @return \Illuminate\Database\Query\Builder
     */
    private function getAvailableAgentsBaseQuery()
    {
        return User::whereHas('delivery_agent_profile', function ($query) {
            $query->where('availability_status', 'ONLINE')
                  ->where('kyc_status', 'APPROVED')
                  ->whereNotNull('current_location');
        })
        ->with(['delivery_agent_profile'])
        ->where('is_active', true)
        ->whereHas('permissions', function ($query) {
            $query->where('name', Permission::DELIVERY_AGENT);
        });
    }
    
    /**
     * Find agents for split delivery assignments with optimized querying
     * 
     * @param array $shopLocations Array of shop locations with IDs
     * @param float $radiusKm Search radius in kilometers
     * @return array Array of agents by shop ID
     */
    private function findSplitDeliveryAgents(array $shopLocations, float $radiusKm)
    {
        try {
            $agentsByShop = [];
            $boundingBoxes = [];
            foreach ($shopLocations as $shop) {
                $shopLat = (float) $shop['location']['lat'];
                $shopLng = (float) $shop['location']['lng'];
                $boundingBoxes[$shop['shop_id']] = $this->routingService->calculateBoundingBox($shopLat, $shopLng, $radiusKm);
            }
            $allAgents = $this->getAvailableAgentsBaseQuery()->get();
            foreach ($shopLocations as $shop) {
                $shopLat = (float) $shop['location']['lat'];
                $shopLng = (float) $shop['location']['lng'];
                $nearbyAgents = $allAgents->filter(function ($agent) use ($boundingBoxes, $shop, $shopLat, $shopLng, $radiusKm) {
                    $bbox = $boundingBoxes[$shop['shop_id']];
                    $location = $agent->delivery_agent_profile->current_location;
                    $lat = (float) $location['lat'];
                    $lng = (float) $location['lng'];
                    if ($lat < $bbox['minLat'] || $lat > $bbox['maxLat'] || $lng < $bbox['minLng'] || $lng > $bbox['maxLng']) {
                        return false;
                    }
                    $distance = $this->routingService->calculateDistance($shopLat, $shopLng, $lat, $lng);
                    $agent->distance = $distance;
                    return $distance <= $radiusKm;
                })->sortBy('distance')->values();
                $agentsByShop[$shop['shop_id']] = $nearbyAgents;
            }
            return $agentsByShop;
        } catch (\Exception $e) {
            DeliveryExceptionHelper::internalError(DeliveryConstants::SOMETHING_WENT_WRONG, "Failed to find split delivery agents: " . $e->getMessage());
        }
    }

    /**
     * Validate location format and coordinates
     *
     * @param array|null $location
     * @return bool
     */
    private function isValidLocation($location)
    {
        if (!is_array($location) || !isset($location['lat']) || !isset($location['lng'])) {
            return false;
        }
        $lat = (float) $location['lat'];
        $lng = (float) $location['lng'];
        return is_numeric($lat) && is_numeric($lng) && abs($lat) <= 90 && abs($lng) <= 180;
    }
}