<?php

namespace Marvel\Services;

use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Notification;
use Marvel\Database\Models\User;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\Order;
use Marvel\Notifications\DeliveryStatusChangedNotification;
use Marvel\Notifications\DeliveryAssignedNotification;
use Marvel\Notifications\DeliveryETAUpdatedNotification;

class RealTimeNotificationService
{
    /**
     * Send real-time delivery assignment notification
     *
     * @param Delivery $delivery
     * @param bool $isAutomatic
     * @param User|null $assignedBy
     * @return void
     */
    public function notifyDeliveryAssigned(Delivery $delivery, bool $isAutomatic = false, ?User $assignedBy = null): void
    {
        $agent = $delivery->deliveryAgent;
        $customer = $delivery->order->customer;

        // Notify delivery agent
        if ($agent) {
            // Real-time WebSocket notification
            $this->sendWebSocketNotification($agent, [
                'type' => 'delivery_assigned',
                'delivery_id' => $delivery->id,
                'order_tracking_number' => $delivery->order->tracking_number,
                'pickup_address' => $delivery->pickup_addresses[0] ?? null,
                'delivery_address' => $delivery->delivery_address,
                'delivery_fee' => $delivery->delivery_fee,
                'estimated_delivery_time' => $delivery->estimated_delivery_time,
                'is_automatic' => $isAutomatic,
                'assigned_by' => $assignedBy?->name,
                'message' => 'You have been assigned a new delivery',
            ]);

            // Email/SMS notification
            $agent->notify(new DeliveryAssignedNotification($delivery, $isAutomatic, $assignedBy));
        }

        // Notify customer
        if ($customer) {
            $this->sendWebSocketNotification($customer, [
                'type' => 'delivery_assigned',
                'delivery_id' => $delivery->id,
                'agent_name' => $agent?->name,
                'estimated_delivery_time' => $delivery->estimated_delivery_time,
                'message' => 'Your delivery has been assigned to an agent',
            ]);
        }

        // Notify shop owner
        $shop = $delivery->order->shop;
        if ($shop && $shop->owner) {
            $this->sendWebSocketNotification($shop->owner, [
                'type' => 'delivery_assigned',
                'delivery_id' => $delivery->id,
                'order_id' => $delivery->order_id,
                'agent_name' => $agent?->name,
                'message' => 'Delivery has been assigned for order #' . $delivery->order->tracking_number,
            ]);
        }
    }

    /**
     * Send real-time delivery status change notification
     *
     * @param Delivery $delivery
     * @param string $previousStatus
     * @return void
     */
    public function notifyDeliveryStatusChanged(Delivery $delivery, string $previousStatus): void
    {
        $customer = $delivery->order->customer;
        $agent = $delivery->deliveryAgent;

        $statusMessages = [
            'ACCEPTED' => 'Your delivery agent has accepted the delivery',
            'PICKED_UP' => 'Your order has been picked up and is on the way',
            'IN_TRANSIT' => 'Your order is in transit',
            'DELIVERED' => 'Your order has been delivered successfully',
            'FAILED' => 'Delivery attempt failed. We will contact you shortly',
            'CANCELLED' => 'Your delivery has been cancelled',
        ];

        $message = $statusMessages[$delivery->status] ?? 'Delivery status updated';

        // Notify customer
        if ($customer) {
            $this->sendWebSocketNotification($customer, [
                'type' => 'delivery_status_changed',
                'delivery_id' => $delivery->id,
                'status' => $delivery->status,
                'previous_status' => $previousStatus,
                'message' => $message,
                'timestamp' => now()->toISOString(),
            ]);

            // Send push notification for important status changes
            if (in_array($delivery->status, ['PICKED_UP', 'DELIVERED', 'FAILED'])) {
                $customer->notify(new DeliveryStatusChangedNotification($delivery, $previousStatus));
            }
        }

        // Notify shop owner
        $shop = $delivery->order->shop;
        if ($shop && $shop->owner) {
            $this->sendWebSocketNotification($shop->owner, [
                'type' => 'delivery_status_changed',
                'delivery_id' => $delivery->id,
                'order_id' => $delivery->order_id,
                'status' => $delivery->status,
                'previous_status' => $previousStatus,
                'agent_name' => $agent?->name,
                'message' => "Delivery status changed to {$delivery->status} for order #{$delivery->order->tracking_number}",
            ]);
        }
    }

    /**
     * Send real-time ETA update notification
     *
     * @param Delivery $delivery
     * @param \Carbon\Carbon $newETA
     * @param \Carbon\Carbon|null $previousETA
     * @return void
     */
    public function notifyETAUpdated(Delivery $delivery, $newETA, $previousETA = null): void
    {
        $customer = $delivery->order->customer;

        if (!$customer) {
            return;
        }

        $timeDifference = $previousETA ? $newETA->diffInMinutes($previousETA, false) : 0;
        
        $message = 'Delivery time updated';
        if ($timeDifference > 5) {
            $message = 'Delivery will be later than expected';
        } elseif ($timeDifference < -5) {
            $message = 'Delivery will arrive earlier than expected';
        }

        $this->sendWebSocketNotification($customer, [
            'type' => 'eta_updated',
            'delivery_id' => $delivery->id,
            'new_eta' => $newETA->toISOString(),
            'previous_eta' => $previousETA?->toISOString(),
            'time_difference_minutes' => $timeDifference,
            'message' => $message,
        ]);

        // Send push notification if significant delay (>15 minutes)
        if ($timeDifference > 15) {
            $customer->notify(new DeliveryETAUpdatedNotification($delivery, $newETA, $previousETA));
        }
    }

    /**
     * Send agent availability change notification
     *
     * @param User $agent
     * @param string $newStatus
     * @param string $previousStatus
     * @return void
     */
    public function notifyAgentStatusChanged(User $agent, string $newStatus, string $previousStatus): void
    {
        // Notify admin dashboard
        $this->sendWebSocketNotification(null, [
            'type' => 'agent_status_changed',
            'agent_id' => $agent->id,
            'agent_name' => $agent->name,
            'new_status' => $newStatus,
            'previous_status' => $previousStatus,
            'timestamp' => now()->toISOString(),
        ], 'admin.dashboard.agents');

        // If agent goes offline, notify customers with active deliveries
        if ($newStatus === 'OFFLINE') {
            $activeDeliveries = Delivery::where('delivery_agent_user_id', $agent->id)
                ->whereIn('status', ['ASSIGNED', 'ACCEPTED', 'PICKED_UP', 'IN_TRANSIT'])
                ->with('order.customer')
                ->get();

            foreach ($activeDeliveries as $delivery) {
                $customer = $delivery->order->customer;
                if ($customer) {
                    $this->sendWebSocketNotification($customer, [
                        'type' => 'agent_status_changed',
                        'delivery_id' => $delivery->id,
                        'agent_status' => $newStatus,
                        'message' => 'Your delivery agent status has changed. We will keep you updated.',
                    ]);
                }
            }
        }
    }

    /**
     * Send emergency notification for delivery issues
     *
     * @param Delivery $delivery
     * @param string $issueType
     * @param array $details
     * @return void
     */
    public function notifyDeliveryEmergency(Delivery $delivery, string $issueType, array $details = []): void
    {
        $customer = $delivery->order->customer;
        $agent = $delivery->deliveryAgent;
        $shop = $delivery->order->shop;

        $emergencyMessages = [
            'AGENT_UNREACHABLE' => 'We are unable to reach your delivery agent. We are working to resolve this.',
            'DELIVERY_DELAYED' => 'Your delivery is experiencing unexpected delays.',
            'ROUTE_BLOCKED' => 'Your delivery route is blocked. Finding alternative route.',
            'VEHICLE_BREAKDOWN' => 'Delivery vehicle has broken down. Arranging alternative delivery.',
            'WEATHER_DELAY' => 'Delivery delayed due to weather conditions.',
        ];

        $message = $emergencyMessages[$issueType] ?? 'There is an issue with your delivery. We are working to resolve it.';

        // Notify customer immediately
        if ($customer) {
            $this->sendWebSocketNotification($customer, [
                'type' => 'delivery_emergency',
                'delivery_id' => $delivery->id,
                'issue_type' => $issueType,
                'message' => $message,
                'details' => $details,
                'priority' => 'high',
            ]);

            // Send immediate push notification
            $customer->notify(new \Marvel\Notifications\DeliveryEmergencyNotification($delivery, $issueType, $message));
        }

        // Notify shop owner
        if ($shop && $shop->owner) {
            $this->sendWebSocketNotification($shop->owner, [
                'type' => 'delivery_emergency',
                'delivery_id' => $delivery->id,
                'order_id' => $delivery->order_id,
                'issue_type' => $issueType,
                'agent_name' => $agent?->name,
                'message' => "Emergency: {$message} (Order #{$delivery->order->tracking_number})",
                'details' => $details,
            ]);
        }

        // Notify admin/support team
        $this->sendWebSocketNotification(null, [
            'type' => 'delivery_emergency',
            'delivery_id' => $delivery->id,
            'agent_id' => $agent?->id,
            'issue_type' => $issueType,
            'details' => $details,
            'requires_intervention' => true,
        ], 'admin.emergency.deliveries');
    }

    /**
     * Send WebSocket notification
     *
     * @param User|null $user
     * @param array $data
     * @param string|null $channel
     * @return void
     */
    private function sendWebSocketNotification(?User $user, array $data, ?string $channel = null): void
    {
        if ($channel) {
            // Broadcast to specific channel
            Broadcast::channel($channel)->send($data);
        } elseif ($user) {
            // Broadcast to user's private channel
            Broadcast::channel("user.{$user->id}")->send($data);
        }
    }

    /**
     * Send bulk notifications to multiple users
     *
     * @param array $users
     * @param array $data
     * @return void
     */
    public function sendBulkNotifications(array $users, array $data): void
    {
        foreach ($users as $user) {
            $this->sendWebSocketNotification($user, $data);
        }
    }

    /**
     * Get notification preferences for user
     *
     * @param User $user
     * @return array
     */
    public function getNotificationPreferences(User $user): array
    {
        // This could be stored in user preferences
        return [
            'websocket' => true,
            'email' => true,
            'sms' => false,
            'push' => true,
            'delivery_updates' => true,
            'eta_updates' => true,
            'emergency_only' => false,
        ];
    }

    /**
     * Update notification preferences for user
     *
     * @param User $user
     * @param array $preferences
     * @return void
     */
    public function updateNotificationPreferences(User $user, array $preferences): void
    {
        // Store preferences in user profile or separate table
        $user->update(['notification_preferences' => $preferences]);
    }
}
