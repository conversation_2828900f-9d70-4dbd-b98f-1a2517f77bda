<?php

namespace Marvel\Console;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Marvel\Database\Models\DeliveryAgentEarning;
use Marvel\Database\Models\DeliveryAgentTransaction;
use Marvel\Database\Models\DeliveryAgentWithdrawal;
use Marvel\Database\Models\User;
use Marvel\Database\Repositories\DeliveryAgentWithdrawalRepository;
use Marvel\Enums\Role;

class ProcessScheduledPayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marvel:process-scheduled-payments {--min-amount=} {--payment-method=} {--dry-run}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process scheduled payments for delivery agents';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting scheduled payments processing...');

        $minAmount = $this->option('min-amount') ?? config('shop.delivery.min_auto_payout_amount', 50);
        $paymentMethod = $this->option('payment-method') ?? null;
        $dryRun = $this->option('dry-run') ?? false;

        if ($dryRun) {
            $this->warn('Running in dry-run mode. No actual payments will be processed.');
        }

        // Get all active delivery agents with earnings above the minimum amount
        $agents = User::whereHas('roles', function ($q) {
            $q->where('name', Role::DELIVERY_AGENT);
        })
        ->where('is_active', true)
        ->whereHas('delivery_agent_earnings', function ($q) use ($minAmount) {
            $q->where('current_balance', '>=', $minAmount);
        })
        ->with('delivery_agent_earnings')
        ->get();

        $this->info("Found {$agents->count()} agents eligible for payment.");

        $processedCount = 0;
        $failedCount = 0;
        $skippedCount = 0;

        foreach ($agents as $agent) {
            $earnings = $agent->delivery_agent_earnings;

            // Skip if no payment info
            if (!$earnings || !$earnings->payment_info) {
                $this->warn("Agent #{$agent->id} ({$agent->name}) skipped: No payment information.");
                $skippedCount++;
                continue;
            }

            // Skip if payment method doesn't match (if specified)
            if ($paymentMethod && (!isset($earnings->payment_info['method']) || $earnings->payment_info['method'] !== $paymentMethod)) {
                $this->warn("Agent #{$agent->id} ({$agent->name}) skipped: Payment method doesn't match filter.");
                $skippedCount++;
                continue;
            }

            $amount = $earnings->current_balance;

            $this->info("Processing payment of {$amount} for agent #{$agent->id} ({$agent->name})...");

            if ($dryRun) {
                $this->line("Would process payment of {$amount} for agent #{$agent->id} ({$agent->name})");
                $processedCount++;
                continue;
            }

            try {
                DB::beginTransaction();

                // Create withdrawal request
                $withdrawal = DeliveryAgentWithdrawal::create([
                    'delivery_agent_user_id' => $agent->id,
                    'amount' => $amount,
                    'status' => 'PENDING',
                    'payment_method_details' => $earnings->payment_info,
                    'requested_at' => now(),
                    'notes' => 'Automatic scheduled payment',
                ]);

                // Update earnings
                $earnings->current_balance -= $amount;
                $earnings->pending_withdrawal_amount += $amount;
                $earnings->save();

                // Create transaction record
                DeliveryAgentTransaction::create([
                    'delivery_agent_user_id' => $agent->id,
                    'transaction_type' => DeliveryAgentTransaction::TYPE_WITHDRAWAL,
                    'reference_type' => DeliveryAgentTransaction::REF_WITHDRAWAL,
                    'reference_id' => $withdrawal->id,
                    'amount' => -1 * abs($amount),
                    'status' => DeliveryAgentTransaction::STATUS_PENDING,
                    'payment_method' => $earnings->payment_info['method'] ?? DeliveryAgentTransaction::METHOD_OTHER,
                    'provider' => $earnings->payment_info['provider'] ?? null,
                    'payment_details' => $earnings->payment_info,
                    'notes' => 'Automatic scheduled payment',
                ]);

                DB::commit();
                $this->info("Payment of {$amount} for agent #{$agent->id} ({$agent->name}) processed successfully.");
                $processedCount++;
            } catch (\Exception $e) {
                DB::rollBack();
                $this->error("Failed to process payment for agent #{$agent->id} ({$agent->name}): {$e->getMessage()}");
                Log::error("Failed to process scheduled payment: {$e->getMessage()}", [
                    'agent_id' => $agent->id,
                    'amount' => $amount,
                    'exception' => $e,
                ]);
                $failedCount++;
            }
        }

        $this->info("Scheduled payments processing completed.");
        $this->info("Processed: {$processedCount}, Failed: {$failedCount}, Skipped: {$skippedCount}");

        return 0;
    }
}
