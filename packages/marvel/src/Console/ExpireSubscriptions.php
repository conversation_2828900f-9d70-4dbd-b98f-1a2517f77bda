<?php

namespace Marvel\Console;

use Illuminate\Console\Command;
use Marvel\Database\Models\Subscription;
use <PERSON><PERSON>\Api\Types\SubscriptionStatus;

class ExpireSubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:expire-subscriptions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Deactivate expired subscriptions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $expired = Subscription::where('is_active' , true)->where('status', SubscriptionStatus::STATUS_ACTIVE)->where('end_date' , '<' , now())->update(['is_active' => false , 'status' => SubscriptionStatus::STATUS_SUSPENDED]);

        // check and send emails when subscription turns to end soon

        // Three days before it ends, send email to user

        // One day before it expires, send email to let the user we will reniew his subscription

        // The day we charge, if we don't have the possibility to charge before we end the subcription

        $this->info("Expired $expired subscription");
    }
}
