<?php

namespace Marvel\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\User;

class DeliveryAssigned implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The delivery instance.
     *
     * @var \Marvel\Database\Models\Delivery
     */
    public $delivery;

    /**
     * The user who assigned the delivery, or null if automatic.
     *
     * @var \Marvel\Database\Models\User|null
     */
    public $assignedBy;

    /**
     * Whether this was an automatic assignment.
     *
     * @var bool
     */
    public $isAutomatic;

    /**
     * Create a new event instance.
     *
     * @param  \Marvel\Database\Models\Delivery  $delivery
     * @param  \Marvel\Database\Models\User|null  $assignedBy
     * @param  bool  $isAutomatic
     * @return void
     */
    public function __construct(Delivery $delivery, ?User $assignedBy = null, bool $isAutomatic = false)
    {
        $this->delivery = $delivery;
        $this->assignedBy = $assignedBy;
        $this->isAutomatic = $isAutomatic;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('delivery.agent.' . $this->delivery->delivery_agent_user_id);
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'delivery.assigned';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        $order = $this->delivery->order;
        $message = 'You have been assigned a new delivery for order #' . $order->tracking_number;

        if ($this->isAutomatic) {
            $message .= ' (automatically assigned)';
        } elseif ($this->assignedBy) {
            $message .= ' by ' . $this->assignedBy->name;
        }

        return [
            'delivery_id' => $this->delivery->id,
            'order_id' => $order->id,
            'order_tracking' => $order->tracking_number,
            'type' => 'delivery_assigned',
            'is_automatic' => $this->isAutomatic,
            'assigned_by' => $this->assignedBy ? [
                'id' => $this->assignedBy->id,
                'name' => $this->assignedBy->name,
            ] : null,
            'message' => $message,
        ];
    }
}
