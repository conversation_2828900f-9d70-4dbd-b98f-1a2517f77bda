<?php

namespace Marvel\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Marvel\Database\Models\User;

class AgentLocationUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $agent;
    public $locationData;

    /**
     * Create a new event instance.
     *
     * @param User $agent
     * @param array $locationData
     */
    public function __construct(User $agent, array $locationData)
    {
        $this->agent = $agent;
        $this->locationData = $locationData;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return [
            // Private channel for the agent themselves
            new PrivateChannel('agent.' . $this->agent->id),
            
            // Private channel for admin tracking
            new PrivateChannel('admin.agent-tracking'),
            
            // Private channel for delivery tracking if agent has active deliveries
            new PrivateChannel('delivery.agent.' . $this->agent->id),
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'agent_id' => $this->agent->id,
            'agent_name' => $this->agent->name,
            'location' => $this->locationData,
            'availability_status' => $this->agent->delivery_agent_profile?->availability_status,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'agent.location.updated';
    }
}
