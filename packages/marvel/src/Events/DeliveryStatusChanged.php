<?php

namespace Marvel\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\User;

class DeliveryStatusChanged implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The delivery instance.
     *
     * @var \Marvel\Database\Models\Delivery
     */
    public $delivery;

    /**
     * The previous status.
     *
     * @var string
     */
    public $previousStatus;

    /**
     * The user who updated the status.
     *
     * @var \Marvel\Database\Models\User
     */
    public $updatedBy;

    /**
     * Create a new event instance.
     *
     * @param  \Marvel\Database\Models\Delivery  $delivery
     * @param  string  $previousStatus
     * @param  \Marvel\Database\Models\User  $updatedBy
     * @return void
     */
    public function __construct(Delivery $delivery, string $previousStatus, User $updatedBy)
    {
        $this->delivery = $delivery;
        $this->previousStatus = $previousStatus;
        $this->updatedBy = $updatedBy;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        // Broadcast to both the customer and the delivery agent
        return [
            new PrivateChannel('delivery.customer.' . $this->delivery->order->customer_id),
            new PrivateChannel('delivery.agent.' . $this->delivery->delivery_agent_user_id),
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'delivery.status.changed';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        $order = $this->delivery->order;
        $statusMessage = $this->getStatusMessage();

        return [
            'delivery_id' => $this->delivery->id,
            'order_id' => $order->id,
            'order_tracking' => $order->tracking_number,
            'type' => 'delivery_status_changed',
            'previous_status' => $this->previousStatus,
            'current_status' => $this->delivery->status,
            'message' => $statusMessage,
        ];
    }

    /**
     * Get a human-readable message for the status change.
     *
     * @return string
     */
    protected function getStatusMessage()
    {
        $order = $this->delivery->order;
        $status = $this->delivery->status;
        $trackingNumber = $order->tracking_number;

        switch ($status) {
            case 'ACCEPTED':
                return "Your delivery for order #{$trackingNumber} has been accepted by the delivery agent.";
            case 'PICKED_UP':
                return "Your order #{$trackingNumber} has been picked up by the delivery agent.";
            case 'IN_TRANSIT':
                return "Your order #{$trackingNumber} is now in transit.";
            case 'DELIVERED':
                return "Your order #{$trackingNumber} has been delivered successfully.";
            case 'FAILED':
                return "Delivery for order #{$trackingNumber} has failed. Our team will contact you shortly.";
            default:
                return "The status of your delivery for order #{$trackingNumber} has been updated to {$status}.";
        }
    }
}
