<?php

namespace Marvel\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\User;

class DeliveryCancelled implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The delivery instance.
     *
     * @var \Marvel\Database\Models\Delivery
     */
    public $delivery;

    /**
     * The user who cancelled the delivery.
     *
     * @var \Marvel\Database\Models\User
     */
    public $cancelledBy;

    /**
     * The reason for cancellation.
     *
     * @var string|null
     */
    public $reason;

    /**
     * Create a new event instance.
     *
     * @param  \Marvel\Database\Models\Delivery  $delivery
     * @param  \Marvel\Database\Models\User  $cancelledBy
     * @param  string|null  $reason
     * @return void
     */
    public function __construct(Delivery $delivery, User $cancelledBy, ?string $reason = null)
    {
        $this->delivery = $delivery;
        $this->cancelledBy = $cancelledBy;
        $this->reason = $reason;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        // Broadcast to both the customer and the delivery agent
        return [
            new PrivateChannel('delivery.customer.' . $this->delivery->order->customer_id),
            new PrivateChannel('delivery.agent.' . $this->delivery->delivery_agent_user_id),
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'delivery.cancelled';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        $order = $this->delivery->order;
        $message = 'Delivery for order #' . $order->tracking_number . ' has been cancelled' .
                   ($this->reason ? ': ' . $this->reason : '.');

        return [
            'delivery_id' => $this->delivery->id,
            'order_id' => $order->id,
            'order_tracking' => $order->tracking_number,
            'type' => 'delivery_cancelled',
            'reason' => $this->reason,
            'message' => $message,
        ];
    }
}
