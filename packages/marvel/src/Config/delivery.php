<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Automatic Assignment Configuration
    |--------------------------------------------------------------------------
    |
    | These settings control the automatic delivery assignment system.
    |
    */

    'auto_assignment_enabled' => env('DELIVERY_AUTO_ASSIGNMENT_ENABLED', true),

    'auto_assignment_radius' => env('DELIVERY_AUTO_ASSIGNMENT_RADIUS', 15), // kilometers

    'auto_assignment_retry_attempts' => env('DELIVERY_AUTO_ASSIGNMENT_RETRY_ATTEMPTS', 3),

    'auto_assignment_retry_delay' => env('DELIVERY_AUTO_ASSIGNMENT_RETRY_DELAY', 5), // minutes

    'auto_assignment_delay' => env('DELIVERY_AUTO_ASSIGNMENT_DELAY', 0), // minutes to wait before assignment

    'auto_assignment_queue' => env('DELIVERY_AUTO_ASSIGNMENT_QUEUE', 'default'),

    /*
    |--------------------------------------------------------------------------
    | Agent Selection Strategy
    |--------------------------------------------------------------------------
    |
    | Determines how agents are selected for automatic assignment:
    | - 'nearest': Select the closest agent (default)
    | - 'round_robin': Distribute assignments evenly
    | - 'least_busy': Select agent with fewest active deliveries
    | - 'best_rating': Select highest rated agent
    |
    */

    'agent_selection_strategy' => env('DELIVERY_AGENT_SELECTION_STRATEGY', 'nearest'),

    /*
    |--------------------------------------------------------------------------
    | Delivery Fee Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for calculating delivery fees in automatic assignment.
    |
    */

    'base_delivery_fee' => env('DELIVERY_BASE_FEE', 10.0),

    'per_km_fee' => env('DELIVERY_PER_KM_FEE', 1.0),

    /*
    |--------------------------------------------------------------------------
    | Multi-Vendor Delivery Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for handling multi-vendor deliveries and consolidation.
    |
    */

    'max_consolidation_distance' => env('DELIVERY_MAX_CONSOLIDATION_DISTANCE', 20.0), // Maximum absolute distance in km between shops for consolidation

    'max_shop_to_center_distance' => env('DELIVERY_MAX_SHOP_TO_CENTER_DISTANCE', 10.0), // Maximum allowed distance from any shop to central point

    'consolidated_delivery_fee_multiplier' => env('DELIVERY_CONSOLIDATED_FEE_MULTIPLIER', 1.5), // Multiplier for consolidated deliveries

    'multi_pickup_fee' => env('DELIVERY_MULTI_PICKUP_FEE', 5.0), // Additional fee per extra pickup location

    /*
    |--------------------------------------------------------------------------
    | Distance Calculation
    |--------------------------------------------------------------------------
    |
    | Settings for geographic distance calculations.
    |
    */

    'earth_radius_km' => 6371, // Earth's radius in kilometers

    'max_search_radius' => env('DELIVERY_MAX_SEARCH_RADIUS', 50), // Maximum search radius in km

    /*
    |--------------------------------------------------------------------------
    | Assignment Triggers
    |--------------------------------------------------------------------------
    |
    | Order statuses that should trigger automatic delivery assignment.
    |
    */

    'assignment_trigger_statuses' => [
        'PROCESSING',
        'AT_LOCAL_FACILITY',
        'PENDING',
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Statuses for Assignment
    |--------------------------------------------------------------------------
    |
    | Payment statuses that allow delivery assignment.
    |
    */

    'assignment_payment_statuses' => [
        'SUCCESS',
        'CASH_ON_DELIVERY',
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for delivery assignment logging.
    |
    */

    'log_assignments' => env('DELIVERY_LOG_ASSIGNMENTS', true),

    'log_channel' => env('DELIVERY_LOG_CHANNEL', 'daily'),

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Settings for delivery assignment notifications.
    |
    */

    'notify_agents_on_assignment' => env('DELIVERY_NOTIFY_AGENTS', true),

    'notify_customers_on_assignment' => env('DELIVERY_NOTIFY_CUSTOMERS', true),

    /*
    |--------------------------------------------------------------------------
    | Performance Monitoring
    |--------------------------------------------------------------------------
    |
    | Settings for monitoring assignment performance.
    |
    */

    'track_assignment_metrics' => env('DELIVERY_TRACK_METRICS', true),

    'assignment_timeout_minutes' => env('DELIVERY_ASSIGNMENT_TIMEOUT', 30),

    /*
    |--------------------------------------------------------------------------
    | agent location update
    |--------------------------------------------------------------------------
    |
    | Settings for updating agent location.
    |
    */
    'location_update_threshold_meters' => 10,
    'history_save_threshold_meters' => 50,
    'history_save_threshold_minutes' => 5,
];
