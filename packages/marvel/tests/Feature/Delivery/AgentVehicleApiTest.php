<?php

namespace Marvel\Tests\Feature\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Marvel\Database\Models\User;
use Marvel\Database\Models\Vehicle;
use Marvel\Enums\Permission;
use Tests\TestCase;

class AgentVehicleApiTest extends TestCase
{
    use RefreshDatabase;

    protected $agent;

    public function setUp(): void
    {
        parent::setUp();

        // Create delivery agent
        $this->agent = User::factory()->create();
        $this->agent->givePermissionTo(Permission::DELIVERY_AGENT);

        // Create agent profile
        $this->agent->delivery_agent_profile()->create([
            'availability_status' => 'OFFLINE',
            'kyc_status' => 'APPROVED',
        ]);
    }

    /** @test */
    public function agent_can_list_vehicles()
    {
        // Create vehicles
        Vehicle::create([
            'delivery_agent_user_id' => $this->agent->id,
            'type' => 'CAR',
            'registration_number' => 'ABC123',
            'is_verified' => true,
            'is_active' => true,
        ]);

        Vehicle::create([
            'delivery_agent_user_id' => $this->agent->id,
            'type' => 'BIKE',
            'registration_number' => 'XYZ789',
            'is_verified' => false,
            'is_active' => false,
        ]);

        $response = $this->actingAs($this->agent)->getJson('/api/agent/vehicles');

        $response->assertStatus(200)
            ->assertJsonCount(2, 'data')
            ->assertJsonPath('data.0.type', 'CAR')
            ->assertJsonPath('data.1.type', 'BIKE');
    }

    /** @test */
    public function agent_can_create_vehicle()
    {
        $registrationFile = \Illuminate\Http\UploadedFile::fake()->image('registration.jpg');
        $insuranceFile = \Illuminate\Http\UploadedFile::fake()->image('insurance.jpg');

        $response = $this->actingAs($this->agent)->postJson('/api/agent/vehicles', [
            'type' => 'VAN',
            'make' => 'Toyota',
            'model' => 'Hiace',
            'registration_number' => 'VAN456',
            'color' => 'White',
            'vehicle_registration' => $registrationFile,
            'vehicle_insurance' => $insuranceFile,
        ]);

        $response->assertStatus(201)
            ->assertJsonPath('type', 'VAN')
            ->assertJsonPath('make', 'Toyota')
            ->assertJsonPath('registration_number', 'VAN456');

        $this->assertDatabaseHas('vehicles', [
            'delivery_agent_user_id' => $this->agent->id,
            'type' => 'VAN',
            'registration_number' => 'VAN456',
        ]);
    }

    /** @test */
    public function agent_can_update_vehicle()
    {
        // Create vehicle
        $vehicle = Vehicle::create([
            'delivery_agent_user_id' => $this->agent->id,
            'type' => 'CAR',
            'registration_number' => 'ABC123',
            'is_verified' => true,
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->agent)->putJson("/api/agent/vehicles/{$vehicle->id}", [
            'color' => 'Blue',
            'model' => 'Corolla',
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('color', 'Blue')
            ->assertJsonPath('model', 'Corolla');

        $this->assertDatabaseHas('vehicles', [
            'id' => $vehicle->id,
            'color' => 'Blue',
            'model' => 'Corolla',
        ]);
    }

    /** @test */
    public function agent_can_set_active_vehicle()
    {
        // Create vehicles
        $vehicle1 = Vehicle::create([
            'delivery_agent_user_id' => $this->agent->id,
            'type' => 'CAR',
            'registration_number' => 'ABC123',
            'is_verified' => true,
            'is_active' => true,
        ]);

        $vehicle2 = Vehicle::create([
            'delivery_agent_user_id' => $this->agent->id,
            'type' => 'BIKE',
            'registration_number' => 'XYZ789',
            'is_verified' => true,
            'is_active' => false,
        ]);

        $response = $this->actingAs($this->agent)->postJson("/api/agent/vehicles/{$vehicle2->id}/set-active");

        $response->assertStatus(200)
            ->assertJsonPath('profile.active_vehicle_id', $vehicle2->id);

        $this->agent->delivery_agent_profile->refresh();
        $this->assertEquals($vehicle2->id, $this->agent->delivery_agent_profile->active_vehicle_id);
    }

    /** @test */
    public function agent_cannot_access_another_agents_vehicle()
    {
        // Create another agent
        $anotherAgent = User::factory()->create();
        $anotherAgent->givePermissionTo(Permission::DELIVERY_AGENT);

        // Create vehicle for another agent
        $vehicle = Vehicle::create([
            'delivery_agent_user_id' => $anotherAgent->id,
            'type' => 'CAR',
            'registration_number' => 'OTHER123',
            'is_verified' => true,
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->agent)->getJson("/api/agent/vehicles/{$vehicle->id}");

        $response->assertStatus(404);
    }
}
