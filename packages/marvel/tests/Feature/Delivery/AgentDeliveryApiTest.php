<?php

namespace Marvel\Tests\Feature\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\User;
use Marvel\Enums\Permission;
use Tests\TestCase;

class AgentDeliveryApiTest extends TestCase
{
    use RefreshDatabase;

    protected $agent;
    protected $admin;
    protected $order;
    protected $delivery;

    public function setUp(): void
    {
        parent::setUp();

        // Create delivery agent
        $this->agent = User::factory()->create();
        $this->agent->givePermissionTo(Permission::DELIVERY_AGENT);

        // Create agent profile
        $this->agent->delivery_agent_profile()->create([
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
        ]);

        // Create admin
        $this->admin = User::factory()->create();
        $this->admin->givePermissionTo(Permission::SUPER_ADMIN);

        // Create shop
        $shop = Shop::factory()->create([
            'owner_id' => $this->admin->id,
        ]);

        // Create order
        $this->order = Order::factory()->create([
            'shop_id' => $shop->id,
            'requires_delivery' => true,
        ]);

        // Create delivery
        $this->delivery = Delivery::create([
            'order_id' => $this->order->id,
            'delivery_agent_user_id' => $this->agent->id,
            'status' => 'ASSIGNED',
            'pickup_address' => ['address' => '123 Pickup St'],
            'delivery_address' => ['address' => '456 Delivery St'],
            'delivery_fee' => 15.00,
            'assigned_at' => now(),
            'assigned_by_user_id' => $this->admin->id,
        ]);

        // Update order with delivery ID
        $this->order->update(['delivery_id' => $this->delivery->id]);
    }

    /** @test */
    public function agent_can_list_deliveries()
    {
        $response = $this->actingAs($this->agent)->getJson('/api/agent/deliveries');

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $this->delivery->id)
            ->assertJsonPath('data.0.status', 'ASSIGNED');
    }

    /** @test */
    public function agent_can_view_delivery_details()
    {
        $response = $this->actingAs($this->agent)->getJson("/api/agent/deliveries/{$this->delivery->id}");

        $response->assertStatus(200)
            ->assertJsonPath('id', $this->delivery->id)
            ->assertJsonPath('order_id', $this->order->id)
            ->assertJsonPath('delivery_fee', 15.00);
    }

    /** @test */
    public function agent_can_accept_delivery()
    {
        $response = $this->actingAs($this->agent)->postJson("/api/agent/deliveries/{$this->delivery->id}/accept", [
            'notes' => 'I will deliver this order',
            'location' => ['lat' => 40.7128, 'lng' => -74.0060],
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('delivery.status', 'ACCEPTED_BY_AGENT');

        $this->assertDatabaseHas('deliveries', [
            'id' => $this->delivery->id,
            'status' => 'ACCEPTED_BY_AGENT',
        ]);

        $this->assertDatabaseHas('delivery_status_logs', [
            'delivery_id' => $this->delivery->id,
            'status' => 'ACCEPTED_BY_AGENT',
            'notes' => 'I will deliver this order',
        ]);
    }

    /** @test */
    public function agent_can_reject_delivery()
    {
        $response = $this->actingAs($this->agent)->postJson("/api/agent/deliveries/{$this->delivery->id}/reject", [
            'reason' => 'Too far away',
            'notes' => 'The delivery location is too far',
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('delivery.status', 'REJECTED_BY_AGENT');

        $this->assertDatabaseHas('deliveries', [
            'id' => $this->delivery->id,
            'status' => 'REJECTED_BY_AGENT',
        ]);
    }

    /** @test */
    public function agent_can_update_delivery_status()
    {
        // First accept the delivery
        $this->actingAs($this->agent)->postJson("/api/agent/deliveries/{$this->delivery->id}/accept");

        // Then pick up
        $response = $this->actingAs($this->agent)->postJson("/api/agent/deliveries/{$this->delivery->id}/pickup", [
            'notes' => 'Picked up the order',
            'location' => ['lat' => 40.7128, 'lng' => -74.0060],
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('delivery.status', 'PICKED_UP');

        // Then in transit
        $response = $this->actingAs($this->agent)->postJson("/api/agent/deliveries/{$this->delivery->id}/in-transit", [
            'notes' => 'On my way',
            'location' => ['lat' => 40.7129, 'lng' => -74.0061],
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('delivery.status', 'IN_TRANSIT');

        // Then reached destination
        $response = $this->actingAs($this->agent)->postJson("/api/agent/deliveries/{$this->delivery->id}/reached", [
            'notes' => 'Arrived at destination',
            'location' => ['lat' => 40.7130, 'lng' => -74.0062],
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('delivery.status', 'REACHED_DESTINATION');
    }

    /** @test */
    public function agent_can_add_proof_of_delivery()
    {
        // Set up delivery status to REACHED_DESTINATION
        $this->delivery->update(['status' => 'REACHED_DESTINATION']);

        $podImage = \Illuminate\Http\UploadedFile::fake()->image('pod.jpg');

        $response = $this->actingAs($this->agent)->postJson("/api/agent/deliveries/{$this->delivery->id}/proof-of-delivery", [
            'pod_type' => 'PHOTO',
            'pod_image' => $podImage,
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('delivery.pod_type', 'PHOTO');
    }

    /** @test */
    public function agent_can_mark_delivery_as_delivered()
    {
        // Set up delivery status to REACHED_DESTINATION with POD
        $this->delivery->update([
            'status' => 'REACHED_DESTINATION',
            'pod_type' => 'PHOTO',
            'proof_of_delivery' => 'pod_image.jpg',
        ]);

        $response = $this->actingAs($this->agent)->postJson("/api/agent/deliveries/{$this->delivery->id}/deliver", [
            'notes' => 'Delivered successfully',
            'location' => ['lat' => 40.7130, 'lng' => -74.0062],
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('delivery.status', 'DELIVERED');

        $this->assertDatabaseHas('deliveries', [
            'id' => $this->delivery->id,
            'status' => 'DELIVERED',
        ]);
    }

    /** @test */
    public function agent_can_confirm_payment_for_cod()
    {
        // Set up delivery status to DELIVERED
        $this->delivery->update(['status' => 'DELIVERED']);

        // Set order payment status
        $this->order->update(['payment_status' => 'payment-cash-on-delivery']);

        $response = $this->actingAs($this->agent)->postJson("/api/agent/deliveries/{$this->delivery->id}/confirm-payment");

        $response->assertStatus(200)
            ->assertJsonPath('delivery.status', 'COMPLETED');

        $this->order->refresh();
        $this->assertEquals('payment-success', $this->order->payment_status);
    }
}
