<?php

namespace Marvel\Tests\Feature\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\DeliveryAgentEarning;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\User;
use Marvel\Enums\Permission;
use Tests\TestCase;

class AgentDashboardApiTest extends TestCase
{
    use RefreshDatabase;

    protected $agent;
    protected $admin;
    protected $order;
    protected $delivery;
    protected $earnings;

    public function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create();
        $this->admin->givePermissionTo(Permission::SUPER_ADMIN);

        // Create agent user
        $this->agent = User::factory()->create();
        $this->agent->givePermissionTo(Permission::DELIVERY_AGENT);

        // Create agent profile
        $this->agent->delivery_agent_profile()->create([
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
            'current_location' => ['lat' => 40.7128, 'lng' => -74.0060],
        ]);

        // Create shop
        $shop = Shop::factory()->create();

        // Create order
        $this->order = Order::factory()->create([
            'shop_id' => $shop->id,
            'requires_delivery' => true,
        ]);

        // Create delivery
        $this->delivery = Delivery::create([
            'order_id' => $this->order->id,
            'delivery_agent_user_id' => $this->agent->id,
            'status' => 'ASSIGNED',
            'pickup_address' => ['address' => '123 Pickup St'],
            'delivery_address' => ['address' => '456 Delivery St'],
            'delivery_fee' => 15.00,
            'assigned_at' => now(),
            'assigned_by_user_id' => $this->admin->id,
        ]);

        // Update order with delivery ID
        $this->order->update(['delivery_id' => $this->delivery->id]);

        // Create another delivery that's completed
        $completedDelivery = Delivery::create([
            'order_id' => $this->order->id,
            'delivery_agent_user_id' => $this->agent->id,
            'status' => 'COMPLETED',
            'pickup_address' => ['address' => '123 Pickup St'],
            'delivery_address' => ['address' => '456 Delivery St'],
            'delivery_fee' => 20.00,
            'assigned_at' => now()->subDays(1),
            'assigned_by_user_id' => $this->admin->id,
            'completed_at' => now()->subHours(2),
        ]);

        // Create earnings record
        $this->earnings = DeliveryAgentEarning::create([
            'delivery_agent_user_id' => $this->agent->id,
            'total_earnings' => 100.00,
            'withdrawn_amount' => 50.00,
            'current_balance' => 50.00,
            'pending_withdrawal_amount' => 0.00,
        ]);
    }

    /** @test */
    public function agent_can_view_dashboard_summary()
    {
        $response = $this->actingAs($this->agent)->getJson('/api/agent/dashboard/summary');

        $response->assertStatus(200)
            ->assertJsonPath('success', true)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'earnings' => [
                        'today',
                        'week',
                        'month'
                    ],
                    'delivery_counts'
                ]
            ]);
    }

    /** @test */
    public function agent_can_view_recent_deliveries()
    {
        $response = $this->actingAs($this->agent)->getJson('/api/agent/dashboard/recent-deliveries');

        $response->assertStatus(200)
            ->assertJsonPath('success', true)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'order_id',
                        'status',
                        'delivery_fee'
                    ]
                ]
            ])
            ->assertJsonCount(2, 'data');
    }

    /** @test */
    public function agent_can_view_recent_deliveries_with_limit()
    {
        $response = $this->actingAs($this->agent)->getJson('/api/agent/dashboard/recent-deliveries?limit=1');

        $response->assertStatus(200)
            ->assertJsonPath('success', true)
            ->assertJsonCount(1, 'data');
    }

    /** @test */
    public function unauthorized_user_cannot_access_dashboard_summary()
    {
        // Create a regular customer user
        $customer = User::factory()->create();
        $customer->givePermissionTo(Permission::CUSTOMER);

        $response = $this->actingAs($customer)->getJson('/api/agent/dashboard/summary');

        $response->assertStatus(403);
    }

    /** @test */
    public function unauthorized_user_cannot_access_recent_deliveries()
    {
        // Create a regular customer user
        $customer = User::factory()->create();
        $customer->givePermissionTo(Permission::CUSTOMER);

        $response = $this->actingAs($customer)->getJson('/api/agent/dashboard/recent-deliveries');

        $response->assertStatus(403);
    }
}
