<?php

namespace Marvel\Tests\Feature\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Marvel\Database\Models\User;
use Marvel\Database\Models\DeliveryAgentProfile;
use Marvel\Database\Models\DeliveryAgentLocationHistory;
use Marvel\Enums\Permission;
use Marvel\Tests\TestCase;
use Spatie\Permission\Models\Role;

class AgentLocationApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $agent;
    protected $profile;

    protected function setUp(): void
    {
        parent::setUp();

        // Create delivery agent role if it doesn't exist
        $role = Role::firstOrCreate(['name' => 'delivery_agent']);

        // Create a delivery agent user
        $this->agent = User::factory()->create([
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        // Assign delivery agent permission
        $this->agent->givePermissionTo(Permission::DELIVERY_AGENT);
        $this->agent->assignRole($role);

        // Create delivery agent profile
        $this->profile = $this->agent->delivery_agent_profile()->create([
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
            'current_location' => null,
        ]);
    }

    /** @test */
    public function agent_can_update_location()
    {
        $locationData = [
            'lat' => 40.7128,
            'lng' => -74.0060,
            'accuracy' => 5.0,
            'speed' => 25.5,
            'heading' => 180.0,
        ];

        $response = $this->actingAs($this->agent)->postJson('/agent/location', $locationData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'current_location',
                    'last_check_in_at',
                ]
            ])
            ->assertJsonPath('success', true)
            ->assertJsonPath('data.current_location.lat', 40.7128)
            ->assertJsonPath('data.current_location.lng', -74.0060);

        // Verify database was updated
        $this->profile->refresh();
        $this->assertEquals(40.7128, $this->profile->current_location['lat']);
        $this->assertEquals(-74.0060, $this->profile->current_location['lng']);
        $this->assertNotNull($this->profile->last_check_in_at);
    }

    /** @test */
    public function agent_can_get_current_location()
    {
        // Set initial location
        $this->profile->update([
            'current_location' => ['lat' => 40.7128, 'lng' => -74.0060],
            'last_check_in_at' => now(),
        ]);

        $response = $this->actingAs($this->agent)->getJson('/agent/location');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'current_location',
                    'availability_status',
                    'last_check_in_at',
                ]
            ])
            ->assertJsonPath('success', true)
            ->assertJsonPath('data.current_location.lat', 40.7128)
            ->assertJsonPath('data.current_location.lng', -74.0060)
            ->assertJsonPath('data.availability_status', 'ONLINE');
    }

    /** @test */
    public function offline_agent_cannot_update_location()
    {
        // Set agent offline
        $this->profile->update(['availability_status' => 'OFFLINE']);

        $locationData = [
            'lat' => 40.7128,
            'lng' => -74.0060,
        ];

        $response = $this->actingAs($this->agent)->postJson('/agent/location', $locationData);

        $response->assertStatus(400)
            ->assertJsonPath('message', 'Location update not allowed');
    }

    /** @test */
    public function location_update_requires_valid_coordinates()
    {
        $invalidData = [
            'lat' => 91, // Invalid latitude
            'lng' => -74.0060,
        ];

        $response = $this->actingAs($this->agent)->postJson('/agent/location', $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['lat']);

        $invalidData = [
            'lat' => 40.7128,
            'lng' => 181, // Invalid longitude
        ];

        $response = $this->actingAs($this->agent)->postJson('/agent/location', $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['lng']);
    }

    /** @test */
    public function location_update_saves_to_history()
    {
        $locationData = [
            'lat' => 40.7128,
            'lng' => -74.0060,
        ];

        $this->actingAs($this->agent)->postJson('/agent/location', $locationData);

        // Check if location history was created
        $this->assertDatabaseHas('delivery_agent_location_histories', [
            'delivery_agent_user_id' => $this->agent->id,
        ]);

        $history = DeliveryAgentLocationHistory::where('delivery_agent_user_id', $this->agent->id)->first();
        $this->assertEquals(40.7128, $history->location['lat']);
        $this->assertEquals(-74.0060, $history->location['lng']);
    }

    /** @test */
    public function location_update_requires_authentication()
    {
        $locationData = [
            'lat' => 40.7128,
            'lng' => -74.0060,
        ];

        $response = $this->postJson('/agent/location', $locationData);

        $response->assertStatus(401);
    }

    /** @test */
    public function location_update_requires_delivery_agent_permission()
    {
        // Create a regular user without delivery agent permission
        $regularUser = User::factory()->create([
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        $locationData = [
            'lat' => 40.7128,
            'lng' => -74.0060,
        ];

        $response = $this->actingAs($regularUser)->postJson('/agent/location', $locationData);

        $response->assertStatus(403);
    }

    /** @test */
    public function small_location_changes_only_update_timestamp()
    {
        // Set initial location
        $initialLocation = ['lat' => 40.7128, 'lng' => -74.0060];
        $this->profile->update([
            'current_location' => $initialLocation,
            'last_check_in_at' => now()->subMinutes(5),
        ]);

        // Update with very small change (less than 10 meters)
        $locationData = [
            'lat' => 40.71281, // Very small change
            'lng' => -74.00601,
        ];

        $response = $this->actingAs($this->agent)->postJson('/agent/location', $locationData);

        $response->assertStatus(200)
            ->assertJsonPath('message', 'Check-in updated');

        // Verify location didn't change but timestamp did
        $this->profile->refresh();
        $this->assertEquals($initialLocation['lat'], $this->profile->current_location['lat']);
        $this->assertEquals($initialLocation['lng'], $this->profile->current_location['lng']);
    }

    /** @test */
    public function agent_without_profile_gets_error()
    {
        // Create agent without profile
        $agentWithoutProfile = User::factory()->create([
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
        $agentWithoutProfile->givePermissionTo(Permission::DELIVERY_AGENT);

        $locationData = [
            'lat' => 40.7128,
            'lng' => -74.0060,
        ];

        $response = $this->actingAs($agentWithoutProfile)->postJson('/agent/location', $locationData);

        $response->assertStatus(404)
            ->assertJsonPath('message', 'Delivery agent profile not found');
    }
}
