<?php

namespace Marvel\Tests\Feature\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\User;
use Marvel\Enums\Permission;
use Spatie\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class AdminDeliveryApiTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $agent;
    protected $order;
    protected $delivery;

    public function setUp(): void
    {
        parent::setUp();

        // Create permissions
        $this->createPermissions();

        // Create admin
        $this->admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->admin->givePermissionTo(Permission::SUPER_ADMIN);

        // Create delivery agent
        $this->agent = User::create([
            'name' => 'Delivery Agent',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->agent->givePermissionTo(Permission::DELIVERY_AGENT);

        // Create agent profile
        $this->agent->delivery_agent_profile()->create([
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
        ]);

        // Create shop
        $shop = Shop::create([
            'name' => 'Test Shop',
            'slug' => 'test-shop',
            'description' => 'Test shop description',
            'owner_id' => $this->admin->id,
            'is_active' => true,
            'address' => [
                'street_address' => '123 Test St',
                'city' => 'Test City',
                'state' => 'Test State',
                'zip' => '12345',
                'country' => 'Test Country',
            ],
        ]);

        // Create order
        $this->order = Order::create([
            'tracking_number' => 'ORDER-' . rand(10000, 99999),
            'customer_id' => $this->admin->id,
            'shop_id' => $shop->id,
            'order_status' => 'order-processing',
            'amount' => 100.00,
            'payment_status' => 'payment-success',
            'payment_method' => 'CASH',
            'requires_delivery' => true,
            'delivery_id' => null,
        ]);

        // Create delivery
        $this->delivery = Delivery::create([
            'order_id' => $this->order->id,
            'delivery_agent_user_id' => $this->agent->id,
            'status' => 'ASSIGNED',
            'pickup_address' => ['address' => '123 Pickup St'],
            'delivery_address' => ['address' => '456 Delivery St'],
            'delivery_fee' => 15.00,
            'assigned_at' => now(),
            'assigned_by_user_id' => $this->admin->id,
        ]);

        // Update order with delivery ID
        $this->order->update(['delivery_id' => $this->delivery->id]);
    }

    /** @test */
    public function admin_can_list_deliveries()
    {
        $response = $this->actingAs($this->admin)->getJson('/api/admin/deliveries');

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $this->delivery->id)
            ->assertJsonPath('data.0.status', 'ASSIGNED');
    }

    /** @test */
    public function admin_can_view_delivery_details()
    {
        $response = $this->actingAs($this->admin)->getJson("/api/admin/deliveries/{$this->delivery->id}");

        $response->assertStatus(200)
            ->assertJsonPath('id', $this->delivery->id)
            ->assertJsonPath('order_id', $this->order->id)
            ->assertJsonPath('delivery_fee', 15.00);
    }

    /** @test */
    public function admin_can_assign_delivery()
    {
        // Create a new order without delivery
        $newOrder = Order::factory()->create([
            'shop_id' => $this->order->shop_id,
            'requires_delivery' => true,
        ]);

        $response = $this->actingAs($this->admin)->postJson("/api/admin/orders/{$newOrder->id}/assign-delivery", [
            'delivery_agent_user_id' => $this->agent->id,
            'delivery_fee' => 20.00,
            'notes' => 'Please deliver ASAP',
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('delivery.delivery_agent_user_id', $this->agent->id)
            ->assertJsonPath('delivery.delivery_fee', 20.00)
            ->assertJsonPath('delivery.status', 'ASSIGNED');

        $newOrder->refresh();
        $this->assertNotNull($newOrder->delivery_id);
    }

    /** @test */
    public function admin_can_cancel_delivery()
    {
        $response = $this->actingAs($this->admin)->postJson("/api/admin/deliveries/{$this->delivery->id}/cancel", [
            'reason' => 'Customer requested cancellation',
            'notes' => 'Refund processed',
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('delivery.status', 'CANCELLED');

        $this->assertDatabaseHas('deliveries', [
            'id' => $this->delivery->id,
            'status' => 'CANCELLED',
            'cancellation_reason' => 'Customer requested cancellation',
        ]);
    }

    /** @test */
    public function admin_can_list_pending_assignment_orders()
    {
        // Create a new order without delivery
        $newOrder = Order::factory()->create([
            'shop_id' => $this->order->shop_id,
            'requires_delivery' => true,
            'status' => 'processing',
            'payment_status' => 'payment-success',
        ]);

        $response = $this->actingAs($this->admin)->getJson('/api/admin/orders/pending-assignment');

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $newOrder->id);
    }

    /** @test */
    public function admin_can_manage_delivery_agents()
    {
        $response = $this->actingAs($this->admin)->getJson('/api/admin/agents');

        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $this->agent->id);

        $response = $this->actingAs($this->admin)->getJson("/api/admin/agents/{$this->agent->id}");

        $response->assertStatus(200)
            ->assertJsonPath('agent.id', $this->agent->id)
            ->assertJsonPath('profile.kyc_status', 'APPROVED');
    }

    /** @test */
    public function admin_can_approve_and_reject_kyc()
    {
        // Create a new agent with pending KYC
        $newAgent = User::factory()->create();
        $newAgent->givePermissionTo(Permission::DELIVERY_AGENT);

        $newAgent->delivery_agent_profile()->create([
            'availability_status' => 'OFFLINE',
            'kyc_status' => 'SUBMITTED',
        ]);

        // Approve KYC
        $response = $this->actingAs($this->admin)->postJson("/api/admin/agents/{$newAgent->id}/approve-kyc");

        $response->assertStatus(200)
            ->assertJsonPath('profile.kyc_status', 'APPROVED');

        // Create another agent for rejection test
        $anotherAgent = User::factory()->create();
        $anotherAgent->givePermissionTo(Permission::DELIVERY_AGENT);

        $anotherAgent->delivery_agent_profile()->create([
            'availability_status' => 'OFFLINE',
            'kyc_status' => 'SUBMITTED',
        ]);

        // Reject KYC
        $response = $this->actingAs($this->admin)->postJson("/api/admin/agents/{$anotherAgent->id}/reject-kyc", [
            'rejection_reason' => 'Documents unclear',
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('profile.kyc_status', 'REJECTED')
            ->assertJsonPath('profile.kyc_rejection_reason', 'Documents unclear');
    }

    /**
     * Create permissions needed for testing.
     *
     * @return void
     */
    protected function createPermissions()
    {
        // Create roles if they don't exist
        Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'api']);
        Role::firstOrCreate(['name' => 'delivery_agent', 'guard_name' => 'api']);
        Role::firstOrCreate(['name' => 'customer', 'guard_name' => 'api']);
        Role::firstOrCreate(['name' => 'store_owner', 'guard_name' => 'api']);
        Role::firstOrCreate(['name' => 'staff', 'guard_name' => 'api']);

        // Create permissions if they don't exist
        $permissions = [
            Permission::SUPER_ADMIN,
            Permission::STORE_OWNER,
            Permission::STAFF,
            Permission::CUSTOMER,
            Permission::DELIVERY_AGENT,
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }
    }
}
