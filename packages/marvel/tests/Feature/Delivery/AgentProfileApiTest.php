<?php

namespace Marvel\Tests\Feature\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Marvel\Database\Models\User;
use Marvel\Enums\Permission;
use Spatie\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class AgentProfileApiTest extends TestCase
{
    use RefreshDatabase;

    protected $agent;
    protected $admin;

    public function setUp(): void
    {
        parent::setUp();

        // Create permissions
        $this->createPermissions();

        // Create delivery agent without profile (we'll create it in each test)
        $this->agent = new User([
            'name' => 'Delivery Agent',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->agent->save();
        $this->agent->givePermissionTo(Permission::DELIVERY_AGENT);

        // Create admin
        $this->admin = new User([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->admin->save();
        $this->admin->givePermissionTo(Permission::SUPER_ADMIN);
    }

    /** @test */
    public function agent_can_view_profile()
    {
        // Create profile
        $profile = $this->agent->delivery_agent_profile()->create([
            'availability_status' => 'OFFLINE',
            'kyc_status' => 'PENDING',
        ]);

        $response = $this->actingAs($this->agent)->getJson('/api/agent/profile');

        $response->assertStatus(200)
            ->assertJsonPath('id', $profile->id)
            ->assertJsonPath('user_id', $this->agent->id)
            ->assertJsonPath('availability_status', 'OFFLINE')
            ->assertJsonPath('kyc_status', 'PENDING');
    }

    /** @test */
    public function agent_can_update_profile()
    {
        // Create profile
        $profile = $this->agent->delivery_agent_profile()->create([
            'availability_status' => 'OFFLINE',
            'kyc_status' => 'PENDING',
        ]);

        $response = $this->actingAs($this->agent)->putJson('/api/agent/profile', [
            'current_location' => ['lat' => 40.7128, 'lng' => -74.0060],
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('current_location.lat', 40.7128)
            ->assertJsonPath('current_location.lng', -74.0060);
    }

    /** @test */
    public function agent_can_submit_kyc()
    {
        // Create profile
        $profile = $this->agent->delivery_agent_profile()->create([
            'availability_status' => 'OFFLINE',
            'kyc_status' => 'PENDING',
        ]);

        // Create test files
        $idFile = UploadedFile::fake()->image('id.jpg');
        $licenseFile = UploadedFile::fake()->image('license.jpg');
        $addressFile = UploadedFile::fake()->image('address.jpg');

        $response = $this->actingAs($this->agent)->postJson('/api/agent/profile/kyc', [
            'kyc_id' => $idFile,
            'kyc_license' => $licenseFile,
            'kyc_address_proof' => $addressFile,
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('kyc_status', 'SUBMITTED');
    }

    /** @test */
    public function agent_can_toggle_wallet_preference()
    {
        // Create profile
        $profile = $this->agent->delivery_agent_profile()->create([
            'availability_status' => 'OFFLINE',
            'kyc_status' => 'PENDING',
            'use_wallet_for_earnings' => false,
        ]);

        $response = $this->actingAs($this->agent)->postJson('/api/agent/profile/wallet-preference', [
            'use_wallet_for_earnings' => true,
            'wallet_points_conversion_rate' => 12.5,
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('profile.use_wallet_for_earnings', true)
            ->assertJsonPath('profile.wallet_points_conversion_rate', 12.5);
    }

    /** @test */
    public function non_agent_cannot_access_agent_profile()
    {
        $regularUser = new User([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $regularUser->save();
        $regularUser->givePermissionTo(Permission::CUSTOMER);

        $response = $this->actingAs($regularUser)->getJson('/api/agent/profile');

        $response->assertStatus(403);
    }

    /**
     * Create permissions needed for testing.
     *
     * @return void
     */
    protected function createPermissions()
    {
        // Create roles if they don't exist
        $adminRole = Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'api']);
        $agentRole = Role::firstOrCreate(['name' => 'delivery_agent', 'guard_name' => 'api']);
        $customerRole = Role::firstOrCreate(['name' => 'customer', 'guard_name' => 'api']);

        // Create permissions if they don't exist
        $permissions = [
            Permission::SUPER_ADMIN,
            Permission::STORE_OWNER,
            Permission::STAFF,
            Permission::CUSTOMER,
            Permission::DELIVERY_AGENT,
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }

        // Assign permissions to roles if not already assigned
        if (!$adminRole->hasPermissionTo(Permission::SUPER_ADMIN)) {
            $adminRole->givePermissionTo(Permission::SUPER_ADMIN);
        }

        if (!$agentRole->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            $agentRole->givePermissionTo(Permission::DELIVERY_AGENT);
        }

        if (!$customerRole->hasPermissionTo(Permission::CUSTOMER)) {
            $customerRole->givePermissionTo(Permission::CUSTOMER);
        }
    }
}
