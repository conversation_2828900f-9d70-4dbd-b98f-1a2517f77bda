<?php

namespace Tests\Feature\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\User;
use Marvel\Enums\Permission;
use Spatie\Permission\Models\Permission as PermissionModel;
use Tests\TestCase;

class FindNearestAgentsFeatureTest extends TestCase
{
    use RefreshDatabase;

    // We no longer need a default authenticated user property.
    // protected User $admin;

    public function setUp(): void
    {
        parent::setUp();
        // The setUp method is now only for creating non-user-specific data, like permissions.
        $this->createPermissions();
    }

    /**
     * @test
     * @description A Super Admin can find agents for any order.
     */
    public function super_admin_can_find_agents_for_single_child_order(): void
    {
        // ARRANGE: Create a Super Admin and the necessary order data.
        $superAdmin = User::factory()->create()->givePermissionTo(Permission::SUPER_ADMIN);
        $parentOrder = $this->createOrderForShop($superAdmin, null);
        $shop = $this->createShopWithLocation($superAdmin, ['lat' => 4.04, 'lng' => 9.71]);
        $this->createOrderForShop($superAdmin, $shop, $parentOrder->id);
        $agent = $this->createDeliveryAgentWithLocation(['lat' => 4.07, 'lng' => 9.79]);

        // IMPORTANT: Load children.shop relationship for parent order as the repository expects it.
        $parentOrder->load('children.shop');

        // ACT: Authenticate as the Super Admin and hit the endpoint.
        Sanctum::actingAs($superAdmin);
        $response = $this->getJson(route('orders.nearestAgents', ['order' => $parentOrder->id]));

        // ASSERT: The request is successful.
        $response->assertStatus(200)
            ->assertJsonPath('meta.should_consolidate', false)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $agent->id); // Ensure the correct agent is returned
    }

    /**
     * @test
     * @description A Store Owner can find agents for orders from THEIR OWN shop.
     */
    public function store_owner_can_find_agents_for_their_own_order(): void
    {
        // ARRANGE: Create a Store Owner and an order for their shop.
        $storeOwner = User::factory()->create()->givePermissionTo(Permission::STORE_OWNER);
        $theirShop = $this->createShopWithLocation($storeOwner, ['lat' => 4.04, 'lng' => 9.71]);
        $order = new Order([
            'tracking_number' => 'ORDER-' . rand(10000, 99999),
            'customer_id' => $storeOwner->id,
            'shop_id' => $theirShop->id,
            'requires_delivery' => true, // Ensure delivery is required for this order
            'order_status' => 'order-processing',
            'payment_status' => 'payment-success',
            'amount' => 100.00,
            'customer_contact' => '1234567890',
            'delivery_id' => null,
        ]);
        $order->save();
        $agent = $this->createDeliveryAgentWithLocation(['lat' => 4.045, 'lng' => 9.715]); // Create an agent near the shop

        // ACT: Authenticate as the Store Owner and hit the endpoint.
        Sanctum::actingAs($storeOwner);
        $response = $this->getJson(route('orders.nearestAgents', ['order' => $order->id]));
        dump($response->json());
        // ASSERT: The request is successful and returns the agent.
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $agent->id);
    }

    /**
     * @test
     * @description A Store Owner CANNOT find agents for orders from another shop.
     */
    public function store_owner_cannot_find_agents_for_another_shops_order(): void
    {
        // ARRANGE: Create two owners, their respective shops, and an order for the first owner's shop.
        // ARRANGE: Create a store owner, their shop, and an order from a completely different shop.
        $storeOwner = User::factory()->create();
        $ownerShop = $this->createShopWithLocation($storeOwner, ['lat' => 4.04, 'lng' => 9.71]);
        $storeOwner->shop_id = $ownerShop->id;
        $storeOwner->save();
        $storeOwner->givePermissionTo(Permission::STORE_OWNER);

        // Create a completely separate shop and order
        $anotherShopOwner = User::factory()->create()->givePermissionTo(Permission::CUSTOMER); // A different user, explicitly a customer
        $anotherShop = $this->createShopWithLocation($anotherShopOwner, ['lat' => 5.0, 'lng' => 10.0]);
        $anotherOrder = $this->createOrderForShop($anotherShopOwner, $anotherShop); // Order for another shop
        $anotherOrder->refresh(); // Ensure relationships are fresh before the request
        $anotherOrder->load('shop'); // Explicitly load the shop relationship

        // ASSERT ARRANGE: Confirm the setup is as expected before the request
        $this->assertNotNull($storeOwner->id);
        $this->assertNotNull($anotherShopOwner->id);
        $this->assertNotEquals($storeOwner->id, $anotherShopOwner->id, 'Store owner and another shop owner should be different users.');
        $this->assertNotNull($ownerShop->id);
        $this->assertNotNull($anotherShop->id);
        $this->assertNotEquals($ownerShop->id, $anotherShop->id, 'Owner shop and another shop should be different.');
        $this->assertEquals($anotherShop->id, $anotherOrder->shop_id, 'Order should belong to another shop.');
        $this->assertEquals($anotherShopOwner->id, $anotherShop->owner_id, 'Another shop should belong to another shop owner.');
        $this->assertNotNull($anotherOrder->shop, 'Order should have a shop relationship loaded.');
        $this->assertNotEquals($storeOwner->id, $anotherOrder->shop->owner_id, 'Authenticated store owner should NOT own the order\'s shop.');


        // ACT: Authenticate as the original store owner and try to access the order from another shop.
        Sanctum::actingAs($storeOwner);
        $response = $this->getJson(route('orders.nearestAgents', ['order' => $anotherOrder->id]));

        // ASSERT: The request should be forbidden.
        // NOTE: This depends on your controller/policy logic. It might be 403 or 404.
        // Let's assume you have a policy that returns 403.
        $response->assertStatus(403);
    }

    /**
     * @test
     * @description A Customer CANNOT access this admin-level endpoint.
     */
    public function customer_cannot_find_nearest_agents(): void
    {
        // ARRANGE: Create a customer and an order.
        $customer = User::factory()->create()->givePermissionTo(Permission::CUSTOMER);
        $order = new Order([
            'tracking_number' => 'ORDER-' . rand(10000, 99999),
            'customer_id' => $customer->id,
            'shop_id' => null, // Assuming customer orders might not have a shop_id initially
            'order_status' => 'order-processing',
            'payment_status' => 'payment-success',
            'amount' => 100.00,
            'customer_contact' => '1234567890',
            'requires_delivery' => true,
            'delivery_id' => null,
        ]);
        $order->save();

        // ACT: Authenticate as the customer.
        Sanctum::actingAs($customer);
        $response = $this->getJson(route('orders.nearestAgents', ['order' => $order->id]));

        // ASSERT: The request should be forbidden.
        $response->assertStatus(403);
    }


    // --- HELPER METHODS (Updated to accept the user for ownership) ---

    protected function createPermissions(): void
    {
        $permissions = [Permission::SUPER_ADMIN, Permission::STORE_OWNER, Permission::CUSTOMER, Permission::DELIVERY_AGENT];
        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }
    }

    protected function createShopWithLocation(User $owner, array $location): Shop
    {
        return Shop::factory()->create([
            'owner_id' => $owner->id,
            'settings' => ['location' => $location]
        ]);
    }

    protected function createOrderForShop(User $customer, ?Shop $shop = null, ?int $parentId = null): Order
    {
        $order = new Order([
            'tracking_number' => 'ORDER-' . rand(10000, 99999),
            'customer_id' => $customer->id,
            'shop_id' => $shop ? $shop->id : null,
            'parent_id' => $parentId,
            'requires_delivery' => true,
            'order_status' => 'order-processing',
            'payment_status' => 'payment-success',
            'amount' => 100.00,
            'customer_contact' => '1234567890',
            'delivery_id' => null,
        ]);
        $order->save();
        return $order;
    }

    protected function createDeliveryAgentWithLocation(array $location): User
    {
        $agent = User::factory()->create();
        $agent->givePermissionTo(Permission::DELIVERY_AGENT);
        $agent->delivery_agent_profile()->create([
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
            'current_location' => $location,
        ]);
        return $agent;
    }
}