<?php

namespace Marvel\Tests\Feature\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Marvel\Database\Models\DeliveryAgentEarning;
use Marvel\Database\Models\DeliveryAgentTransaction;
use Marvel\Database\Models\DeliveryAgentWithdrawal;
use Marvel\Database\Models\User;
use Marvel\Database\Models\Wallet;
use Marvel\Enums\Permission;
use Tests\TestCase;

class AgentEarningsApiTest extends TestCase
{
    use RefreshDatabase;

    protected $agent;

    public function setUp(): void
    {
        parent::setUp();

        // Create delivery agent
        $this->agent = User::factory()->create();
        $this->agent->givePermissionTo(Permission::DELIVERY_AGENT);

        // Create agent profile
        $this->agent->delivery_agent_profile()->create([
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
        ]);

        // Create earnings record
        DeliveryAgentEarning::create([
            'delivery_agent_user_id' => $this->agent->id,
            'total_earnings' => 500.00,
            'withdrawn_amount' => 200.00,
            'current_balance' => 300.00,
            'pending_withdrawal_amount' => 0.00,
            'payment_info' => [
                'method' => 'MOBILE_MONEY',
                'provider' => 'ORANGE_MONEY',
                'details' => [
                    'phone_number' => '**********',
                    'account_name' => 'Test Agent',
                ],
            ],
        ]);
    }

    /** @test */
    public function agent_can_view_balance()
    {
        $response = $this->actingAs($this->agent)->getJson('/api/agent/earnings/balance');

        $response->assertStatus(200)
            ->assertJsonPath('total_earnings', 500.00)
            ->assertJsonPath('current_balance', 300.00)
            ->assertJsonPath('withdrawn_amount', 200.00);
    }

    /** @test */
    public function agent_can_view_payment_info()
    {
        $response = $this->actingAs($this->agent)->getJson('/api/agent/payment-info');

        $response->assertStatus(200)
            ->assertJsonPath('payment_info.method', 'MOBILE_MONEY')
            ->assertJsonPath('payment_info.provider', 'ORANGE_MONEY')
            ->assertJsonPath('payment_info.details.phone_number', '**********');
    }

    /** @test */
    public function agent_can_update_payment_info()
    {
        $response = $this->actingAs($this->agent)->putJson('/api/agent/payment-info', [
            'payment_info' => [
                'method' => 'BANK_TRANSFER',
                'provider' => 'STANDARD_BANK',
                'details' => [
                    'account_number' => '**********',
                    'account_name' => 'Test Agent',
                    'branch_code' => '12345',
                ],
            ],
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('payment_info.method', 'BANK_TRANSFER')
            ->assertJsonPath('payment_info.provider', 'STANDARD_BANK')
            ->assertJsonPath('payment_info.details.account_number', '**********');
    }

    /** @test */
    public function agent_can_request_withdrawal()
    {
        $response = $this->actingAs($this->agent)->postJson('/api/agent/withdrawals', [
            'amount' => 100.00,
            'notes' => 'Withdrawal request',
        ]);

        $response->assertStatus(200)
            ->assertJsonPath('withdrawal.amount', 100.00)
            ->assertJsonPath('withdrawal.status', 'PENDING');

        $this->assertDatabaseHas('delivery_agent_withdrawals', [
            'delivery_agent_user_id' => $this->agent->id,
            'amount' => 100.00,
            'status' => 'PENDING',
        ]);

        // Check if earnings were updated
        $earnings = DeliveryAgentEarning::where('delivery_agent_user_id', $this->agent->id)->first();
        $this->assertEquals(200.00, $earnings->current_balance); // 300 - 100
        $this->assertEquals(100.00, $earnings->pending_withdrawal_amount);

        // Check if transaction was created
        $this->assertDatabaseHas('delivery_agent_transactions', [
            'delivery_agent_user_id' => $this->agent->id,
            'transaction_type' => DeliveryAgentTransaction::TYPE_WITHDRAWAL,
            'amount' => -100.00, // Negative amount for withdrawals
            'status' => DeliveryAgentTransaction::STATUS_PENDING,
        ]);
    }

    /** @test */
    public function agent_cannot_withdraw_more_than_balance()
    {
        $response = $this->actingAs($this->agent)->postJson('/api/agent/withdrawals', [
            'amount' => 500.00, // More than current balance of 300
            'notes' => 'Withdrawal request',
        ]);

        $response->assertStatus(400);

        // Check if earnings were not updated
        $earnings = DeliveryAgentEarning::where('delivery_agent_user_id', $this->agent->id)->first();
        $this->assertEquals(300.00, $earnings->current_balance);
        $this->assertEquals(0.00, $earnings->pending_withdrawal_amount);
    }

    /** @test */
    public function agent_can_view_transactions()
    {
        // Create some transactions
        DeliveryAgentTransaction::create([
            'delivery_agent_user_id' => $this->agent->id,
            'transaction_type' => DeliveryAgentTransaction::TYPE_EARNING,
            'reference_type' => DeliveryAgentTransaction::REF_DELIVERY,
            'reference_id' => 1,
            'amount' => 50.00,
            'status' => DeliveryAgentTransaction::STATUS_COMPLETED,
            'notes' => 'Earnings from delivery #1',
            'processed_at' => now()->subDays(5),
        ]);

        DeliveryAgentTransaction::create([
            'delivery_agent_user_id' => $this->agent->id,
            'transaction_type' => DeliveryAgentTransaction::TYPE_WITHDRAWAL,
            'reference_type' => DeliveryAgentTransaction::REF_WITHDRAWAL,
            'reference_id' => 1,
            'amount' => -30.00,
            'status' => DeliveryAgentTransaction::STATUS_COMPLETED,
            'notes' => 'Withdrawal',
            'processed_at' => now()->subDays(2),
        ]);

        $response = $this->actingAs($this->agent)->getJson('/api/agent/transactions');

        $response->assertStatus(200)
            ->assertJsonCount(2, 'data')
            ->assertJsonPath('data.0.transaction_type', DeliveryAgentTransaction::TYPE_WITHDRAWAL)
            ->assertJsonPath('data.0.amount', -30.00)
            ->assertJsonPath('data.1.transaction_type', DeliveryAgentTransaction::TYPE_EARNING)
            ->assertJsonPath('data.1.amount', 50.00);
    }

    /** @test */
    public function agent_can_view_wallet()
    {
        // Create wallet
        $wallet = Wallet::create([
            'customer_id' => $this->agent->id,
            'total_points' => 1000,
            'points_used' => 200,
            'available_points' => 800,
        ]);

        // Set wallet preference
        $this->agent->delivery_agent_profile->update([
            'use_wallet_for_earnings' => true,
            'wallet_points_conversion_rate' => 10,
        ]);

        $response = $this->actingAs($this->agent)->getJson('/api/agent/wallet');

        $response->assertStatus(200)
            ->assertJsonPath('wallet.total_points', 1000)
            ->assertJsonPath('wallet.available_points', 800)
            ->assertJsonPath('conversion_rate', 10)
            ->assertJsonPath('currency_amount', 80); // 800 points / 10 = 80 currency
    }
}
