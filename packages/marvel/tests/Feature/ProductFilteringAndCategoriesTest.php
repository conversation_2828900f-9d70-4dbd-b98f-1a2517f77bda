<?php

namespace Marvel\Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Marvel\Database\Models\Category;
use Marvel\Database\Models\Product;
use Marvel\Database\Models\Manufacturer;
use Marvel\Database\Models\Variation;
use Tests\TestCase;

class ProductFilteringAndCategoriesTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test product filtering by category
     *
     * @return void
     */
    public function testProductFilteringByCategory()
    {
        // Create categories
        $category1 = Category::factory()->create();
        $category2 = Category::factory()->create();
        
        // Create products and associate them with categories
        $productsForCategory1 = Product::factory()->count(5)->create();
        $productsForCategory2 = Product::factory()->count(3)->create();
        
        foreach ($productsForCategory1 as $product) {
            $product->categories()->attach($category1);
        }
        
        foreach ($productsForCategory2 as $product) {
            $product->categories()->attach($category2);
        }
        
        // Test filtering by category
        $response = $this->getJson("/filter-products?category={$category1->id}");
        
        $response->assertStatus(200);
        $response->assertJsonCount(5, 'data');
    }
    
    /**
     * Test product filtering by price range
     *
     * @return void
     */
    public function testProductFilteringByPriceRange()
    {
        // Create products with different prices
        Product::factory()->count(3)->create(['price' => 10]);
        Product::factory()->count(2)->create(['price' => 50]);
        Product::factory()->count(4)->create(['price' => 100]);
        
        // Test filtering by min price
        $response = $this->getJson("/filter-products?min_price=50");
        
        $response->assertStatus(200);
        $response->assertJsonCount(6, 'data'); // 2 products with price 50 + 4 products with price 100
        
        // Test filtering by max price
        $response = $this->getJson("/filter-products?max_price=50");
        
        $response->assertStatus(200);
        $response->assertJsonCount(5, 'data'); // 3 products with price 10 + 2 products with price 50
        
        // Test filtering by price range
        $response = $this->getJson("/filter-products?min_price=50&max_price=50");
        
        $response->assertStatus(200);
        $response->assertJsonCount(2, 'data'); // Only products with price 50
    }
    
    /**
     * Test product filtering by brand
     *
     * @return void
     */
    public function testProductFilteringByBrand()
    {
        // Create manufacturers (brands)
        $brand1 = Manufacturer::factory()->create();
        $brand2 = Manufacturer::factory()->create();
        
        // Create products with different brands
        Product::factory()->count(4)->create(['manufacturer_id' => $brand1->id]);
        Product::factory()->count(2)->create(['manufacturer_id' => $brand2->id]);
        
        // Test filtering by brand
        $response = $this->getJson("/filter-products?brand={$brand1->id}");
        
        $response->assertStatus(200);
        $response->assertJsonCount(4, 'data');
    }
    
    /**
     * Test product sorting
     *
     * @return void
     */
    public function testProductSorting()
    {
        // Create products with different prices and creation dates
        Product::factory()->create(['price' => 100, 'created_at' => now()->subDays(5)]);
        Product::factory()->create(['price' => 50, 'created_at' => now()->subDays(3)]);
        Product::factory()->create(['price' => 75, 'created_at' => now()->subDays(1)]);
        
        // Test sorting by price (low to high)
        $response = $this->getJson("/filter-products?sort_by=price_low_high");
        
        $response->assertStatus(200);
        $this->assertEquals(50, $response->json('data.0.price'));
        $this->assertEquals(75, $response->json('data.1.price'));
        $this->assertEquals(100, $response->json('data.2.price'));
        
        // Test sorting by price (high to low)
        $response = $this->getJson("/filter-products?sort_by=price_high_low");
        
        $response->assertStatus(200);
        $this->assertEquals(100, $response->json('data.0.price'));
        $this->assertEquals(75, $response->json('data.1.price'));
        $this->assertEquals(50, $response->json('data.2.price'));
        
        // Test sorting by newest
        $response = $this->getJson("/filter-products?sort_by=newest");
        
        $response->assertStatus(200);
        // The newest product should be first
        $this->assertEquals(75, $response->json('data.0.price')); // Created 1 day ago
    }
    
    /**
     * Test parent categories endpoint
     *
     * @return void
     */
    public function testParentCategoriesEndpoint()
    {
        // Create parent categories
        Category::factory()->count(5)->create(['parent' => null]);
        
        // Create child categories
        $parentCategory = Category::first();
        Category::factory()->count(3)->create(['parent' => $parentCategory->id]);
        
        // Test parent categories endpoint
        $response = $this->getJson("/parent-categories");
        
        $response->assertStatus(200);
        $response->assertJsonCount(5, 'data');
    }
    
    /**
     * Test child categories endpoint
     *
     * @return void
     */
    public function testChildCategoriesEndpoint()
    {
        // Create parent category
        $parentCategory = Category::factory()->create(['parent' => null]);
        
        // Create child categories
        Category::factory()->count(4)->create(['parent' => $parentCategory->id]);
        
        // Create another parent category with different children
        $anotherParentCategory = Category::factory()->create(['parent' => null]);
        Category::factory()->count(2)->create(['parent' => $anotherParentCategory->id]);
        
        // Test child categories endpoint
        $response = $this->getJson("/child-categories/{$parentCategory->id}");
        
        $response->assertStatus(200);
        $response->assertJsonCount(4, 'data');
    }
}
