<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Marvel\Database\Models\User;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\Product;
use Marvel\Enums\Permission;
use Spatie\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Config;

class ProductLocationFilterTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected User $superAdmin;
    protected User $customer;

    public function setUp(): void
    {
        parent::setUp();
        $this->createPermissions();

        $this->superAdmin = new User([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->superAdmin->save();
        $this->superAdmin->givePermissionTo(Permission::SUPER_ADMIN);
        $token = $this->superAdmin->createToken('test-token')->plainTextToken;
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ]);

        $this->customer = new User([
            'name' => 'Customer',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->customer->save();
        $this->customer->givePermissionTo(Permission::CUSTOMER);
    }

    /**
     * Create permissions needed for testing.
     *
     * @return void
     */
    protected function createPermissions()
    {
        Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'api']);
        Role::firstOrCreate(['name' => 'customer', 'guard_name' => 'api']);

        $permissions = [
            Permission::SUPER_ADMIN,
            Permission::CUSTOMER,
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }
    }

    /**
     * Test that the products endpoint returns all products without coordinates.
     *
     * @return void
     */
    public function test_products_endpoint_returns_all_products_without_coordinates()
    {
        // Create shops and products
        $shop1 = Shop::factory()->create([
            'name' => 'Shop A',
            'settings' => [
                'location' => [
                    'lat' => 0.0,
                    'lng' => 0.0
                ]
            ]
        ]);
        $shop2 = Shop::factory()->create([
            'name' => 'Shop B',
            'settings' => [
                'location' => [
                    'lat' => 10.0,
                    'lng' => 10.0
                ]
            ]
        ]);

        Product::factory()->count(3)->create(['shop_id' => $shop1->id]);
        Product::factory()->count(2)->create(['shop_id' => $shop2->id]);

        // Authenticate as super admin
        $this->actingAs($this->superAdmin);

        // Make HTTP request to /products endpoint without coordinates
        $response = $this->get('/products');

        // Assert response
        $response->assertStatus(200);
        $response->assertJsonCount(5, 'data'); // Expecting all 5 products
    }

    /**
     * Test that the products endpoint filters by default radius with coordinates only.
     *
     * @return void
     */
    public function test_products_endpoint_filters_by_default_radius_with_coordinates_only()
    {
        // Set a mock default radius for testing
        Config::set('delivery.auto_assignment_radius', 5); // 5km

        // Create shops and products
        $shopWithinRadius = Shop::factory()->create([
            'name' => 'Shop Within',
            'settings' => [
                'location' => [
                    'lat' => 0.001,
                    'lng' => 0.001
                ]
            ]
        ]);
        $shopOutsideRadius = Shop::factory()->create([
            'name' => 'Shop Outside',
            'settings' => [
                'location' => [
                    'lat' => 0.1,
                    'lng' => 0.1
                ]
            ]
        ]);

        $productWithin = Product::factory()->create(['shop_id' => $shopWithinRadius->id]);
        $productOutside = Product::factory()->create(['shop_id' => $shopOutsideRadius->id]);

        // Authenticate as super admin
        $this->actingAs($this->superAdmin);

        // Make HTTP request to /products endpoint with coordinates only
        $response = $this->get('/products?coordinates=0.0,0.0');

        // Assert response
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $response->assertJsonFragment(['id' => $productWithin->id]);
        $response->assertJsonMissing(['id' => $productOutside->id]);
    }

    /**
     * Test that the products endpoint filters by custom radius with coordinates and radius.
     *
     * @return void
     */
    public function test_products_endpoint_filters_by_custom_radius_with_coordinates_and_radius()
    {
        // Create shops and products
        $shopWithinCustomRadius = Shop::factory()->create([
            'name' => 'Shop Within Custom',
            'settings' => [
                'location' => [
                    'lat' => 0.0005,
                    'lng' => 0.0005
                ]
            ]
        ]);
        $shopOutsideCustomRadius = Shop::factory()->create([
            'name' => 'Shop Outside Custom',
            'settings' => [
                'location' => [
                    'lat' => 0.005,
                    'lng' => 0.005
                ]
            ]
        ]);

        $productWithinCustom = Product::factory()->create(['shop_id' => $shopWithinCustomRadius->id]);
        $productOutsideCustom = Product::factory()->create(['shop_id' => $shopOutsideCustomRadius->id]);

        // Authenticate as super admin
        $this->actingAs($this->superAdmin);

        // Make HTTP request to /products endpoint with coordinates and custom radius
        $response = $this->get('/products?coordinates=0.0,0.0&radius=0.1'); // 0.1 km

        // Assert response
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $response->assertJsonFragment(['id' => $productWithinCustom->id]);
        $response->assertJsonMissing(['id' => $productOutsideCustom->id]);
    }

    /**
     * Test that the filter products endpoint returns all products without coordinates.
     *
     * @return void
     */
    public function test_filter_products_endpoint_returns_all_products_without_coordinates()
    {
        // Create shops and products
        $shop1 = Shop::factory()->create([
            'name' => 'Shop C',
            'settings' => [
                'location' => [
                    'lat' => 0.0,
                    'lng' => 0.0
                ]
            ]
        ]);
        $shop2 = Shop::factory()->create([
            'name' => 'Shop D',
            'settings' => [
                'location' => [
                    'lat' => 10.0,
                    'lng' => 10.0
                ]
            ]
        ]);

        Product::factory()->count(3)->create(['shop_id' => $shop1->id]);
        Product::factory()->count(2)->create(['shop_id' => $shop2->id]);

        // Authenticate as super admin
        $this->actingAs($this->superAdmin);

        // Make HTTP request to /filter-products endpoint without coordinates
        $response = $this->get('/filter-products');

        // Assert response
        $response->assertStatus(200);
        $response->assertJsonCount(5, 'data'); // Expecting all 5 products
    }

    /**
     * Test that the filter products endpoint filters by default radius with coordinates only.
     *
     * @return void
     */
    public function test_filter_products_endpoint_filters_by_default_radius_with_coordinates_only()
    {
        // Set a mock default radius for testing
        Config::set('delivery.auto_assignment_radius', 5); // 5km

        // Create shops and products
        $shopWithinRadius = Shop::factory()->create([
            'name' => 'Shop Within Filter',
            'settings' => [
                'location' => [
                    'lat' => 0.001,
                    'lng' => 0.001
                ]
            ]
        ]);
        $shopOutsideRadius = Shop::factory()->create([
            'name' => 'Shop Outside Filter',
            'settings' => [
                'location' => [
                    'lat' => 0.1,
                    'lng' => 0.1
                ]
            ]
        ]);

        $productWithin = Product::factory()->create(['shop_id' => $shopWithinRadius->id]);
        $productOutside = Product::factory()->create(['shop_id' => $shopOutsideRadius->id]);

        // Authenticate as super admin
        $this->actingAs($this->superAdmin);

        // Make HTTP request to /filter-products endpoint with coordinates only
        $response = $this->get('/filter-products?coordinates=0.0,0.0');

        // Assert response
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $response->assertJsonFragment(['id' => $productWithin->id]);
        $response->assertJsonMissing(['id' => $productOutside->id]);
    }

    /**
     * Test that the filter products endpoint filters by custom radius with coordinates and radius.
     *
     * @return void
     */
    public function test_filter_products_endpoint_filters_by_custom_radius_with_coordinates_and_radius()
    {
        // Create shops and products
        $shopWithinCustomRadius = Shop::factory()->create([
            'name' => 'Shop Within Custom Filter',
            'settings' => [
                'location' => [
                    'lat' => 0.0005,
                    'lng' => 0.0005
                ]
            ]
        ]);
        $shopOutsideCustomRadius = Shop::factory()->create([
            'name' => 'Shop Outside Custom Filter',
            'settings' => [
                'location' => [
                    'lat' => 0.005,
                    'lng' => 0.005
                ]
            ]
        ]);

        $productWithinCustom = Product::factory()->create(['shop_id' => $shopWithinCustomRadius->id]);
        $productOutsideCustom = Product::factory()->create(['shop_id' => $shopOutsideCustomRadius->id]);

        // Authenticate as super admin
        $this->actingAs($this->superAdmin);

        // Make HTTP request to /filter-products endpoint with coordinates and custom radius
        $response = $this->get('/filter-products?coordinates=0.0,0.0&radius=0.1'); // 0.1 km

        // Assert response
        $response->assertStatus(200);
        $response->assertJsonCount(1, 'data');
        $response->assertJsonFragment(['id' => $productWithinCustom->id]);
        $response->assertJsonMissing(['id' => $productOutsideCustom->id]);
    }
}