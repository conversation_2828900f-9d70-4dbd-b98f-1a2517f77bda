<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Mail;
use Marvel\Mail\ContactAdmin;
use Tests\TestCase;
use Marvel\Database\Models\User;
use Marvel\Enums\Permission;
use Spatie\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role;
use Symfony\Component\HttpFoundation\Response;

class UserControllerContactAdminTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $superAdmin;
    protected User $customer;

    public function setUp(): void
    {
        parent::setUp();
        $this->createPermissions();

        $this->superAdmin = new User([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->superAdmin->save();
        $this->superAdmin->givePermissionTo(Permission::SUPER_ADMIN);
        $token = $this->superAdmin->createToken('test-token')->plainTextToken;
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ]);

        $this->customer = new User([
            'name' => 'Customer',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->customer->save();
        $this->customer->givePermissionTo(Permission::CUSTOMER);
    }

    /**
     * Create permissions needed for testing.
     *
     * @return void
     */
    protected function createPermissions()
    {
        \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'api']);
        \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'customer', 'guard_name' => 'api']);

        $permissions = [
            Permission::SUPER_ADMIN,
            Permission::CUSTOMER,
        ];

        foreach ($permissions as $permission) {
            \Spatie\Permission\Models\Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }
    }

    public function test_contact_admin_sends_email_successfully()
    {
        // Arrange
        Mail::fake();

        $admin = User::factory()->create();
        $admin->givePermissionTo(Permission::SUPER_ADMIN);

        $data = [
            'subject' => $this->faker->sentence,
            'name' => $this->faker->name,
            'email' => $this->faker->email,
            'description' => $this->faker->paragraph,
        ];

        // Act
        $response = $this->postJson('/contact-us', $data);

        // Assert
        $response->assertStatus(Response::HTTP_OK);
        $response->assertJson(['message' => 'CHAWKBAZAR_MESSAGE.EMAIL_SENT_SUCCESSFUL', 'success' => true]);

        Mail::assertSent(ContactAdmin::class, function ($mail) use ($data, $admin) {
            return $mail->hasTo($admin->email) &&
                   $mail->details['subject'] === $data['subject'] &&
                   $mail->details['name'] === $data['name'] &&
                   $mail->details['email'] === $data['email'] &&
                   $mail->details['description'] === $data['description'];
        });
    }

    public function test_contact_admin_throws_internal_server_error_on_email_failure()
    {
        // Arrange
        Mail::fake();
        Mail::shouldReceive('to')
            ->andThrow(new \Exception('Failed to send email'));

        $data = [
            'subject' => $this->faker->sentence,
            'name' => $this->faker->name,
            'email' => $this->faker->email,
            'description' => $this->faker->paragraph,
        ];

        // Act
        $response = $this->postJson('/contact-us', $data);

        // Assert
        $response->assertStatus(Response::HTTP_INTERNAL_SERVER_ERROR);
        $response->assertJson(['message' => 'Something went wrong', 'success' => false]);
    }
}
