<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Marvel\Database\Models\Settings;
use Tests\TestCase;
use Marvel\Database\Models\User;
use Marvel\Database\Models\Product;
use Marvel\Database\Models\Shop;
use Marvel\Enums\Permission;
use Marvel\Enums\ProductType;
use Spatie\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role;

class NegotiatedPricingTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected User $superAdmin;
    protected User $storeOwner;
    protected User $customer;
    protected Shop $shop;
    protected Product $simpleProduct;
    protected Product $variableProduct;

    public function setUp(): void
    {
        parent::setUp();
        $this->createPermissions(); // Ensure permissions are created
        $this->superAdmin = new User([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->superAdmin->save();
        $this->superAdmin->givePermissionTo(Permission::SUPER_ADMIN);

        $this->storeOwner = new User([
            'name' => 'Store Owner',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->storeOwner->save();
        $this->storeOwner->givePermissionTo(Permission::STORE_OWNER);
        $token = $this->storeOwner->createToken('test-token')->plainTextToken;
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ]);

        $this->customer = new User([
            'name' => 'Customer',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->customer->save();
        $this->customer->givePermissionTo(Permission::CUSTOMER);
        $this->shop = Shop::factory()->create();
        $this->simpleProduct = Product::factory()->create([
            'shop_id' => $this->shop->id,
            'product_type' => ProductType::SIMPLE,
            'price' => 100,
            'sale_price' => 80,
        ]);
        $this->variableProduct = Product::factory()->create([
            'shop_id' => $this->shop->id,
            'product_type' => ProductType::VARIABLE,
            'price' => 200,
            'sale_price' => 150,
        ]);
        // $this->variableProduct->variation_options()->createMany(
        //     \Marvel\Database\Factories\VariationFactory::new()->count(1)->make([
        //         'product_id' => $this->variableProduct->id,
        //         'price' => 200,
        //         'sale_price' => 150,
        //     ])->toArray()
        // );

        // Ensure app_settings.trust is true for testing purposes
        Settings::updateOrCreate(
            ['id' => 1], // Assuming a single settings record with ID 1
            [
                'options' => [
                    'app_settings' => [
                        'trust' => true,
                    ],
                ],
            ]
        );
    }

    /**
     * Create permissions needed for testing.
     *
     * @return void
     */
    protected function createPermissions()
    {
        // Create roles if they don't exist
        Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'api']);
        Role::firstOrCreate(['name' => 'store_owner', 'guard_name' => 'api']);
        Role::firstOrCreate(['name' => 'customer', 'guard_name' => 'api']);

        // Create permissions if they don't exist
        $permissions = [
            Permission::SUPER_ADMIN,
            Permission::STORE_OWNER,
            Permission::CUSTOMER,
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }
    }

    /**
     * Test that a STORE_OWNER can access the /users endpoint.
     *
     * @return void
     */
    public function test_store_owner_can_access_users_endpoint()
    {
        $response = $this->get('/users');
        $response->assertStatus(200);
    }

    /**
     * Test that a STORE_OWNER can create an order with manual_total_override (positive discount).
     *
     * @return void
     */
    public function test_store_owner_can_create_order_with_positive_manual_total_override()
    {
        $products = [
            [
                'product_id' => $this->simpleProduct->id,
                'order_quantity' => 2,
                'unit_price' => $this->simpleProduct->sale_price,
                'subtotal' => $this->simpleProduct->sale_price * 2,
            ],
        ];

        $initialCalculatedAmount = $this->simpleProduct->sale_price * 2; // 160
        $salesTax = 10;
        $deliveryFee = 5;
        $initialTotal = $initialCalculatedAmount + $salesTax + $deliveryFee; // 175

        $manualTotalOverride = 150; // Negotiated price, lower than initial total

        $response = $this->post('/orders', [
            'customer_id' => $this->customer->id,
            'shop_id' => $this->shop->id, // Added shop_id
            'products' => $products,
            'amount' => $initialCalculatedAmount,
            'sales_tax' => $salesTax,
            'delivery_fee' => $deliveryFee,
            'paid_total' => $initialTotal, // This will be overridden
            'total' => $initialTotal, // This will be overridden
            'manual_total_override' => $manualTotalOverride,
            'customer_contact' => $this->faker->e164PhoneNumber(), // Use faker for contact
            'payment_gateway' => 'CASH_ON_DELIVERY',
            'shipping_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
            'billing_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
        ]);

        $response->dump();
        
        $response->assertStatus(201);
        $response->assertJson([
            'paid_total' => $manualTotalOverride,
            'total' => $manualTotalOverride,
        ]);

        $order = $response->json();
        $this->assertDatabaseHas('orders', [
            'id' => $order['id'],
            'paid_total' => $manualTotalOverride,
            'total' => $manualTotalOverride,
            // Calculate expected discount: (initialTotal - manualTotalOverride)
            'discount' => $initialTotal - $manualTotalOverride,
        ]);

        // Verify proportional product price adjustment
        $orderProduct = $this->customer->orders()->find($order['id'])->products()->first();
        $expectedSubtotal = ($this->simpleProduct->sale_price * 2) - (($this->simpleProduct->sale_price * 2) / $initialCalculatedAmount) * ($initialTotal - $manualTotalOverride);
        $expectedUnitPrice = $expectedSubtotal / 2;

        $this->assertEqualsWithDelta($expectedSubtotal, $orderProduct->pivot->subtotal, 0.01);
        $this->assertEqualsWithDelta($expectedUnitPrice, $orderProduct->pivot->unit_price, 0.01);
    }

    /**
     * Test that a STORE_OWNER can create an order with manual_total_override (zero discount).
     *
     * @return void
     */
    public function test_store_owner_can_create_order_with_zero_manual_total_override()
    {
        $products = [
            [
                'product_id' => $this->simpleProduct->id,
                'order_quantity' => 1,
                'unit_price' => $this->simpleProduct->sale_price,
                'subtotal' => $this->simpleProduct->sale_price * 1,
            ],
        ];

        $initialCalculatedAmount = $this->simpleProduct->sale_price * 1; // 80
        $salesTax = 10;
        $deliveryFee = 5;
        $initialTotal = $initialCalculatedAmount + $salesTax + $deliveryFee; // 95

        $manualTotalOverride = $initialTotal; // Negotiated price, same as initial total

        $response = $this->post('/orders', [
            'customer_id' => $this->customer->id,
            'shop_id' => $this->shop->id, // Added shop_id
            'products' => $products,
            'amount' => $initialCalculatedAmount,
            'sales_tax' => $salesTax,
            'delivery_fee' => $deliveryFee,
            'paid_total' => $initialTotal,
            'total' => $initialTotal,
            'manual_total_override' => $manualTotalOverride,
            'customer_contact' => $this->faker->e164PhoneNumber(), // Use faker for contact
            'payment_gateway' => 'CASH_ON_DELIVERY',
            'shipping_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
            'billing_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
        ]);

        $response->assertStatus(201);
        $response->assertJson([
            'paid_total' => $manualTotalOverride,
            'total' => $manualTotalOverride,
        ]);

        $order = $response->json();
        $this->assertDatabaseHas('orders', [
            'id' => $order['id'],
            'paid_total' => $manualTotalOverride,
            'total' => $manualTotalOverride,
            'discount' => 0, // No additional discount
        ]);

        // Verify product prices remain unchanged
        $orderProduct = $this->customer->orders()->find($order['id'])->products()->first();
        $this->assertEqualsWithDelta($this->simpleProduct->sale_price * 1, $orderProduct->pivot->subtotal, 0.01);
        $this->assertEqualsWithDelta($this->simpleProduct->sale_price, $orderProduct->pivot->unit_price, 0.01);
    }

    /**
     * Test that a STORE_OWNER can create an order with manual_total_override (negative discount/markup).
     *
     * @return void
     */
    public function test_store_owner_can_create_order_with_negative_manual_total_override()
    {
        $products = [
            [
                'product_id' => $this->simpleProduct->id,
                'order_quantity' => 1,
                'unit_price' => $this->simpleProduct->sale_price,
                'subtotal' => $this->simpleProduct->sale_price * 1,
            ],
        ];

        $initialCalculatedAmount = $this->simpleProduct->sale_price * 1; // 80
        $salesTax = 10;
        $deliveryFee = 5;
        $initialTotal = $initialCalculatedAmount + $salesTax + $deliveryFee; // 95

        $manualTotalOverride = 120; // Negotiated price, higher than initial total

        $response = $this->post('/orders', [
            'customer_id' => $this->customer->id,
            'shop_id' => $this->shop->id, // Added shop_id
            'products' => $products,
            'amount' => $initialCalculatedAmount,
            'sales_tax' => $salesTax,
            'delivery_fee' => $deliveryFee,
            'paid_total' => $initialTotal,
            'total' => $initialTotal,
            'manual_total_override' => $manualTotalOverride,
            'customer_contact' => $this->faker->e164PhoneNumber(), // Use faker for contact
            'payment_gateway' => 'CASH_ON_DELIVERY',
            'shipping_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
            'billing_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
        ]);

        $response->assertStatus(201);
        $response->assertJson([
            'paid_total' => $manualTotalOverride,
            'total' => $manualTotalOverride,
        ]);

        $order = $response->json();
        $this->assertDatabaseHas('orders', [
            'id' => $order['id'],
            'paid_total' => $manualTotalOverride,
            'total' => $manualTotalOverride,
            // Calculate expected discount: (initialTotal - manualTotalOverride)
            'discount' => $initialTotal - $manualTotalOverride, // This will be negative
        ]);

        // Verify proportional product price adjustment (markup)
        $orderProduct = $this->customer->orders()->find($order['id'])->products()->first();
        $expectedSubtotal = ($this->simpleProduct->sale_price * 1) - (($this->simpleProduct->sale_price * 1) / $initialCalculatedAmount) * ($initialTotal - $manualTotalOverride);
        $expectedUnitPrice = $expectedSubtotal / 1;

        $this->assertEqualsWithDelta($expectedSubtotal, $orderProduct->pivot->subtotal, 0.01);
        $this->assertEqualsWithDelta($expectedUnitPrice, $orderProduct->pivot->unit_price, 0.01);
    }

    /**
     * Test that a CUSTOMER cannot use manual_total_override.
     *
     * @return void
     */
    public function test_customer_cannot_use_manual_total_override()
    {
        // Authenticate as customer for this test
        $customerToken = $this->customer->createToken('customer-token')->plainTextToken;
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $customerToken,
        ]);

        $products = [
            [
                'product_id' => $this->simpleProduct->id,
                'order_quantity' => 1,
                'unit_price' => $this->simpleProduct->sale_price,
                'subtotal' => $this->simpleProduct->sale_price * 1,
            ],
        ];

        $initialCalculatedAmount = $this->simpleProduct->sale_price * 1; // 80
        $salesTax = 10;
        $deliveryFee = 5;
        $initialTotal = $initialCalculatedAmount + $salesTax + $deliveryFee; // 95

        $manualTotalOverride = 50; // Attempted override by customer

        $response = $this->post('/orders', [
            'customer_id' => $this->customer->id,
            'shop_id' => $this->shop->id, // Added shop_id
            'products' => $products,
            'amount' => $initialCalculatedAmount,
            'sales_tax' => $salesTax,
            'delivery_fee' => $deliveryFee,
            'paid_total' => $initialTotal,
            'total' => $initialTotal,
            'manual_total_override' => $manualTotalOverride, // This should be ignored
            'customer_contact' => $this->faker->e164PhoneNumber(), // Use faker for contact
            'payment_gateway' => 'CASH_ON_DELIVERY',
            'shipping_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
            'billing_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
        ]);

        // Revert to store owner authentication for subsequent tests
        $storeOwnerToken = $this->storeOwner->createToken('test-token')->plainTextToken;
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $storeOwnerToken,
        ]);

        $response->assertStatus(201);
        // Assert that the manual_total_override was ignored and original total was used
        $response->assertJson([
            'paid_total' => $initialTotal,
            'total' => $initialTotal,
        ]);

        $order = $response->json();
        $this->assertDatabaseHas('orders', [
            'id' => $order['id'],
            'paid_total' => $initialTotal,
            'total' => $initialTotal,
            'discount' => 0, // No discount applied from manual override
        ]);

        // Verify product prices remain unchanged
        $orderProduct = $this->customer->orders()->find($order['id'])->products()->first();
        $this->assertEqualsWithDelta($this->simpleProduct->sale_price * 1, $orderProduct->pivot->subtotal, 0.01);
        $this->assertEqualsWithDelta($this->simpleProduct->sale_price, $orderProduct->pivot->unit_price, 0.01);
    }

    /**
     * Test that a SUPER_ADMIN can create an order with manual_total_override.
     *
     * @return void
     */
    public function test_super_admin_can_create_order_with_manual_total_override()
    {
        $products = [
            [
                'product_id' => $this->simpleProduct->id,
                'order_quantity' => 1,
                'unit_price' => $this->simpleProduct->sale_price,
                'subtotal' => $this->simpleProduct->sale_price * 1,
            ],
        ];

        $initialCalculatedAmount = $this->simpleProduct->sale_price * 1; // 80
        $salesTax = 10;
        $deliveryFee = 5;
        $initialTotal = $initialCalculatedAmount + $salesTax + $deliveryFee; // 95

        $manualTotalOverride = 70; // Negotiated price

        $response = $this->post('/orders', [
            'customer_id' => $this->customer->id,
            'shop_id' => $this->shop->id, // Added shop_id
            'products' => $products,
            'amount' => $initialCalculatedAmount,
            'sales_tax' => $salesTax,
            'delivery_fee' => $deliveryFee,
            'paid_total' => $initialTotal,
            'total' => $initialTotal,
            'manual_total_override' => $manualTotalOverride,
            'customer_contact' => $this->faker->e164PhoneNumber(), // Use faker for contact
            'payment_gateway' => 'CASH_ON_DELIVERY',
            'shipping_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
            'billing_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
        ]);

        $response->assertStatus(201);
        $response->assertJson([
            'paid_total' => $manualTotalOverride,
            'total' => $manualTotalOverride,
        ]);

        $order = $response->json();
        $this->assertDatabaseHas('orders', [
            'id' => $order['id'],
            'paid_total' => $manualTotalOverride,
            'total' => $manualTotalOverride,
            'discount' => $initialTotal - $manualTotalOverride,
        ]);

        // Verify proportional product price adjustment
        $orderProduct = $this->customer->orders()->find($order['id'])->products()->first();
        $expectedSubtotal = ($this->simpleProduct->sale_price * 1) - (($this->simpleProduct->sale_price * 1) / $initialCalculatedAmount) * ($initialTotal - $manualTotalOverride);
        $expectedUnitPrice = $expectedSubtotal / 1;

        $this->assertEqualsWithDelta($expectedSubtotal, $orderProduct->pivot->subtotal, 0.01);
        $this->assertEqualsWithDelta($expectedUnitPrice, $orderProduct->pivot->unit_price, 0.01);
    }

    /**
     * Test order creation with manual_total_override and initial_calculated_amount is zero.
     *
     * @return void
     */
    public function test_order_with_zero_initial_calculated_amount_and_manual_total_override()
    {
        $freeProduct = Product::factory()->create([
            'shop_id' => $this->shop->id,
            'product_type' => ProductType::SIMPLE,
            'price' => 0,
            'sale_price' => 0,
        ]);

        $products = [
            [
                'product_id' => $freeProduct->id,
                'order_quantity' => 1,
                'unit_price' => 0,
                'subtotal' => 0,
            ],
        ];

        $initialCalculatedAmount = 0; // All products are free
        $salesTax = 0;
        $deliveryFee = 0;
        $initialTotal = $initialCalculatedAmount + $salesTax + $deliveryFee; // 0

        $manualTotalOverride = 25; // Negotiated price for free items

        $response = $this->post('/orders', [
            'customer_id' => $this->customer->id,
            'shop_id' => $this->shop->id, // Added shop_id
            'products' => $products,
            'amount' => $initialCalculatedAmount,
            'sales_tax' => $salesTax,
            'delivery_fee' => $deliveryFee,
            'paid_total' => $initialTotal,
            'total' => $initialTotal,
            'manual_total_override' => $manualTotalOverride,
            'customer_contact' => $this->faker->e164PhoneNumber(),
            'payment_gateway' => 'CASH_ON_DELIVERY',
            'shipping_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
            'billing_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
        ]);

        $response->assertStatus(201);
        $response->assertJson([
            'paid_total' => $manualTotalOverride,
            'total' => $manualTotalOverride,
        ]);

        $order = $response->json();
        $this->assertDatabaseHas('orders', [
            'id' => $order['id'],
            'paid_total' => $manualTotalOverride,
            'total' => $manualTotalOverride,
            'discount' => $initialTotal - $manualTotalOverride, // This will be negative
        ]);

        // Verify proportional product price adjustment
        $orderProduct = $this->customer->orders()->find($order['id'])->products()->first();
        // Since initial_calculated_amount is 0, the proportional discount calculation needs careful handling.
        // The code handles division by zero by skipping the proportional adjustment if initial_calculated_amount is 0.
        // In this case, the product's subtotal and unit_price should remain 0, and the entire manual_total_override
        // should be reflected in the order's total and paid_total.
        $this->assertEqualsWithDelta(0, $orderProduct->pivot->subtotal, 0.01);
        $this->assertEqualsWithDelta(0, $orderProduct->pivot->unit_price, 0.01);
    }

    /**
     * Test that a STORE_OWNER can create an order with manual_total_override and multiple products.
     *
     * @return void
     */
    public function test_store_owner_can_create_order_with_manual_total_override_and_multiple_products()
    {
        // Create a second simple product
        $secondSimpleProduct = Product::factory()->create([
            'shop_id' => $this->shop->id,
            'product_type' => ProductType::SIMPLE,
            'price' => 50,
            'sale_price' => 40,
        ]);

        $products = [
            [
                'product_id' => $this->simpleProduct->id, // Sale price 80
                'order_quantity' => 2,
                'unit_price' => $this->simpleProduct->sale_price,
                'subtotal' => $this->simpleProduct->sale_price * 2, // 160
            ],
            [
                'product_id' => $secondSimpleProduct->id, // Sale price 40
                'order_quantity' => 3,
                'unit_price' => $secondSimpleProduct->sale_price,
                'subtotal' => $secondSimpleProduct->sale_price * 3, // 120
            ],
        ];

        $initialCalculatedAmount = ($this->simpleProduct->sale_price * 2) + ($secondSimpleProduct->sale_price * 3); // 160 + 120 = 280
        $salesTax = 20;
        $deliveryFee = 10;
        $initialTotal = $initialCalculatedAmount + $salesTax + $deliveryFee; // 280 + 20 + 10 = 310

        $manualTotalOverride = 250; // Negotiated price, lower than initial total

        $response = $this->post('/orders', [
            'customer_id' => $this->customer->id,
            'shop_id' => $this->shop->id, // Added shop_id
            'products' => $products,
            'amount' => $initialCalculatedAmount,
            'sales_tax' => $salesTax,
            'delivery_fee' => $deliveryFee,
            'paid_total' => $initialTotal,
            'total' => $initialTotal,
            'manual_total_override' => $manualTotalOverride,
            'customer_contact' => $this->faker->e164PhoneNumber(),
            'payment_gateway' => 'CASH_ON_DELIVERY',
            'shipping_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
            'billing_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
        ]);

        $response->assertStatus(201);
        $response->assertJson([
            'paid_total' => $manualTotalOverride,
            'total' => $manualTotalOverride,
        ]);

        $order = $response->json();
        $this->assertDatabaseHas('orders', [
            'id' => $order['id'],
            'paid_total' => $manualTotalOverride,
            'total' => $manualTotalOverride,
            'discount' => $initialTotal - $manualTotalOverride, // 310 - 250 = 60
        ]);

        // Verify proportional product price adjustment for each product
        $orderProducts = $this->customer->orders()->find($order['id'])->products;

        // Product 1: simpleProduct
        $product1 = $orderProducts->where('id', $this->simpleProduct->id)->first();
        $product1OriginalSubtotal = $this->simpleProduct->sale_price * 2; // 160
        $negotiationDiscount = $initialTotal - $manualTotalOverride; // 60
        $proportionalDiscountPerProduct1 = ($product1OriginalSubtotal / $initialCalculatedAmount) * $negotiationDiscount; // (160 / 280) * 60 = 34.2857
        $expectedSubtotal1 = $product1OriginalSubtotal - $proportionalDiscountPerProduct1; // 160 - 34.2857 = 125.7143
        $expectedUnitPrice1 = $expectedSubtotal1 / 2; // 62.85715

        $this->assertEqualsWithDelta($expectedSubtotal1, $product1->pivot->subtotal, 0.01);
        $this->assertEqualsWithDelta($expectedUnitPrice1, $product1->pivot->unit_price, 0.01);

        // Product 2: secondSimpleProduct
        $product2 = $orderProducts->where('id', $secondSimpleProduct->id)->first();
        $product2OriginalSubtotal = $secondSimpleProduct->sale_price * 3; // 120
        $proportionalDiscountPerProduct2 = ($product2OriginalSubtotal / $initialCalculatedAmount) * $negotiationDiscount; // (120 / 280) * 60 = 25.7143
        $expectedSubtotal2 = $product2OriginalSubtotal - $proportionalDiscountPerProduct2; // 120 - 25.7143 = 94.2857
        $expectedUnitPrice2 = $expectedSubtotal2 / 3; // 31.42856

        $this->assertEqualsWithDelta($expectedSubtotal2, $product2->pivot->subtotal, 0.01);
        $this->assertEqualsWithDelta($expectedUnitPrice2, $product2->pivot->unit_price, 0.01);
    }

    /**
     * Test that a STORE_OWNER can create an order with manual_total_override and an existing coupon discount.
     *
     * @return void
     */
    public function test_store_owner_can_create_order_with_manual_total_override_and_coupon_discount()
    {
        $products = [
            [
                'product_id' => $this->simpleProduct->id,
                'order_quantity' => 2,
                'unit_price' => $this->simpleProduct->sale_price,
                'subtotal' => $this->simpleProduct->sale_price * 2,
            ],
        ];

        $initialCalculatedAmount = $this->simpleProduct->sale_price * 2; // 160
        $salesTax = 10;
        $deliveryFee = 5;
        $couponDiscount = 10; // Existing coupon discount
        $initialTotal = $initialCalculatedAmount + $salesTax + $deliveryFee - $couponDiscount; // 160 + 10 + 5 - 10 = 165

        $manualTotalOverride = 150; // Negotiated price, lower than initial total

        $response = $this->post('/orders', [
            'customer_id' => $this->customer->id,
            'shop_id' => $this->shop->id, // Added shop_id
            'products' => $products,
            'amount' => $initialCalculatedAmount,
            'sales_tax' => $salesTax,
            'delivery_fee' => $deliveryFee,
            'paid_total' => $initialTotal,
            'total' => $initialTotal,
            'discount' => $couponDiscount, // Existing coupon discount
            'manual_total_override' => $manualTotalOverride,
            'customer_contact' => $this->faker->e164PhoneNumber(),
            'payment_gateway' => 'CASH_ON_DELIVERY',
            'shipping_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
            'billing_address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->countryCode,
            ],
        ]);

        $response->assertStatus(201);
        $response->assertJson([
            'paid_total' => $manualTotalOverride,
            'total' => $manualTotalOverride,
        ]);

        $order = $response->json();
        $negotiationDiscount = $initialTotal - $manualTotalOverride; // 165 - 150 = 15
        $expectedTotalDiscount = $couponDiscount + $negotiationDiscount; // 10 + 15 = 25

        $this->assertDatabaseHas('orders', [
            'id' => $order['id'],
            'paid_total' => $manualTotalOverride,
            'total' => $manualTotalOverride,
            'discount' => $expectedTotalDiscount,
        ]);

        // Verify proportional product price adjustment
        $orderProduct = $this->customer->orders()->find($order['id'])->products()->first();
        $productOriginalSubtotal = $this->simpleProduct->sale_price * 2; // 160
        $proportionalDiscountPerProduct = ($productOriginalSubtotal / $initialCalculatedAmount) * $negotiationDiscount; // (160 / 160) * 15 = 15
        $expectedSubtotal = $productOriginalSubtotal - $proportionalDiscountPerProduct; // 160 - 15 = 145
        $expectedUnitPrice = $expectedSubtotal / 2; // 72.5

        $this->assertEqualsWithDelta($expectedSubtotal, $orderProduct->pivot->subtotal, 0.01);
        $this->assertEqualsWithDelta($expectedUnitPrice, $orderProduct->pivot->unit_price, 0.01);
    }

    /**
     * Test that a STORE_OWNER can create an order with manual_total_override and a variable product.
     *
     * @return void
     */
    // public function test_store_owner_can_create_order_with_manual_total_override_and_variable_product()
    // {
    //     $products = [
    //         [
    //             'product_id' => $this->variableProduct->id,
    //             'variation_option_id' => $this->variableProduct->variations->first()->id,
    //             'order_quantity' => 2,
    //             'unit_price' => $this->variableProduct->variations->first()->sale_price, // 150
    //             'subtotal' => $this->variableProduct->variations->first()->sale_price * 2, // 300
    //         ],
    //     ];

    //     $initialCalculatedAmount = $this->variableProduct->sale_price * 2; // 300
    //     $salesTax = 20;
    //     $deliveryFee = 10;
    //     $initialTotal = $initialCalculatedAmount + $salesTax + $deliveryFee; // 300 + 20 + 10 = 330

    //     $manualTotalOverride = 280; // Negotiated price, lower than initial total

    //     $response = $this->post('/orders', [
    //         'customer_id' => $this->customer->id,
    //         'shop_id' => $this->shop->id, // Added shop_id
    //         'products' => $products,
    //         'amount' => $initialCalculatedAmount,
    //         'sales_tax' => $salesTax,
    //         'delivery_fee' => $deliveryFee,
    //         'paid_total' => $initialTotal,
    //         'total' => $initialTotal,
    //         'manual_total_override' => $manualTotalOverride,
    //         'customer_contact' => $this->faker->e164PhoneNumber(),
    //         'payment_gateway' => 'CASH_ON_DELIVERY',
    //         'shipping_address' => [
    //             'street_address' => $this->faker->streetAddress,
    //             'city' => $this->faker->city,
    //             'state' => $this->faker->state,
    //             'zip' => $this->faker->postcode,
    //             'country' => $this->faker->countryCode,
    //         ],
    //         'billing_address' => [
    //             'street_address' => $this->faker->streetAddress,
    //             'city' => $this->faker->city,
    //             'state' => $this->faker->state,
    //             'zip' => $this->faker->postcode,
    //             'country' => $this->faker->countryCode,
    //         ],
    //     ]);
 
    //     $response->dump(); // Add dump for debugging
    //     $response->assertStatus(201);
    //     $response->assertJson([
    //         'paid_total' => $manualTotalOverride,
    //         'total' => $manualTotalOverride,
    //     ]);

    //     $order = $response->json();
    //     $this->assertDatabaseHas('orders', [
    //         'id' => $order['id'],
    //         'paid_total' => $manualTotalOverride,
    //         'total' => $manualTotalOverride,
    //         'discount' => $initialTotal - $manualTotalOverride, // 330 - 280 = 50
    //     ]);

    //     // Verify proportional product price adjustment
    //     $orderProduct = $this->customer->orders()->find($order['id'])->products()->first();
    //     $productOriginalSubtotal = $this->variableProduct->sale_price * 2; // 300
    //     $negotiationDiscount = $initialTotal - $manualTotalOverride; // 50
    //     $proportionalDiscountPerProduct = ($productOriginalSubtotal / $initialCalculatedAmount) * $negotiationDiscount; // (300 / 300) * 50 = 50
    //     $expectedSubtotal = $productOriginalSubtotal - $proportionalDiscountPerProduct; // 300 - 50 = 250
    //     $expectedUnitPrice = $expectedSubtotal / 2; // 125

    //     $this->assertEqualsWithDelta($expectedSubtotal, $orderProduct->pivot->subtotal, 0.01);
    //     $this->assertEqualsWithDelta($expectedUnitPrice, $orderProduct->pivot->unit_price, 0.01);
    // }
}