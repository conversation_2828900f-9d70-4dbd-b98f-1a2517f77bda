<?php

namespace Marvel\Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Marvel\Database\Models\Category;
use Marvel\Database\Models\Product;
use Marvel\Database\Models\User;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\OrderProduct;
use Tests\TestCase;

class CategoryEndpointsTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test recommended categories endpoint for non-authenticated users
     *
     * @return void
     */
    public function testRecommendedCategoriesForNonAuthenticatedUsers()
    {
        // Create some categories with products
        $categories = Category::factory()->count(5)->create();
        
        foreach ($categories as $category) {
            $products = Product::factory()->count(3)->create();
            $category->products()->attach($products);
        }

        // Test the endpoint
        $response = $this->getJson('/recommended-categories');
        
        $response->assertStatus(200);
        $response->assertJsonCount(5); // Default limit is 5
    }

    /**
     * Test recommended categories endpoint for authenticated users
     *
     * @return void
     */
    public function testRecommendedCategoriesForAuthenticatedUsers()
    {
        // Create a user
        $user = User::factory()->create();
        
        // Create some categories with products
        $categories = Category::factory()->count(10)->create();
        
        foreach ($categories as $category) {
            $products = Product::factory()->count(2)->create();
            $category->products()->attach($products);
        }
        
        // Create an order for the user with products from specific categories
        $order = Order::factory()->create([
            'customer_id' => $user->id
        ]);
        
        // Add products from the first 3 categories to the order
        for ($i = 0; $i < 3; $i++) {
            $product = $categories[$i]->products->first();
            OrderProduct::factory()->create([
                'order_id' => $order->id,
                'product_id' => $product->id,
                'order_quantity' => 1
            ]);
        }
        
        // Test the endpoint
        $response = $this->actingAs($user)->getJson('/recommended-categories');
        
        $response->assertStatus(200);
        $response->assertJsonCount(5); // Default limit is 5
        
        // The first categories should be the ones from the user's orders
        $responseData = $response->json();
        $this->assertEquals($categories[0]->id, $responseData[0]['id']);
        $this->assertEquals($categories[1]->id, $responseData[1]['id']);
        $this->assertEquals($categories[2]->id, $responseData[2]['id']);
    }

    /**
     * Test trending categories endpoint
     *
     * @return void
     */
    public function testTrendingCategories()
    {
        // Create some categories with products
        $categories = Category::factory()->count(5)->create();
        
        foreach ($categories as $category) {
            $products = Product::factory()->count(2)->create();
            $category->products()->attach($products);
        }
        
        // Create orders with products from specific categories
        $user = User::factory()->create();
        
        // Create more orders for the first category to make it trending
        for ($i = 0; $i < 5; $i++) {
            $order = Order::factory()->create([
                'customer_id' => $user->id,
                'order_status' => 'order-completed',
                'created_at' => now()->subDays(5)
            ]);
            
            $product = $categories[0]->products->first();
            OrderProduct::factory()->create([
                'order_id' => $order->id,
                'product_id' => $product->id,
                'order_quantity' => 1
            ]);
        }
        
        // Create fewer orders for the second category
        for ($i = 0; $i < 3; $i++) {
            $order = Order::factory()->create([
                'customer_id' => $user->id,
                'order_status' => 'order-completed',
                'created_at' => now()->subDays(10)
            ]);
            
            $product = $categories[1]->products->first();
            OrderProduct::factory()->create([
                'order_id' => $order->id,
                'product_id' => $product->id,
                'order_quantity' => 1
            ]);
        }
        
        // Test the endpoint
        $response = $this->getJson('/trending-categories');
        
        $response->assertStatus(200);
        $response->assertJsonCount(3); // Default limit is 3
        
        // The first category should be the one with more orders
        $responseData = $response->json();
        $this->assertEquals($categories[0]->id, $responseData[0]['id']);
        $this->assertEquals($categories[1]->id, $responseData[1]['id']);
    }

    /**
     * Test popular categories endpoint
     *
     * @return void
     */
    public function testPopularCategories()
    {
        // Create some categories with varying numbers of products
        $categories = Category::factory()->count(5)->create();
        
        // First category has 10 products
        $products = Product::factory()->count(10)->create();
        $categories[0]->products()->attach($products);
        
        // Second category has 7 products
        $products = Product::factory()->count(7)->create();
        $categories[1]->products()->attach($products);
        
        // Third category has 5 products
        $products = Product::factory()->count(5)->create();
        $categories[2]->products()->attach($products);
        
        // Fourth category has 3 products
        $products = Product::factory()->count(3)->create();
        $categories[3]->products()->attach($products);
        
        // Fifth category has 1 product
        $products = Product::factory()->count(1)->create();
        $categories[4]->products()->attach($products);
        
        // Test the endpoint
        $response = $this->getJson('/popular-categories');
        
        $response->assertStatus(200);
        $response->assertJsonCount(5);
        
        // Categories should be ordered by product count
        $responseData = $response->json();
        $this->assertEquals($categories[0]->id, $responseData[0]['id']);
        $this->assertEquals($categories[1]->id, $responseData[1]['id']);
        $this->assertEquals($categories[2]->id, $responseData[2]['id']);
        $this->assertEquals($categories[3]->id, $responseData[3]['id']);
        $this->assertEquals($categories[4]->id, $responseData[4]['id']);
    }
}
