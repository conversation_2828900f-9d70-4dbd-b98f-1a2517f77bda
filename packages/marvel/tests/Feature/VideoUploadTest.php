<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;
use Tests\TestCase;
use Marvel\Database\Models\User;
use Marvel\Enums\Permission;
use Symfony\Component\HttpFoundation\Response;

class VideoUploadTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $customer;
    protected User $superAdmin;

    public function setUp(): void
    {
        parent::setUp();
        $this->createPermissions();

        $this->customer = new User([
            'name' => 'Customer',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->customer->save();
        $this->customer->givePermissionTo(Permission::CUSTOMER);

        $this->superAdmin = new User([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->superAdmin->save();
        $this->superAdmin->givePermissionTo(Permission::SUPER_ADMIN);

        Storage::fake('public');
    }

    protected function createPermissions()
    {
        \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'api']);
        \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'customer', 'guard_name' => 'api']);
        \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'staff', 'guard_name' => 'api']);
        \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'store_owner', 'guard_name' => 'api']);
        \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'delivery_agent', 'guard_name' => 'api']);

        $permissions = [
            Permission::SUPER_ADMIN,
            Permission::CUSTOMER,
            Permission::STAFF,
            Permission::STORE_OWNER,
            Permission::DELIVERY_AGENT,
        ];

        foreach ($permissions as $permission) {
            \Spatie\Permission\Models\Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }
    }

    protected function insertContent(string $filePath, int $lineNumber, string $content)
    {
        $fileContent = File::get($filePath);
        $lines = explode("\n", $fileContent);
        array_splice($lines, $lineNumber - 1, 0, $content);
        File::put($filePath, implode("\n", $lines));
    }

    protected function removeContent(string $filePath, int $lineNumber, string $content)
    {
        $fileContent = File::get($filePath);
        $lines = explode("\n", $fileContent);
        // Find the exact line to remove. This is a simple check, might need more robust logic for complex scenarios.
        if (isset($lines[$lineNumber - 1]) && trim($lines[$lineNumber - 1]) === trim($content)) {
            array_splice($lines, $lineNumber - 1, 1);
        }
        File::put($filePath, implode("\n", $lines));
    }

    /** @test */
    public function it_can_upload_a_valid_mp4_video_file()
    {
        $token = $this->customer->createToken('test-token')->plainTextToken;
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ]);

        $file = UploadedFile::fake()->create('video.mp4', 50000, 'video/mp4'); // 50MB
        dump($file);
        $response = $this->postJson('/videos', ['video' => $file]);
        dump($response);
        $response->assertStatus(Response::HTTP_OK)
                 ->assertJsonStructure([
                     'id',
                     'original',
                     'thumbnail',
                 ]);

         $videoData = $response->json();
         $this->assertStringContainsString('video.mp4', $videoData['original']);
         $this->assertNotEmpty($videoData['thumbnail']);
         $this->assertStringContainsString('thumbnail', $videoData['thumbnail']);
         $this->assertEquals('video/mp4', $videoData['mime_type']);
         Storage::disk('public')->assertExists(str_replace(Storage::url(''), '', $videoData['original']));
         Storage::disk('public')->assertExists(str_replace(Storage::url(''), '', $videoData['thumbnail']));
    }

    /** @test */
    public function it_can_upload_a_valid_webm_video_file()
    {
        $token = $this->customer->createToken('test-token')->plainTextToken;
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ]);

        $file = UploadedFile::fake()->create('video.webm', 50000, 'video/webm'); // 50MB

        $response = $this->postJson('/videos', ['video' => $file]);

        $response->assertStatus(Response::HTTP_OK)
                 ->assertJsonStructure([
                     'id',
                     'original',
                     'thumbnail',
                     'name',
                     'size',
                     'mime_type',
                 ]);

         $videoData = $response->json();
         $this->assertStringContainsString('video.webm', $videoData['original']);
         $this->assertNotEmpty($videoData['thumbnail']);
         $this->assertStringContainsString('thumbnail', $videoData['thumbnail']);
         $this->assertEquals('video.webm', $videoData['name']);
         $this->assertEquals(50000, $videoData['size']);
         $this->assertEquals('video/webm', $videoData['mime_type']);
         Storage::disk('public')->assertExists(str_replace(Storage::url(''), '', $videoData['original']));
         Storage::disk('public')->assertExists(str_replace(Storage::url(''), '', $videoData['thumbnail']));
    }

    /** @test */
    public function it_rejects_invalid_file_types()
    {
        $token = $this->customer->createToken('test-token')->plainTextToken;
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ]);

        $imageFile = UploadedFile::fake()->image('image.jpg');
        $textFile = UploadedFile::fake()->create('document.txt', 100, 'text/plain');

        $responseImage = $this->postJson('/videos', ['video' => $imageFile]);
        $responseImage->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
                      ->assertJsonValidationErrors(['video'])
                      ->assertJsonFragment([
                          'message' => 'The given data was invalid.',
                          'errors' => [
                              'video' => [
                                  'The video must be a file of type: mp4, webm.'
                              ]
                          ]
                      ]);

        $responseText = $this->postJson('/videos', ['video' => $textFile]);
        $responseText->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
                     ->assertJsonValidationErrors(['video'])
                     ->assertJsonFragment([
                         'message' => 'The given data was invalid.',
                         'errors' => [
                             'video' => [
                                 'The video must be a file of type: mp4, webm.'
                             ]
                         ]
                     ]);
    }

    /** @test */
    public function it_rejects_files_exceeding_the_size_limit()
    {
        $token = $this->customer->createToken('test-token')->plainTextToken;
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ]);

        // Create a file larger than 100MB (102400 KB)
        $largeVideo = UploadedFile::fake()->create('large_video.mp4', 102401, 'video/mp4');

        $response = $this->postJson('/videos', ['video' => $largeVideo]);

        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
                 ->assertJsonValidationErrors(['video'])
                 ->assertJsonFragment([
                     'message' => 'The given data was invalid.',
                     'errors' => [
                         'video' => [
                             'The video must not be greater than 102400 kilobytes.'
                         ]
                     ]
                 ]);
    }

    /** @test */
    public function it_rejects_requests_without_a_file()
    {
        $token = $this->customer->createToken('test-token')->plainTextToken;
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ]);

        $response = $this->postJson('/videos', []);

        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
                 ->assertJsonValidationErrors(['video'])
                 ->assertJsonFragment([
                     'message' => 'The given data was invalid.',
                     'errors' => [
                         'video' => [
                             'The video field is required.'
                         ]
                     ]
                 ]);
    }

    /** @test */
    public function it_handles_internal_server_errors_gracefully()
    {
        // Temporarily introduce an exception in VideoController@store
        $this->insertContent(
            'packages/marvel/src/Http/Controllers/VideoController.php',
            27,
            "            throw new \Exception('Simulated internal error');"
        );

        $token = $this->customer->createToken('test-token')->plainTextToken;
        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ]);

        $file = UploadedFile::fake()->create('video.mp4', 1000, 'video/mp4');

        $response = $this->postJson('/videos', ['video' => $file]);

        $response->assertStatus(Response::HTTP_INTERNAL_SERVER_ERROR)
                 ->assertJsonFragment([
                     'message' => 'Internal server error',
                     'reason' => 'Simulated internal error'
                 ]);

        // Remove the simulated exception line
        $this->removeContent(
            'packages/marvel/src/Http/Controllers/VideoController.php',
            27,
            "            throw new \Exception('Simulated internal error');"
        );
    }
}