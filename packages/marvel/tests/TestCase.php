<?php

namespace Marvel\Tests;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Marvel\Enums\Permission;
use Spatie\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role;
use Tests\CreatesApplication;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication, RefreshDatabase;

    /**
     * Seed permissions needed for testing.
     *
     * @return void
     */
    protected function seedPermissions()
    {
        // Create roles if they don't exist
        $adminRole = Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'api']);
        $agentRole = Role::firstOrCreate(['name' => 'delivery_agent', 'guard_name' => 'api']);
        $customerRole = Role::firstOrCreate(['name' => 'customer', 'guard_name' => 'api']);
        $storeOwnerRole = Role::firstOrCreate(['name' => 'store_owner', 'guard_name' => 'api']);
        $staffRole = Role::firstOrCreate(['name' => 'staff', 'guard_name' => 'api']);

        // Create permissions if they don't exist
        $permissions = [
            Permission::SUPER_ADMIN,
            Permission::STORE_OWNER,
            Permission::STAFF,
            Permission::CUSTOMER,
            Permission::DELIVERY_AGENT,
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }

        // Assign permissions to roles
        $adminRole->givePermissionTo(Permission::SUPER_ADMIN);
        $storeOwnerRole->givePermissionTo(Permission::STORE_OWNER);
        $staffRole->givePermissionTo(Permission::STAFF);
        $customerRole->givePermissionTo(Permission::CUSTOMER);
        $agentRole->givePermissionTo([Permission::DELIVERY_AGENT, Permission::CUSTOMER]);
    }
}
