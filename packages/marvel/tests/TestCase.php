<?php

namespace Marvel\Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;
use Marvel\Enums\Permission;
use Spatie\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role;
use Tests\CreatesApplication;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    protected function setUp(): void
    {
        parent::setUp();

        // Use in-memory SQLite database for testing
        config(['database.connections.testing' => [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
        ]]);

        config(['database.default' => 'testing']);

        // Run migrations
        $this->runDeliveryMigrations();

        // Seed permissions and roles
        $this->seedPermissions();
    }

    /**
     * Run the delivery system migrations.
     *
     * @return void
     */
    protected function runDeliveryMigrations()
    {
        // Create base tables needed for testing
        Artisan::call('migrate', ['--path' => 'vendor/spatie/laravel-permission/database/migrations', '--database' => 'testing']);

        // Create users table
        $this->createUsersTable();

        // Create shops table
        $this->createShopsTable();

        // Create orders table
        $this->createOrdersTable();

        // Run delivery migrations
        $migrationFiles = glob(base_path('packages/marvel/database/migrations/delivery/*.php'));
        foreach ($migrationFiles as $file) {
            $migrationClass = $this->getMigrationClass($file);
            $instance = new $migrationClass;
            $instance->up();
        }
    }

    /**
     * Get the migration class from a file.
     *
     * @param string $file
     * @return string
     */
    protected function getMigrationClass($file)
    {
        $fileInfo = pathinfo($file);
        $className = \Illuminate\Support\Str::studly(implode('_', array_slice(explode('_', $fileInfo['filename']), 4)));

        require_once $file;

        return $className;
    }

    /**
     * Create users table for testing.
     *
     * @return void
     */
    protected function createUsersTable()
    {
        Schema::create('users', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->boolean('is_active')->default(true);
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Create shops table for testing.
     *
     * @return void
     */
    protected function createShopsTable()
    {
        Schema::create('shops', function ($table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->unsignedBigInteger('owner_id');
            $table->boolean('is_active')->default(true);
            $table->json('address')->nullable();
            $table->json('settings')->nullable();
            $table->timestamps();

            $table->foreign('owner_id')->references('id')->on('users');
        });
    }

    /**
     * Create orders table for testing.
     *
     * @return void
     */
    protected function createOrdersTable()
    {
        Schema::create('orders', function ($table) {
            $table->id();
            $table->string('tracking_number')->unique();
            $table->unsignedBigInteger('customer_id');
            $table->unsignedBigInteger('shop_id');
            $table->string('order_status')->default('order-pending');
            $table->decimal('amount', 10, 2);
            $table->string('payment_status')->default('payment-pending');
            $table->string('payment_method')->nullable();
            $table->json('shipping_address')->nullable();
            $table->json('billing_address')->nullable();
            $table->boolean('requires_delivery')->default(false);
            $table->unsignedBigInteger('delivery_id')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('customer_id')->references('id')->on('users');
            $table->foreign('shop_id')->references('id')->on('shops');
        });
    }

    /**
     * Seed permissions needed for testing.
     *
     * @return void
     */
    protected function seedPermissions()
    {
        // Create roles if they don't exist
        $adminRole = Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'api']);
        $agentRole = Role::firstOrCreate(['name' => 'delivery_agent', 'guard_name' => 'api']);
        $customerRole = Role::firstOrCreate(['name' => 'customer', 'guard_name' => 'api']);
        $storeOwnerRole = Role::firstOrCreate(['name' => 'store_owner', 'guard_name' => 'api']);
        $staffRole = Role::firstOrCreate(['name' => 'staff', 'guard_name' => 'api']);

        // Create permissions if they don't exist
        $permissions = [
            Permission::SUPER_ADMIN,
            Permission::STORE_OWNER,
            Permission::STAFF,
            Permission::CUSTOMER,
            Permission::DELIVERY_AGENT,
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }

        // Assign permissions to roles
        $adminRole->givePermissionTo(Permission::SUPER_ADMIN);
        $storeOwnerRole->givePermissionTo(Permission::STORE_OWNER);
        $staffRole->givePermissionTo(Permission::STAFF);
        $customerRole->givePermissionTo(Permission::CUSTOMER);
        $agentRole->givePermissionTo([Permission::DELIVERY_AGENT, Permission::CUSTOMER]);
    }
}
