<?php

namespace Marvel\Tests\Unit\Http\Requests;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Marvel\Database\Models\User;
use Marvel\Http\Requests\UpdateAgentLocationRequest;
use Marvel\Tests\TestCase;
use Marvel\Enums\Permission;
use Spatie\Permission\Models\Permission as PermissionModel;

class UpdateAgentLocationRequestTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->createPermissions();
    }

    /** @test */
    public function it_validates_required_location_fields()
    {
        $request = new UpdateAgentLocationRequest();
        $rules = $request->rules();

        // Test missing location
        $validator = Validator::make([], $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('location', $validator->errors()->toArray());

        // Test missing lat
        $validator = Validator::make([
            'location' => ['lng' => -74.0060]
        ], $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('location.lat', $validator->errors()->toArray());

        // Test missing lng
        $validator = Validator::make([
            'location' => ['lat' => 40.7128]
        ], $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('location.lng', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_coordinate_ranges()
    {
        $request = new UpdateAgentLocationRequest();
        $rules = $request->rules();

        // Test invalid latitude (too high)
        $validator = Validator::make([
            'location' => ['lat' => 91.0, 'lng' => -74.0060]
        ], $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('location.lat', $validator->errors()->toArray());

        // Test invalid latitude (too low)
        $validator = Validator::make([
            'location' => ['lat' => -91.0, 'lng' => -74.0060]
        ], $rules);
        $this->assertFalse($validator->passes());

        // Test invalid longitude (too high)
        $validator = Validator::make([
            'location' => ['lat' => 40.7128, 'lng' => 181.0]
        ], $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('location.lng', $validator->errors()->toArray());

        // Test invalid longitude (too low)
        $validator = Validator::make([
            'location' => ['lat' => 40.7128, 'lng' => -181.0]
        ], $rules);
        $this->assertFalse($validator->passes());
    }

    /** @test */
    public function it_rejects_null_island_coordinates()
    {
        $request = new UpdateAgentLocationRequest();
        $rules = $request->rules();

        $validator = Validator::make([
            'location' => ['lat' => 0.0, 'lng' => 0.0]
        ], $rules);

        $this->assertFalse($validator->passes());
        $this->assertStringContainsString('null island', $validator->errors()->first('location.lat'));
    }

    /** @test */
    public function it_validates_status_values()
    {
        $request = new UpdateAgentLocationRequest();
        $rules = $request->rules();

        // Test valid status
        $validator = Validator::make([
            'location' => ['lat' => 40.7128, 'lng' => -74.0060],
            'status' => 'ONLINE'
        ], $rules);
        $this->assertTrue($validator->passes());

        // Test invalid status
        $validator = Validator::make([
            'location' => ['lat' => 40.7128, 'lng' => -74.0060],
            'status' => 'INVALID_STATUS'
        ], $rules);
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('status', $validator->errors()->toArray());
    }

    /** @test */
    public function it_validates_coordinate_precision()
    {
        // Create coordinates with more than 8 decimal places
        $highPrecisionLat = 40.123456789012345; // 15 decimal places
        $highPrecisionLng = -74.123456789012345; // 15 decimal places

        $request = new UpdateAgentLocationRequest();
        $request->merge([
            'location' => [
                'lat' => $highPrecisionLat,
                'lng' => $highPrecisionLng
            ]
        ]);

        $validator = Validator::make([
            'location' => [
                'lat' => $highPrecisionLat,
                'lng' => $highPrecisionLng
            ]
        ], $request->rules());

        // Apply the withValidator method
        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
        $this->assertStringContainsString('precision too high', $validator->errors()->first('location.lat'));
        $this->assertStringContainsString('precision too high', $validator->errors()->first('location.lng'));
    }

    /** @test */
    public function it_detects_suspicious_coordinate_patterns()
    {
        // Test repeated digits pattern (need 6+ consecutive digits)
        $suspiciousLat = 11.1111111; // 7 consecutive 1s
        $suspiciousLng = 22.2222222; // 7 consecutive 2s

        $request = new UpdateAgentLocationRequest();
        $request->merge([
            'location' => [
                'lat' => $suspiciousLat,
                'lng' => $suspiciousLng
            ]
        ]);

        $validator = Validator::make([
            'location' => [
                'lat' => $suspiciousLat,
                'lng' => $suspiciousLng
            ]
        ], $request->rules());

        $request->withValidator($validator);

        $this->assertFalse($validator->passes());
        $this->assertStringContainsString('suspicious pattern', $validator->errors()->first('location.lat'));
        $this->assertStringContainsString('suspicious pattern', $validator->errors()->first('location.lng'));
    }

    /** @test */
    public function it_accepts_valid_coordinates()
    {
        $request = new UpdateAgentLocationRequest();
        $rules = $request->rules();

        $validator = Validator::make([
            'location' => ['lat' => 40.7128456, 'lng' => -74.0059728],
            'status' => 'ONLINE'
        ], $rules);

        $this->assertTrue($validator->passes());
    }

    /** @test */
    public function it_authorizes_delivery_agents()
    {
        $agent = User::factory()->create();
        $agent->givePermissionTo(Permission::DELIVERY_AGENT);

        $request = new UpdateAgentLocationRequest();
        $request->setUserResolver(function () use ($agent) {
            return $agent;
        });

        $this->assertTrue($request->authorize());
    }

    /** @test */
    public function it_rejects_unauthorized_users()
    {
        $user = User::factory()->create();
        // Don't give delivery agent permission

        $request = new UpdateAgentLocationRequest();
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        $this->assertFalse($request->authorize());
    }

    /** @test */
    public function it_rejects_unauthenticated_requests()
    {
        $request = new UpdateAgentLocationRequest();
        $request->setUserResolver(function () {
            return null;
        });

        $this->assertFalse($request->authorize());
    }

    /** @test */
    public function it_provides_custom_error_messages()
    {
        $request = new UpdateAgentLocationRequest();
        $messages = $request->messages();

        $this->assertArrayHasKey('location.required', $messages);
        $this->assertArrayHasKey('location.lat.required', $messages);
        $this->assertArrayHasKey('location.lng.required', $messages);
        $this->assertArrayHasKey('location.lat.between', $messages);
        $this->assertArrayHasKey('location.lng.between', $messages);
        $this->assertArrayHasKey('status.in', $messages);

        $this->assertEquals('Location data is required.', $messages['location.required']);
        $this->assertEquals('Latitude must be between -90 and 90 degrees.', $messages['location.lat.between']);
    }

    /** @test */
    public function it_handles_non_numeric_coordinates()
    {
        $request = new UpdateAgentLocationRequest();
        $rules = $request->rules();

        $validator = Validator::make([
            'location' => ['lat' => 'not_a_number', 'lng' => 'also_not_a_number']
        ], $rules);

        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('location.lat', $validator->errors()->toArray());
        $this->assertArrayHasKey('location.lng', $validator->errors()->toArray());
    }

    /** @test */
    public function it_handles_edge_case_coordinates()
    {
        $request = new UpdateAgentLocationRequest();
        $rules = $request->rules();

        // Test boundary values
        $validator = Validator::make([
            'location' => ['lat' => 90.0, 'lng' => 180.0]
        ], $rules);
        $this->assertTrue($validator->passes());

        $validator = Validator::make([
            'location' => ['lat' => -90.0, 'lng' => -180.0]
        ], $rules);
        $this->assertTrue($validator->passes());
    }

    private function createPermissions(): void
    {
        $permissions = [
            Permission::DELIVERY_AGENT,
            Permission::CUSTOMER,
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }
    }
}
