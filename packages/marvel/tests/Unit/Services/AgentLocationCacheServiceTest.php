<?php

namespace Marvel\Tests\Unit\Services;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Marvel\Services\AgentLocationCacheService;
use Marvel\Tests\TestCase;

class AgentLocationCacheServiceTest extends TestCase
{
    use RefreshDatabase;

    private AgentLocationCacheService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new AgentLocationCacheService();
        
        // Clear cache before each test
        Cache::flush();
    }

    /** @test */
    public function it_caches_agent_location_successfully()
    {
        $agentId = 123;
        $location = ['lat' => 40.7128, 'lng' => -74.0060];
        $status = 'ONLINE';

        $this->service->cacheAgentLocation($agentId, $location, $status);

        $cached = $this->service->getCachedAgentLocation($agentId);

        $this->assertNotNull($cached);
        $this->assertEquals(40.7128, $cached['lat']);
        $this->assertEquals(-74.0060, $cached['lng']);
        $this->assertEquals('ONLINE', $cached['status']);
        $this->assertArrayHasKey('updated_at', $cached);
    }

    /** @test */
    public function it_returns_null_for_non_existent_agent()
    {
        $cached = $this->service->getCachedAgentLocation(999);
        $this->assertNull($cached);
    }

    /** @test */
    public function it_caches_nearby_agents_results()
    {
        $lat = 40.7128;
        $lng = -74.0060;
        $radius = 5.0;
        $agents = [
            ['id' => 1, 'distance' => 1.2],
            ['id' => 2, 'distance' => 2.5],
        ];

        $this->service->cacheNearbyAgents($lat, $lng, $radius, $agents);

        $cached = $this->service->getCachedNearbyAgents($lat, $lng, $radius);

        $this->assertNotNull($cached);
        $this->assertCount(2, $cached);
        $this->assertEquals(1, $cached[0]['id']);
    }

    /** @test */
    public function it_invalidates_agent_location_cache()
    {
        $agentId = 123;
        $location = ['lat' => 40.7128, 'lng' => -74.0060];

        // Cache location
        $this->service->cacheAgentLocation($agentId, $location);
        $this->assertNotNull($this->service->getCachedAgentLocation($agentId));

        // Invalidate cache
        $this->service->invalidateAgentLocation($agentId);
        $this->assertNull($this->service->getCachedAgentLocation($agentId));
    }

    /** @test */
    public function it_generates_consistent_cache_keys_for_nearby_agents()
    {
        // Test that similar coordinates generate the same cache key
        $agents1 = [['id' => 1]];
        $agents2 = [['id' => 2]];

        // Cache with slightly different coordinates that should round to same key
        $this->service->cacheNearbyAgents(40.7128, -74.0060, 5.0, $agents1);
        $this->service->cacheNearbyAgents(40.7129, -74.0061, 5.0, $agents2);

        // Should get the second one (overwrote the first due to same rounded key)
        $cached = $this->service->getCachedNearbyAgents(40.7128, -74.0060, 5.0);
        $this->assertEquals(2, $cached[0]['id']);
    }

    /** @test */
    public function it_handles_redis_unavailability_gracefully()
    {
        // Mock Redis to throw exception
        Redis::shouldReceive('connection')
            ->andThrow(new \Exception('Redis unavailable'));

        $agentId = 123;
        $location = ['lat' => 40.7128, 'lng' => -74.0060];

        // Should not throw exception
        $this->service->cacheAgentLocation($agentId, $location);

        // Should return empty array when Redis fails
        $result = $this->service->getAgentsWithinRadiusFromRedis(40.7128, -74.0060, 5.0);
        $this->assertEmpty($result);
    }

    /** @test */
    public function it_validates_location_data_before_caching()
    {
        $agentId = 123;
        
        // Test with invalid coordinates
        $invalidLocation = ['lat' => 91.0, 'lng' => -74.0060]; // Invalid latitude
        
        $this->service->cacheAgentLocation($agentId, $invalidLocation);
        
        // Should still cache (validation happens at application level)
        $cached = $this->service->getCachedAgentLocation($agentId);
        $this->assertNotNull($cached);
    }

    /** @test */
    public function it_respects_cache_ttl()
    {
        $agentId = 123;
        $location = ['lat' => 40.7128, 'lng' => -74.0060];

        // Mock cache to verify TTL is set
        Cache::shouldReceive('put')
            ->once()
            ->with(
                "agent_location:{$agentId}",
                \Mockery::type('array'),
                300 // 5 minutes TTL
            );

        $this->service->cacheAgentLocation($agentId, $location);
    }

    /** @test */
    public function it_warms_up_cache_with_active_agents()
    {
        // Create some test agents
        $agent1 = \Marvel\Database\Models\User::factory()->create();
        $agent2 = \Marvel\Database\Models\User::factory()->create();

        \Marvel\Database\Models\DeliveryAgentProfile::factory()->create([
            'user_id' => $agent1->id,
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
            'current_location' => ['lat' => 40.7128, 'lng' => -74.0060],
        ]);

        \Marvel\Database\Models\DeliveryAgentProfile::factory()->create([
            'user_id' => $agent2->id,
            'availability_status' => 'OFFLINE',
            'kyc_status' => 'APPROVED',
            'current_location' => ['lat' => 40.7130, 'lng' => -74.0062],
        ]);

        $this->service->warmUpCache();

        // Should cache the online agent
        $cached = $this->service->getCachedAgentLocation($agent1->id);
        $this->assertNotNull($cached);
        $this->assertEquals('ONLINE', $cached['status']);
    }
}
