<?php

namespace Marvel\Tests\Unit\Services;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Marvel\Database\Models\User;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\DeliveryAgentProfile;
use Marvel\Services\DeliverySecurityAuditService;
use Marvel\Tests\TestCase;

class DeliverySecurityAuditServiceTest extends TestCase
{
    use RefreshDatabase;

    private DeliverySecurityAuditService $auditService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->auditService = new DeliverySecurityAuditService();
    }

    /** @test */
    public function it_logs_security_events_with_proper_structure()
    {
        $user = User::factory()->create(['email' => '<EMAIL>']);
        
        Log::shouldReceive('channel')
            ->with('security')
            ->once()
            ->andReturnSelf();
            
        Log::shouldReceive('info')
            ->once()
            ->with('Delivery Security Event', \Mockery::on(function ($data) {
                return isset($data['event']) && 
                       isset($data['user_id']) && 
                       isset($data['timestamp']) &&
                       $data['event'] === 'test_event';
            }));

        $this->auditService->logSecurityEvent('test_event', ['test' => 'data'], $user);
    }

    /** @test */
    public function it_detects_rapid_assignment_patterns()
    {
        $assignedBy = User::factory()->create();
        $delivery = Delivery::factory()->create(['assigned_by_user_id' => $assignedBy->id]);

        // Create 11 recent deliveries (should trigger warning at 10+)
        Delivery::factory()->count(11)->create([
            'assigned_by_user_id' => $assignedBy->id,
            'created_at' => now()->subMinutes(3)
        ]);

        $issues = $this->auditService->auditDeliveryAssignment($delivery, $assignedBy);

        $this->assertContains('RAPID_ASSIGNMENT_PATTERN', $issues);
    }

    /** @test */
    public function it_detects_off_hours_assignments()
    {
        $assignedBy = User::factory()->create();
        $delivery = Delivery::factory()->create(['assigned_by_user_id' => $assignedBy->id]);

        // Mock current time to be 2 AM (off hours)
        $this->travelTo(now()->setHour(2));

        $issues = $this->auditService->auditDeliveryAssignment($delivery, $assignedBy);

        $this->assertContains('OFF_HOURS_ASSIGNMENT', $issues);
    }

    /** @test */
    public function it_detects_unusual_delivery_fees()
    {
        $assignedBy = User::factory()->create();
        
        // Test high fee
        $highFeeDelivery = Delivery::factory()->create([
            'assigned_by_user_id' => $assignedBy->id,
            'delivery_fee' => 1500.00
        ]);

        $issues = $this->auditService->auditDeliveryAssignment($highFeeDelivery, $assignedBy);
        $this->assertContains('UNUSUAL_DELIVERY_FEE', $issues);

        // Test negative fee
        $negativeFeeDelivery = Delivery::factory()->create([
            'assigned_by_user_id' => $assignedBy->id,
            'delivery_fee' => -10.00
        ]);

        $issues = $this->auditService->auditDeliveryAssignment($negativeFeeDelivery, $assignedBy);
        $this->assertContains('UNUSUAL_DELIVERY_FEE', $issues);
    }

    /** @test */
    public function it_detects_impossible_movement_speed()
    {
        $agentId = 123;
        $previousLocation = ['lat' => 40.7128, 'lng' => -74.0060]; // NYC
        $newLocation = ['lat' => 34.0522, 'lng' => -118.2437]; // LA (about 2400 miles)

        $issues = $this->auditService->auditLocationUpdate($agentId, $newLocation, $previousLocation);

        $this->assertContains('IMPOSSIBLE_MOVEMENT_SPEED', $issues);
    }

    /** @test */
    public function it_detects_suspicious_coordinate_patterns()
    {
        $agentId = 123;

        // Test repeated digits
        $repeatedDigitsLocation = ['lat' => 11.111111, 'lng' => 22.222222];
        $issues = $this->auditService->auditLocationUpdate($agentId, $repeatedDigitsLocation);
        $this->assertContains('SUSPICIOUS_COORDINATE_PATTERN', $issues);

        // Test round numbers
        $roundNumberLocation = ['lat' => 10.000000, 'lng' => 20.000000];
        $issues = $this->auditService->auditLocationUpdate($agentId, $roundNumberLocation);
        $this->assertContains('SUSPICIOUS_COORDINATE_PATTERN', $issues);

        // Test null island
        $nullIslandLocation = ['lat' => 0.0, 'lng' => 0.0];
        $issues = $this->auditService->auditLocationUpdate($agentId, $nullIslandLocation);
        $this->assertContains('SUSPICIOUS_COORDINATE_PATTERN', $issues);
    }

    /** @test */
    public function it_detects_location_spoofing_indicators()
    {
        $agentId = 123;

        // Test coordinates on degree boundaries
        $boundaryLocation = ['lat' => 40.0, 'lng' => -74.0];
        $issues = $this->auditService->auditLocationUpdate($agentId, $boundaryLocation);
        $this->assertContains('POTENTIAL_LOCATION_SPOOFING', $issues);

        // Test alternating digit patterns
        $alternatingLocation = ['lat' => 12.121212, 'lng' => 34.343434];
        $issues = $this->auditService->auditLocationUpdate($agentId, $alternatingLocation);
        $this->assertContains('POTENTIAL_LOCATION_SPOOFING', $issues);
    }

    /** @test */
    public function it_audits_agent_profile_for_security_issues()
    {
        $user = User::factory()->create();
        
        // Test incomplete KYC
        $incompleteProfile = DeliveryAgentProfile::factory()->create([
            'user_id' => $user->id,
            'kyc_status' => 'PENDING'
        ]);

        $issues = $this->auditService->auditAgentProfile($incompleteProfile);
        $this->assertContains('INCOMPLETE_KYC', $issues);

        // Test missing documents
        $noDocsProfile = DeliveryAgentProfile::factory()->create([
            'user_id' => $user->id,
            'documents' => []
        ]);

        $issues = $this->auditService->auditAgentProfile($noDocsProfile);
        $this->assertContains('MISSING_DOCUMENT_identity', $issues);
        $this->assertContains('MISSING_DOCUMENT_vehicle_registration', $issues);
        $this->assertContains('MISSING_DOCUMENT_license', $issues);
    }

    /** @test */
    public function it_detects_rapid_kyc_approval()
    {
        $user = User::factory()->create();

        $rapidProfile = DeliveryAgentProfile::factory()->create([
            'user_id' => $user->id,
            'kyc_status' => 'APPROVED',
            'created_at' => now()->subHours(2), // Created 2 hours ago (should trigger warning)
        ]);

        $issues = $this->auditService->auditAgentProfile($rapidProfile);
        $this->assertContains('RAPID_KYC_APPROVAL', $issues);
    }

    /** @test */
    public function it_allows_normal_movement_speeds()
    {
        $agentId = 123;
        $previousLocation = ['lat' => 40.7128, 'lng' => -74.0060];
        $newLocation = ['lat' => 40.7130, 'lng' => -74.0062]; // About 200 meters

        $issues = $this->auditService->auditLocationUpdate($agentId, $newLocation, $previousLocation);

        $this->assertNotContains('IMPOSSIBLE_MOVEMENT_SPEED', $issues);
    }

    /** @test */
    public function it_allows_valid_coordinate_patterns()
    {
        $agentId = 123;
        $validLocation = ['lat' => 40.7128456, 'lng' => -74.0059728];

        $issues = $this->auditService->auditLocationUpdate($agentId, $validLocation);

        $this->assertNotContains('SUSPICIOUS_COORDINATE_PATTERN', $issues);
        $this->assertNotContains('POTENTIAL_LOCATION_SPOOFING', $issues);
    }

    /** @test */
    public function it_generates_security_report_structure()
    {
        $report = $this->auditService->generateSecurityReport();

        $this->assertArrayHasKey('period', $report);
        $this->assertArrayHasKey('summary', $report);
        $this->assertArrayHasKey('top_issues', $report);
        $this->assertArrayHasKey('recommendations', $report);

        $this->assertArrayHasKey('start', $report['period']);
        $this->assertArrayHasKey('end', $report['period']);

        $this->assertArrayHasKey('total_events', $report['summary']);
        $this->assertArrayHasKey('high_severity', $report['summary']);
        $this->assertArrayHasKey('medium_severity', $report['summary']);
        $this->assertArrayHasKey('low_severity', $report['summary']);
    }

    /** @test */
    public function it_calculates_distance_correctly()
    {
        // Test known distance between NYC and Philadelphia (about 95 miles / 153 km)
        $reflection = new \ReflectionClass($this->auditService);
        $method = $reflection->getMethod('calculateDistance');
        $method->setAccessible(true);

        $distance = $method->invoke(
            $this->auditService,
            40.7128, -74.0060, // NYC
            39.9526, -75.1652  // Philadelphia
        );

        // Should be approximately 130 km (actual distance is closer to this)
        $this->assertGreaterThan(120, $distance);
        $this->assertLessThan(140, $distance);
    }

    /** @test */
    public function it_handles_null_previous_location_gracefully()
    {
        $agentId = 123;
        $newLocation = ['lat' => 40.7128, 'lng' => -74.0060];

        // Should not throw exception with null previous location
        $issues = $this->auditService->auditLocationUpdate($agentId, $newLocation, null);

        // Should not contain movement speed issues when no previous location
        $this->assertNotContains('IMPOSSIBLE_MOVEMENT_SPEED', $issues);
    }
}
