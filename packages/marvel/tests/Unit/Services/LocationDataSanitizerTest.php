<?php

namespace Marvel\Tests\Unit\Services;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Marvel\Database\Models\User;
use Marvel\Services\LocationDataSanitizer;
use Marvel\Tests\TestCase;
use Marvel\Enums\Permission;
use Spatie\Permission\Models\Permission as PermissionModel;

class LocationDataSanitizerTest extends TestCase
{
    use RefreshDatabase;

    private LocationDataSanitizer $sanitizer;

    protected function setUp(): void
    {
        parent::setUp();
        $this->sanitizer = new LocationDataSanitizer();
        $this->createPermissions();
    }

    /** @test */
    public function it_sanitizes_agent_data_for_public_context()
    {
        $agentData = [
            'id' => 1,
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'delivery_agent_profile' => [
                'current_location' => ['lat' => 40.7128456, 'lng' => -74.0059728],
                'phone_number' => '+**********',
                'emergency_contact' => '<PERSON>',
                'bank_details' => ['account' => '*********'],
            ]
        ];

        $result = $this->sanitizer->sanitizeAgentData($agentData, null, 'public');

        // Should remove location entirely for public context
        $this->assertArrayNotHasKey('current_location', $result['delivery_agent_profile']);
        
        // Should remove sensitive data
        $this->assertArrayNotHasKey('phone_number', $result['delivery_agent_profile']);
        $this->assertArrayNotHasKey('emergency_contact', $result['delivery_agent_profile']);
        $this->assertArrayNotHasKey('bank_details', $result['delivery_agent_profile']);
        
        // Should mask email
        $this->assertEquals('jo**@example.com', $result['email']);
    }

    /** @test */
    public function it_reduces_location_precision_for_tracking_context()
    {
        $user = User::factory()->create();
        $user->givePermissionTo(Permission::CUSTOMER); // Give permission to view location

        $agentData = [
            'id' => 999, // Different from user ID
            'delivery_agent_profile' => [
                'current_location' => ['lat' => 40.**********, 'lng' => -74.**********],
            ]
        ];

        $result = $this->sanitizer->sanitizeAgentData($agentData, $user, 'tracking');

        // Should reduce precision to 3 decimal places
        $this->assertEquals(40.713, $result['delivery_agent_profile']['current_location']['lat']);
        $this->assertEquals(-74.006, $result['delivery_agent_profile']['current_location']['lng']);
    }

    /** @test */
    public function it_allows_precise_location_for_admin_context()
    {
        $admin = User::factory()->create();
        $admin->givePermissionTo(Permission::STORE_OWNER);

        $agentData = [
            'id' => 1,
            'delivery_agent_profile' => [
                'current_location' => ['lat' => 40.**********, 'lng' => -74.**********],
            ]
        ];

        $result = $this->sanitizer->sanitizeAgentData($agentData, $admin, 'admin');

        // Should keep precise location for admin
        $this->assertEquals(40.**********, $result['delivery_agent_profile']['current_location']['lat']);
        $this->assertEquals(-74.**********, $result['delivery_agent_profile']['current_location']['lng']);
    }

    /** @test */
    public function it_allows_agent_to_see_own_precise_location()
    {
        $agent = User::factory()->create();
        $agentData = [
            'id' => $agent->id,
            'delivery_agent_profile' => [
                'current_location' => ['lat' => 40.**********, 'lng' => -74.**********],
            ]
        ];

        $result = $this->sanitizer->sanitizeAgentData($agentData, $agent, 'public');

        // Agent should see their own precise location
        $this->assertEquals(40.**********, $result['delivery_agent_profile']['current_location']['lat']);
    }

    /** @test */
    public function it_sanitizes_location_history_based_on_permissions()
    {
        $user = User::factory()->create();
        $admin = User::factory()->create();
        $admin->givePermissionTo(Permission::SUPER_ADMIN);

        $locationHistory = [
            ['location' => ['lat' => 40.7128456, 'lng' => -74.0059728], 'timestamp' => '2023-01-01'],
            ['location' => ['lat' => 40.7130123, 'lng' => -74.0061234], 'timestamp' => '2023-01-02'],
        ];

        // Regular user should not see history
        $userResult = $this->sanitizer->sanitizeLocationHistory($locationHistory, $user, 'public');
        $this->assertEmpty($userResult);

        // Admin should see precise history in admin context
        $adminResult = $this->sanitizer->sanitizeLocationHistory($locationHistory, $admin, 'admin');
        $this->assertCount(2, $adminResult);
        $this->assertEquals(40.7128456, $adminResult[0]['location']['lat']);
    }

    /** @test */
    public function it_masks_email_addresses_correctly()
    {
        $testCases = [
            '<EMAIL>' => 'jo**@example.com',
            '<EMAIL>' => '<EMAIL>', // Too short to mask
            '<EMAIL>' => '<EMAIL>', // Too short to mask
            '<EMAIL>' => 'ab*@test.com',
            '<EMAIL>' => 've***********@example.com',
        ];

        foreach ($testCases as $input => $expected) {
            $agentData = ['email' => $input, 'delivery_agent_profile' => []];
            $result = $this->sanitizer->sanitizeAgentData($agentData, null, 'public');
            $this->assertEquals($expected, $result['email'], "Failed for email: {$input}");
        }
    }

    /** @test */
    public function it_sanitizes_delivery_data_for_customer_view()
    {
        $customer = User::factory()->create();
        $deliveryData = [
            'id' => 1,
            'status' => 'IN_TRANSIT',
            'delivery_fee' => 5.99,
            'internal_notes' => 'Secret admin notes',
            'delivery_agent' => [
                'id' => 2,
                'name' => 'Agent Smith',
                'email' => '<EMAIL>',
                'delivery_agent_profile' => [
                    'current_location' => ['lat' => 40.7128456, 'lng' => -74.0059728],
                    'phone_number' => '+**********',
                ]
            ]
        ];

        $result = $this->sanitizer->sanitizeDeliveryDataForCustomer($deliveryData, $customer);

        // Should keep allowed fields
        $this->assertEquals(1, $result['id']);
        $this->assertEquals('IN_TRANSIT', $result['status']);
        $this->assertEquals(5.99, $result['delivery_fee']);

        // Should remove internal notes
        $this->assertArrayNotHasKey('internal_notes', $result);

        // Should sanitize agent data
        $this->assertArrayHasKey('delivery_agent', $result);
        $this->assertEquals('Agent Smith', $result['delivery_agent']['name']);
        $this->assertArrayNotHasKey('phone_number', $result['delivery_agent']);
    }

    /** @test */
    public function it_handles_missing_location_data_gracefully()
    {
        $agentData = [
            'id' => 1,
            'delivery_agent_profile' => [
                'availability_status' => 'ONLINE',
                // No current_location
            ]
        ];

        $result = $this->sanitizer->sanitizeAgentData($agentData, null, 'public');

        // Should not crash and should return the data without location
        $this->assertEquals(1, $result['id']);
        $this->assertArrayNotHasKey('current_location', $result['delivery_agent_profile']);
    }

    /** @test */
    public function it_validates_context_permissions_correctly()
    {
        $superAdmin = User::factory()->create();
        $superAdmin->givePermissionTo(Permission::SUPER_ADMIN);

        $storeOwner = User::factory()->create();
        $storeOwner->givePermissionTo(Permission::STORE_OWNER);

        $customer = User::factory()->create();
        $customer->givePermissionTo(Permission::CUSTOMER);

        $agentData = [
            'id' => 999,
            'delivery_agent_profile' => [
                'current_location' => ['lat' => 40.7128456, 'lng' => -74.0059728],
                'phone_number' => '+**********',
            ]
        ];

        // Super admin should see everything
        $superAdminResult = $this->sanitizer->sanitizeAgentData($agentData, $superAdmin, 'admin');
        $this->assertArrayHasKey('phone_number', $superAdminResult['delivery_agent_profile']);
        $this->assertEquals(40.7128456, $superAdminResult['delivery_agent_profile']['current_location']['lat']);

        // Store owner should see precise location but not all sensitive data
        $storeOwnerResult = $this->sanitizer->sanitizeAgentData($agentData, $storeOwner, 'admin');
        $this->assertEquals(40.7128456, $storeOwnerResult['delivery_agent_profile']['current_location']['lat']);

        // Customer should see reduced precision
        $customerResult = $this->sanitizer->sanitizeAgentData($agentData, $customer, 'tracking');
        $this->assertEquals(40.713, $customerResult['delivery_agent_profile']['current_location']['lat']);
        $this->assertArrayNotHasKey('phone_number', $customerResult['delivery_agent_profile']);
    }

    private function createPermissions(): void
    {
        $permissions = [
            Permission::SUPER_ADMIN,
            Permission::STORE_OWNER,
            Permission::DELIVERY_AGENT,
            Permission::CUSTOMER,
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }
    }
}
