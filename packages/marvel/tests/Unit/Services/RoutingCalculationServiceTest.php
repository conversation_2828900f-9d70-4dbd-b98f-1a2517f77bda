<?php

namespace Marvel\Tests\Unit\Services;

use Marvel\Services\RoutingCalculationService;
use Tests\TestCase;

class RoutingCalculationServiceTest extends TestCase
{
    private RoutingCalculationService $service;

    public function setUp(): void
    {
        parent::setUp();
        $this->service = new RoutingCalculationService();
    }

    /** @test */
    public function it_calculates_distance_correctly()
    {
        // Test with known coordinates (New York to Boston)
        $nyLat = 40.7128;
        $nyLng = -74.0060;
        $bostonLat = 42.3601;
        $bostonLng = -71.0589;

        $distance = $this->service->calculateDistance($nyLat, $nyLng, $bostonLat, $bostonLng);

        // The distance should be approximately 306km
        $this->assertGreaterThan(300, $distance);
        $this->assertLessThan(310, $distance);
    }

    /** @test */
    public function it_calculates_small_distances_correctly()
    {
        // Test with very close coordinates (few blocks in Manhattan)
        $point1Lat = 40.7128;
        $point1Lng = -74.0060;
        $point2Lat = 40.7138;
        $point2Lng = -74.0070;

        $distance = $this->service->calculateDistance($point1Lat, $point1Lng, $point2Lat, $point2Lng);

        // The distance should be less than 1km
        $this->assertLessThan(1, $distance);
        $this->assertGreaterThan(0, $distance);
    }

    /** @test */
    public function it_calculates_bounding_box_correctly()
    {
        $centerLat = 40.7128;
        $centerLng = -74.0060;
        $radiusKm = 10;

        $bbox = $this->service->calculateBoundingBox($centerLat, $centerLng, $radiusKm);

        // Validate bbox structure
        $this->assertArrayHasKey('minLat', $bbox);
        $this->assertArrayHasKey('maxLat', $bbox);
        $this->assertArrayHasKey('minLng', $bbox);
        $this->assertArrayHasKey('maxLng', $bbox);

        // Verify bbox size (rough approximation)
        $latDiff = $bbox['maxLat'] - $bbox['minLat'];
        $lngDiff = $bbox['maxLng'] - $bbox['minLng'];

        // At this latitude, 1 degree is roughly 111km for latitude and ~85km for longitude
        // So for a 10km radius, we expect roughly 0.18 degrees difference for lat and 0.24 for lng
        $this->assertGreaterThan(0.15, $latDiff);
        $this->assertLessThan(0.2, $latDiff);
        $this->assertGreaterThan(0.2, $lngDiff);
        $this->assertLessThan(0.3, $lngDiff);
    }

    /** @test */
    public function it_calculates_central_point_correctly()
    {
        $locations = [
            ['lat' => 34.0522, 'lng' => -118.2437], // Los Angeles
            ['lat' => 34.0550, 'lng' => -118.2450], // Very close to LA
        ];

        $centralPoint = $this->service->calculateCentralPoint($locations);

        $this->assertArrayHasKey('lat', $centralPoint);
        $this->assertArrayHasKey('lng', $centralPoint);

        // The central point should be between the two points
        $this->assertGreaterThan(min(34.0522, 34.0550), $centralPoint['lat']);
        $this->assertLessThan(max(34.0522, 34.0550), $centralPoint['lat']);
        $this->assertGreaterThan(min(-118.2450, -118.2437), $centralPoint['lng']);
        $this->assertLessThan(max(-118.2450, -118.2437), $centralPoint['lng']);
    }

    /** @test */
    public function it_returns_null_for_empty_locations()
    {
        $this->assertNull($this->service->calculateCentralPoint([]));
    }

    /** @test */
    public function it_returns_single_point_as_central_point()
    {
        $locations = [
            ['lat' => 34.0522, 'lng' => -118.2437]
        ];

        $centralPoint = $this->service->calculateCentralPoint($locations);

        $this->assertEquals(34.0522, $centralPoint['lat']);
        $this->assertEquals(-118.2437, $centralPoint['lng']);
    }

    /** @test */
    public function it_calculates_max_distance_between_shops_correctly()
    {
        $locations = [
            ['lat' => 34.0522, 'lng' => -118.2437], // Los Angeles
            ['lat' => 37.7749, 'lng' => -122.4194], // San Francisco
            ['lat' => 34.0550, 'lng' => -118.2450], // Very close to LA
        ];

        $maxDistance = $this->service->calculateMaxDistanceBetweenShops($locations);

        // The distance between LA and SF should be around 560km
        $this->assertGreaterThan(550, $maxDistance);
        $this->assertLessThan(570, $maxDistance);
    }

    /** @test */
    public function it_returns_zero_for_single_shop()
    {
        $locations = [
            ['lat' => 34.0522, 'lng' => -118.2437]
        ];

        $maxDistance = $this->service->calculateMaxDistanceBetweenShops($locations);

        $this->assertEquals(0, $maxDistance);
    }

    /** @test */
    public function it_returns_zero_for_empty_locations()
    {
        $maxDistance = $this->service->calculateMaxDistanceBetweenShops([]);

        $this->assertEquals(0, $maxDistance);
    }
}
