<?php

namespace Marvel\Tests\Unit\Services;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\User;
use Marvel\Enums\Permission;
use Marvel\Exceptions\DeliveryException;
use Marvel\Services\AgentDiscoveryService;
use Marvel\Services\RoutingCalculationService;
use Illuminate\Support\Facades\Notification;
use Spatie\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class AgentDiscoveryServiceTest extends TestCase
{
    use RefreshDatabase;

    protected AgentDiscoveryService $service;
    protected User $admin;
    protected User $agent;
    protected Order $order;

    public function setUp(): void
    {
        parent::setUp();

        Notification::fake();

        // Instantiate the service and its dependencies
        $routingService = new RoutingCalculationService();
        $this->service = new AgentDiscoveryService($routingService);
        
        $this->createPermissions();

        // FIX: Reverted to manual model creation from the original working test to avoid factory issues.
        $this->admin = new User([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->admin->save();
        $this->admin->givePermissionTo(Permission::SUPER_ADMIN);

        $this->agent = new User([
            'name' => 'Delivery Agent',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->agent->save();
        $this->agent->givePermissionTo(Permission::DELIVERY_AGENT);
        $this->agent->delivery_agent_profile()->create([
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
        ]);

        $shop = new Shop([
            'name' => 'Test Shop',
            'slug' => 'test-shop',
            'owner_id' => $this->admin->id,
            'is_active' => true,
            'settings' => [
                'location' => ['lat' => 40.7128, 'lng' => -74.0060, 'city' => 'New York', 'country' => 'USA']
            ]
        ]);
        $shop->save();

        $this->order = new Order([
            'tracking_number' => 'ORDER-' . rand(10000, 99999),
            'customer_id' => $this->admin->id,
            'shop_id' => $shop->id,
            'order_status' => 'order-processing',
            'payment_status' => 'payment-success',
            'amount' => 100.00,
            'requires_delivery' => true,
            'delivery_id' => null,
            'customer_contact' => '1234567890', // This was missing and is required
        ]);
        $this->order->save();
    }

    /** @test */
    public function it_can_find_nearest_available_agents()
    {
        $nearbyAgent = $this->createDeliveryAgentWithLocation(['lat' => 40.7138, 'lng' => -74.0070]); // ~0.15km away
        $farAgent = $this->createDeliveryAgentWithLocation(['lat' => 40.8500, 'lng' => -73.8000]); // ~25km away
        $this->agent->delivery_agent_profile->update(['current_location' => ['lat' => 40.7200, 'lng' => -74.0000]]); // ~0.9km away

        $nearestAgents = $this->service->findNearestAvailableAgents($this->order, 15);

        $this->assertCount(2, $nearestAgents);
        $this->assertEquals($nearbyAgent->id, $nearestAgents->first()->id);
        $this->assertEquals($this->agent->id, $nearestAgents->get(1)->id);
        $this->assertTrue($nearestAgents->first()->distance < $nearestAgents->get(1)->distance);
    }

    /** @test */
    public function it_filters_agents_by_availability_status()
    {
        $this->agent->delivery_agent_profile->update(['availability_status' => 'OFFLINE', 'current_location' => ['lat' => 40.7118, 'lng' => -74.0050]]);
        $onlineAgent = $this->createDeliveryAgentWithLocation(['lat' => 40.7138, 'lng' => -74.0070]);

        $nearestAgents = $this->service->findNearestAvailableAgents($this->order);

        $this->assertCount(1, $nearestAgents);
        $this->assertEquals($onlineAgent->id, $nearestAgents->first()->id);
    }

    /** @test */
    public function it_handles_missing_shop_location_gracefully()
    {
        $this->order->shop->settings = null;
        $this->order->shop->save();
        $this->expectException(DeliveryException::class);
        $this->service->findNearestAvailableAgents($this->order);
    }

    /** @test */
    public function it_accepts_order_id_as_parameter()
    {
        $this->agent->delivery_agent_profile->update(['current_location' => ['lat' => 40.7118, 'lng' => -74.0050]]);
        $nearestAgents = $this->service->findNearestAvailableAgents($this->order->id);
        $this->assertCount(1, $nearestAgents);
        $this->assertEquals($this->agent->id, $nearestAgents->first()->id);
    }

    /** @test */
    public function it_calculates_distance_correctly()
    {
        $this->agent->delivery_agent_profile->update(['current_location' => ['lat' => 42.3601, 'lng' => -71.0589]]); // Boston
        $nearestAgents = $this->service->findNearestAvailableAgents($this->order, 15);
        $this->assertCount(0, $nearestAgents);
    }

    /** @test */
    public function it_filters_agents_without_location()
    {
        // agent from setUp has no location by default
        $nearestAgents = $this->service->findNearestAvailableAgents($this->order);
        $this->assertCount(0, $nearestAgents);
    }

    /** @test */
    public function it_filters_agents_with_rejected_kyc()
    {
        $this->createDeliveryAgentWithLocation(['lat' => 40.7138, 'lng' => -74.0070], 'REJECTED');
        $this->agent->delivery_agent_profile->update(['current_location' => ['lat' => 40.7118, 'lng' => -74.0050]]);
        $nearestAgents = $this->service->findNearestAvailableAgents($this->order);
        $this->assertCount(1, $nearestAgents);
        $this->assertEquals($this->agent->id, $nearestAgents->first()->id);
    }

    /** @test */
    public function it_filters_inactive_agents()
    {
        $this->agent->is_active = false;
        $this->agent->save();
        $activeAgent = $this->createDeliveryAgentWithLocation(['lat' => 40.7138, 'lng' => -74.0070]);
        $nearestAgents = $this->service->findNearestAvailableAgents($this->order);
        $this->assertCount(1, $nearestAgents);
        $this->assertEquals($activeAgent->id, $nearestAgents->first()->id);
    }

    /** @test */
    public function it_returns_empty_collection_if_no_agents_found()
    {
        $this->agent->is_active = false;
        $this->agent->save();
        $nearestAgents = $this->service->findNearestAvailableAgents($this->order);
        $this->assertCount(0, $nearestAgents);
        $this->assertTrue($nearestAgents->isEmpty());
    }

    /** @test */
    public function it_throws_exception_if_order_does_not_require_delivery()
    {
        $this->order->requires_delivery = false;
        $this->order->save();
        $this->expectException(DeliveryException::class);
        $this->expectExceptionMessage('Order does not require delivery.');
        $this->service->findNearestAvailableAgents($this->order);
    }

    /** @test */
    public function it_can_find_nearest_agents_for_consolidated_multi_vendor_order()
    {
        User::permission(Permission::DELIVERY_AGENT)->delete();
        $parentOrder = $this->createOrderForShop(null);
        $shop1 = $this->createShopWithLocation(['lat' => 34.0522, 'lng' => -118.2437]);
        $this->createOrderForShop($shop1, $parentOrder->id);
        $shop2 = $this->createShopWithLocation(['lat' => 34.0550, 'lng' => -118.2450]);
        $this->createOrderForShop($shop2, $parentOrder->id);
        $agent = $this->createDeliveryAgentWithLocation(['lat' => 34.0530, 'lng' => -118.2440]);

        config(['delivery.max_consolidation_distance' => 5, 'delivery.max_shop_to_center_distance' => 5]);
        $parentOrder->load('children.shop');

        $result = $this->service->findNearestAgentsForMultiVendor($parentOrder);

        $this->assertTrue($result['should_consolidate']);
        $this->assertNotEmpty($result['agents']);
        $this->assertEquals($agent->id, $result['agents']->first()->id);
    }

    /** @test */
    public function it_can_find_nearest_agents_for_split_multi_vendor_order()
    {
        $parentOrder = $this->createOrderForShop(null);
        $shop1 = $this->createShopWithLocation(['lat' => 34.0522, 'lng' => -118.2437]); // LA
        $this->createOrderForShop($shop1, $parentOrder->id);
        $shop2 = $this->createShopWithLocation(['lat' => 37.7749, 'lng' => -122.4194]); // SF
        $this->createOrderForShop($shop2, $parentOrder->id);
        $agent1 = $this->createDeliveryAgentWithLocation(['lat' => 34.0530, 'lng' => -118.2440]); // Near LA
        $agent2 = $this->createDeliveryAgentWithLocation(['lat' => 37.7750, 'lng' => -122.4200]); // Near SF

        config(['delivery.max_consolidation_distance' => 1, 'delivery.max_shop_to_center_distance' => 1]);
        $parentOrder->load('children.shop');

        $result = $this->service->findNearestAgentsForMultiVendor($parentOrder);

        $this->assertFalse($result['should_consolidate']);
        $agentsByShop = $result['agents'];
        $this->assertArrayHasKey($shop1->id, $agentsByShop);
        $this->assertArrayHasKey($shop2->id, $agentsByShop);
        $this->assertEquals($agent1->id, $agentsByShop[$shop1->id]->first()->id);
        $this->assertEquals($agent2->id, $agentsByShop[$shop2->id]->first()->id);
    }

    /** @test */
    public function it_can_find_agent_for_parent_order_with_a_single_child()
    {
        $shopLocation = ['lat' => 4.0432672, 'lng' => 9.7132527];
        $agentLocation = ['lat' => 4.0752566, 'lng' => 9.7912631];
        $parentOrder = $this->createOrderForShop(null);
        $shop = $this->createShopWithLocation($shopLocation);
        $this->createOrderForShop($shop, $parentOrder->id);
        $agent = $this->createDeliveryAgentWithLocation($agentLocation);
        $parentOrder->load('children.shop');

        $result = $this->service->findNearestAgentsForMultiVendor($parentOrder, 15);

        $this->assertCount(1, $result['agents']);
        $this->assertEquals($agent->id, $result['agents']->first()->id);
        $this->assertFalse($result['should_consolidate']);
    }

    // --- Helper methods copied from DeliveryRepositoryTest to ensure identical test data setup ---
    
    protected function createPermissions()
    {
        $adminRole = Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'api']);
        $agentRole = Role::firstOrCreate(['name' => 'delivery_agent', 'guard_name' => 'api']);
        $permissions = [
            Permission::SUPER_ADMIN, Permission::STORE_OWNER, Permission::STAFF,
            Permission::CUSTOMER, Permission::DELIVERY_AGENT,
        ];
        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }
        if (!$adminRole->hasPermissionTo(Permission::SUPER_ADMIN)) {
            $adminRole->givePermissionTo(Permission::SUPER_ADMIN);
        }
        if (!$agentRole->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            $agentRole->givePermissionTo(Permission::DELIVERY_AGENT);
        }
    }

    protected function createShopWithLocation(array $location): Shop
    {
        $shop = new Shop([
            'name' => 'Shop ' . uniqid(), 'slug' => 'shop-' . uniqid(),
            'owner_id' => $this->admin->id, 'is_active' => true,
            'settings' => ['location' => $location]
        ]);
        $shop->save();
        return $shop;
    }

    protected function createOrderForShop(?Shop $shop = null, ?int $parentId = null): Order
    {
        $order = new Order([
            'tracking_number' => 'ORDER-' . rand(10000, 99999),
            'customer_id' => $this->admin->id, 'shop_id' => $shop ? $shop->id : null,
            'order_status' => 'order-processing', 'payment_status' => 'payment-success',
            'amount' => 50.00, 'requires_delivery' => true, 'delivery_id' => null,
            'customer_contact' => '1234567890', 'parent_id' => $parentId,
        ]);
        $order->save();
        return $order;
    }

    protected function createDeliveryAgentWithLocation(array $location, string $kycStatus = 'APPROVED', string $availabilityStatus = 'ONLINE'): User
    {
        $agent = new User([
            'name' => 'Agent ' . uniqid(), 'email' => 'agent' . uniqid() . '@example.com',
            'password' => bcrypt('password'), 'is_active' => true,
        ]);
        $agent->save();
        $agent->givePermissionTo(Permission::DELIVERY_AGENT);
        $agent->delivery_agent_profile()->create([
            'availability_status' => $availabilityStatus, 'kyc_status' => $kycStatus,
            'current_location' => $location,
        ]);
        return $agent;
    }
}