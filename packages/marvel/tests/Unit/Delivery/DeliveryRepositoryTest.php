<?php

namespace Marvel\Tests\Unit\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\User;
use Marvel\Database\Repositories\DeliveryRepository;
use Marvel\Enums\Permission;
use Marvel\Exceptions\MarvelException;
use Marvel\Exceptions\DeliveryException;
use Illuminate\Support\Facades\Notification;
use Spatie\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class DeliveryRepositoryTest extends TestCase
{
    use RefreshDatabase;

    protected $repository;
    protected $admin;
    protected $agent;
    protected $order;

    public function setUp(): void
    {
        parent::setUp();

        Notification::fake(); // Prevent notifications from being sent during tests

        $this->repository = app(DeliveryRepository::class);

        // Create permissions
        $this->createPermissions();

        // Create admin user
        $this->admin = new User([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->admin->save();
        $this->admin->givePermissionTo(Permission::SUPER_ADMIN);

        // Create delivery agent
        $this->agent = new User([
            'name' => 'Delivery Agent',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->agent->save();
        $this->agent->givePermissionTo(Permission::DELIVERY_AGENT);

        // Create agent profile with approved KYC
        $this->agent->delivery_agent_profile()->create([
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
        ]);

        // Create a shop
        $shop = new Shop([
            'name' => 'Test Shop',
            'slug' => 'test-shop',
            'owner_id' => $this->admin->id,
            'is_active' => true,
            'settings' => [
                'location' => [
                    'lat' => 40.7128,
                    'lng' => -74.0060,
                    'city' => 'New York',
                    'country' => 'USA'
                ]
            ]
        ]);
        $shop->save();

        // Create an order
        $this->order = new Order([
            'tracking_number' => 'ORDER-' . rand(10000, 99999),
            'customer_id' => $this->admin->id,
            'shop_id' => $shop->id,
            'order_status' => 'order-processing',
            'payment_status' => 'payment-success',
            'amount' => 100.00,
            'requires_delivery' => true,
            'delivery_id' => null,
            'customer_contact' => '1234567890', // Added to satisfy NOT NULL constraint
        ]);
        $this->order->save();
    }

    /** @test */
    public function it_can_assign_delivery_to_agent()
    {
        $data = [
            'delivery_agent_user_id' => $this->agent->id,
            'delivery_fee' => 10.00,
            'notes' => 'Test delivery',
        ];

        $delivery = $this->repository->assignDelivery($data, $this->order, $this->admin);

        $this->assertInstanceOf(Delivery::class, $delivery);
        $this->assertEquals($this->agent->id, $delivery->delivery_agent_user_id);
        $this->assertEquals('ASSIGNED', $delivery->status);
        $this->assertEquals(10.00, $delivery->delivery_fee);

        // Check if order was updated
        $this->order->refresh();
        $this->assertEquals($delivery->id, $this->order->delivery_id);

        // Check if status log was created
        $this->assertDatabaseHas('delivery_status_logs', [
            'delivery_id' => $delivery->id,
            'status' => 'ASSIGNED',
        ]);
    }

    /** @test */
    public function it_cannot_assign_delivery_to_agent_with_unapproved_kyc()
    {
        // Update agent profile to have unapproved KYC
        $this->agent->delivery_agent_profile->update([
            'kyc_status' => 'PENDING',
        ]);

        $data = [
            'delivery_agent_user_id' => $this->agent->id,
            'delivery_fee' => 10.00,
        ];

        $this->expectException(DeliveryException::class);

        $this->repository->assignDelivery($data, $this->order, $this->admin);
    }

    /** @test */
    public function it_can_update_delivery_status()
    {
        // First create a delivery
        $data = [
            'delivery_agent_user_id' => $this->agent->id,
            'delivery_fee' => 10.00,
        ];

        $delivery = $this->repository->assignDelivery($data, $this->order, $this->admin);

        // Now update the status
        $updatedDelivery = $this->repository->updateDeliveryStatus(
            $delivery,
            'ACCEPTED_BY_AGENT',
            $this->agent,
            ['notes' => 'Accepted by agent']
        );

        $this->assertEquals('ACCEPTED_BY_AGENT', $updatedDelivery->status);
        $this->assertNotNull($updatedDelivery->accepted_at);

        // Check if status log was created
        $this->assertDatabaseHas('delivery_status_logs', [
            'delivery_id' => $delivery->id,
            'status' => 'ACCEPTED_BY_AGENT',
            'notes' => 'Accepted by agent',
        ]);
    }

    /** @test */
    public function it_validates_status_transitions()
    {
        // First create a delivery
        $data = [
            'delivery_agent_user_id' => $this->agent->id,
            'delivery_fee' => 10.00,
        ];

        $delivery = $this->repository->assignDelivery($data, $this->order, $this->admin);

        // Try to update with invalid status transition
        $this->expectException(DeliveryException::class);

        $this->repository->updateDeliveryStatus(
            $delivery,
            'DELIVERED', // Cannot go directly from ASSIGNED to DELIVERED
            $this->agent
        );
    }

/** @test */
    public function it_cannot_assign_delivery_if_order_already_has_delivery()
    {
        // Assign a delivery first
        $data = [
            'delivery_agent_user_id' => $this->agent->id,
            'delivery_fee' => 10.00,
        ];
        $this->repository->assignDelivery($data, $this->order, $this->admin);

        // Try to assign again
        $this->expectException(DeliveryException::class);
        $this->repository->assignDelivery($data, $this->order, $this->admin);
    }

    /** @test */
    public function it_cannot_assign_delivery_to_non_existent_agent()
    {
        $data = [
            'delivery_agent_user_id' => 99999, // Non-existent user ID
            'delivery_fee' => 10.00,
        ];

        $this->expectException(DeliveryException::class);
        $this->repository->assignDelivery($data, $this->order, $this->admin);
    }

    /** @test */
    public function it_can_assign_delivery_with_null_notes()
    {
        $data = [
            'delivery_agent_user_id' => $this->agent->id,
            'delivery_fee' => 10.00,
            'notes' => null,
        ];

        $delivery = $this->repository->assignDelivery($data, $this->order, $this->admin);

        $this->assertInstanceOf(Delivery::class, $delivery);
        $this->assertNull($delivery->notes_by_admin);
    }

    /** @test */
    public function it_can_transition_delivery_status_from_assigned_to_accepted()
    {
        $delivery = $this->repository->assignDelivery([
            'delivery_agent_user_id' => $this->agent->id,
            'delivery_fee' => 10.00,
        ], $this->order, $this->admin);

        $updatedDelivery = $this->repository->updateDeliveryStatus($delivery, 'ACCEPTED_BY_AGENT', $this->agent);
        $this->assertEquals('ACCEPTED_BY_AGENT', $updatedDelivery->status);
    }

    /** @test */
    public function it_can_transition_delivery_status_from_accepted_to_picked_up()
    {
        $delivery = $this->repository->assignDelivery([
            'delivery_agent_user_id' => $this->agent->id,
            'delivery_fee' => 10.00,
        ], $this->order, $this->admin);
        $delivery = $this->repository->updateDeliveryStatus($delivery, 'ACCEPTED_BY_AGENT', $this->agent);

        $updatedDelivery = $this->repository->updateDeliveryStatus($delivery, 'PICKED_UP', $this->agent);
        $this->assertEquals('PICKED_UP', $updatedDelivery->status);
    }

    /** @test */
    public function it_can_transition_delivery_status_from_picked_up_to_delivered()
    {
        $delivery = $this->repository->assignDelivery([
            'delivery_agent_user_id' => $this->agent->id,
            'delivery_fee' => 10.00,
        ], $this->order, $this->admin);
        $delivery = $this->repository->updateDeliveryStatus($delivery, 'ACCEPTED_BY_AGENT', $this->agent);
        $delivery = $this->repository->updateDeliveryStatus($delivery, 'PICKED_UP', $this->agent);
        $delivery = $this->repository->updateDeliveryStatus($delivery, 'IN_TRANSIT', $this->agent);
        $delivery = $this->repository->updateDeliveryStatus($delivery, 'REACHED_DESTINATION', $this->agent);

        $updatedDelivery = $this->repository->updateDeliveryStatus($delivery, 'DELIVERED', $this->agent);
        $this->assertEquals('DELIVERED', $updatedDelivery->status);
        $this->assertNotNull($updatedDelivery->delivered_at);
    }

    /** @test */
    public function it_can_transition_delivery_status_to_cancelled()
    {
        $delivery = $this->repository->assignDelivery([
            'delivery_agent_user_id' => $this->agent->id,
            'delivery_fee' => 10.00,
        ], $this->order, $this->admin);

        $updatedDelivery = $this->repository->updateDeliveryStatus($delivery, 'CANCELLED', $this->admin, ['reason' => 'Test cancellation']);
        $this->assertEquals('CANCELLED', $updatedDelivery->status);
        $this->assertNotNull($updatedDelivery->cancelled_at);
    }

    /** @test */
    public function it_can_update_delivery_status_with_null_notes()
    {
        $delivery = $this->repository->assignDelivery([
            'delivery_agent_user_id' => $this->agent->id,
            'delivery_fee' => 10.00,
        ], $this->order, $this->admin);

        $updatedDelivery = $this->repository->updateDeliveryStatus($delivery, 'ACCEPTED_BY_AGENT', $this->agent, ['notes' => null]);
        $this->assertEquals('ACCEPTED_BY_AGENT', $updatedDelivery->status);
        $this->assertDatabaseHas('delivery_status_logs', [
            'delivery_id' => $delivery->id,
            'status' => 'ACCEPTED_BY_AGENT',
            'notes' => null,
        ]);
    }

    /** @test */
    public function it_throws_exception_for_invalid_delivery_on_status_update()
    {
        $this->expectException(DeliveryException::class);
        $this->repository->updateDeliveryStatus(new Delivery(), 'ACCEPTED_BY_AGENT', $this->agent);
    }

    /**
     * Create permissions needed for testing.
     *
     * @return void
     */
    protected function createPermissions()
    {
        // Create roles if they don't exist
        $adminRole = Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'api']);
        $agentRole = Role::firstOrCreate(['name' => 'delivery_agent', 'guard_name' => 'api']);

        // Create permissions if they don't exist
        $permissions = [
            Permission::SUPER_ADMIN,
            Permission::STORE_OWNER,
            Permission::STAFF,
            Permission::CUSTOMER,
            Permission::DELIVERY_AGENT,
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }

        // Assign permissions to roles if not already assigned
        if (!$adminRole->hasPermissionTo(Permission::SUPER_ADMIN)) {
            $adminRole->givePermissionTo(Permission::SUPER_ADMIN);
        }

        if (!$agentRole->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            $agentRole->givePermissionTo(Permission::DELIVERY_AGENT);
        }
    }

    /**
     * Helper to create a shop with a specific location.
     *
     * @param array $location
     * @return Shop
     */
    protected function createShopWithLocation(array $location): Shop
    {
        $shop = new Shop([
            'name' => 'Shop ' . uniqid(),
            'slug' => 'shop-' . uniqid(),
            'owner_id' => $this->admin->id,
            'is_active' => true,
            'settings' => [
                'location' => $location
            ]
        ]);
        $shop->save();
        return $shop;
    }

    /**
     * Helper to create an order for a given shop.
     *
     * @param Shop $shop
     * @param int|null $parentId
     * @return Order
     */
    protected function createOrderForShop(?Shop $shop = null, ?int $parentId = null): Order
    {
        $order = new Order([
            'tracking_number' => 'ORDER-' . rand(10000, 99999),
            'customer_id' => $this->admin->id,
            'shop_id' => $shop ? $shop->id : null,
            'order_status' => 'order-processing',
            'payment_status' => 'payment-success',
            'amount' => 50.00,
            'requires_delivery' => true,
            'delivery_id' => null,
            'customer_contact' => '1234567890',
            'parent_id' => $parentId,
        ]);
        $order->save();
        return $order;
    }

    /**
     * Helper to create a delivery agent with a specific location.
     *
     * @param array $location
     * @param string $kycStatus
     * @param string $availabilityStatus
     * @return User
     */
    protected function createDeliveryAgentWithLocation(array $location, string $kycStatus = 'APPROVED', string $availabilityStatus = 'ONLINE'): User
    {
        $agent = new User([
            'name' => 'Agent ' . uniqid(),
            'email' => 'agent' . uniqid() . '@example.com',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $agent->save();
        $agent->givePermissionTo(Permission::DELIVERY_AGENT);
        $agent->delivery_agent_profile()->create([
            'availability_status' => $availabilityStatus,
            'kyc_status' => $kycStatus,
            'current_location' => $location,
        ]);
        return $agent;
    }
}