<?php

namespace Marvel\Tests\Unit\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\User;
use Marvel\Database\Models\DeliveryAgentProfile;
use Marvel\Database\Models\DeliveryAgentAssignmentState;
use Marvel\Database\Repositories\DeliveryRepository;
use Marvel\Enums\Permission;
use Marvel\Tests\DeliveryTestCase;
use Marvel\Database\Models\Shop;
use Spatie\Permission\Models\Permission as PermissionModel;

class AutoAssignmentRaceConditionTest extends DeliveryTestCase
{
    use RefreshDatabase;

    protected DeliveryRepository $deliveryRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->deliveryRepository = app(DeliveryRepository::class);
    }

    /**
     * Test that concurrent assignment attempts don't create duplicate deliveries
     */
    public function test_concurrent_assignment_prevention()
    {
        // Create a test order
        $order = $this->createTestOrder();
        
        // Create a test agent
        $agent = $this->createTestAgent();

        // Simulate concurrent assignment attempts
        $results = [];
        $exceptions = [];

        // Use database transactions to simulate race conditions
        for ($i = 0; $i < 3; $i++) {
            try {
                DB::beginTransaction();
                
                $delivery = $this->deliveryRepository->assignDelivery([
                    'delivery_agent_user_id' => $agent->id,
                    'delivery_fee' => 10.0,
                    'assignment_type' => 'AUTOMATIC',
                ], $order, $agent);
                
                $results[] = $delivery;
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                $exceptions[] = $e->getMessage();
            }
        }

        // Only one assignment should succeed
        $this->assertCount(1, $results, 'Only one assignment should succeed');
        $this->assertGreaterThan(0, count($exceptions), 'Some assignments should fail due to race condition prevention');
        
        // Verify order has exactly one delivery
        $order->refresh();
        $this->assertNotNull($order->delivery_id, 'Order should have a delivery assigned');
        
        // Verify only one delivery record exists for this order
        $deliveryCount = DB::table('deliveries')->where('order_id', $order->id)->count();
        $this->assertEquals(1, $deliveryCount, 'Only one delivery should exist for the order');
    }

    /**
     * Test round-robin agent selection works correctly
     */
    public function test_round_robin_agent_selection()
    {
        // Create multiple agents
        $agents = collect();
        for ($i = 0; $i < 3; $i++) {
            $agents->push($this->createTestAgent());
        }

        // Test round-robin selection
        $repository = new \ReflectionClass($this->deliveryRepository);
        $method = $repository->getMethod('selectAgentRoundRobin');
        $method->setAccessible(true);

        $selections = [];
        for ($i = 0; $i < 6; $i++) {
            $selected = $method->invoke($this->deliveryRepository, $agents);
            $selections[] = $selected->id;
        }

        // Verify round-robin behavior - each agent should be selected twice
        $agentCounts = array_count_values($selections);
        foreach ($agents as $agent) {
            $this->assertEquals(2, $agentCounts[$agent->id] ?? 0, 
                "Agent {$agent->id} should be selected exactly twice in round-robin");
        }
    }

    /**
     * Test agent availability checking
     */
    public function test_agent_availability_checking()
    {
        $agent = $this->createTestAgent();
        
        // Test with online agent
        $repository = new \ReflectionClass($this->deliveryRepository);
        $method = $repository->getMethod('isAgentStillAvailable');
        $method->setAccessible(true);
        
        $this->assertTrue($method->invoke($this->deliveryRepository, $agent), 
            'Online agent should be available');

        // Set agent offline
        $agent->delivery_agent_profile->availability_status = 'OFFLINE';
        $agent->delivery_agent_profile->save();
        
        $this->assertFalse($method->invoke($this->deliveryRepository, $agent), 
            'Offline agent should not be available');
    }

    private function createTestOrder(): Order
    {
        $shop = Shop::factory()->create();
        
        return Order::factory()->create([
            'shop_id' => $shop->id,
            'requires_delivery' => true,
            'delivery_id' => null,
        ]);
    }

    private function createTestAgent(): User
    {
        $agent = User::factory()->create([
            'is_active' => true,
        ]);
        
        $agent->givePermissionTo(Permission::DELIVERY_AGENT);
        
        DeliveryAgentProfile::factory()->create([
            'user_id' => $agent->id,
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
            'current_location' => ['lat' => 40.7128, 'lng' => -74.0060],
        ]);

        return $agent;
    }
}
