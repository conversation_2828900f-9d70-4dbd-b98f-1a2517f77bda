<?php

namespace Marvel\Tests\Unit\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\User;
use Marvel\Database\Models\DeliveryAgentProfile;
use Marvel\Database\Models\DeliveryAgentAssignmentState;
use Marvel\Database\Repositories\DeliveryRepository;
use Marvel\Enums\Permission;
use Marvel\Tests\TestCase;
use Marvel\Database\Models\Shop;
use Spatie\Permission\Models\Permission as PermissionModel;

class AutoAssignmentImprovementsTest extends TestCase
{
    use RefreshDatabase;

    protected DeliveryRepository $deliveryRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->deliveryRepository = app(DeliveryRepository::class);
        
        // Create necessary permissions
        $this->createPermissions();
    }

    /**
     * Test that database transactions prevent race conditions
     */
    public function test_database_transactions_prevent_race_conditions()
    {
        $order = $this->createTestOrder();
        $agent1 = $this->createTestAgent();
        $agent2 = $this->createTestAgent();

        $successCount = 0;
        $failureCount = 0;

        // Simulate concurrent assignments
        for ($i = 0; $i < 3; $i++) {
            try {
                $delivery = $this->deliveryRepository->assignDelivery([
                    'delivery_agent_user_id' => $i % 2 === 0 ? $agent1->id : $agent2->id,
                    'delivery_fee' => 10.0,
                    'assignment_type' => 'AUTOMATIC',
                ], $order, $agent1);
                
                if ($delivery) {
                    $successCount++;
                }
            } catch (\Exception $e) {
                $failureCount++;
            }
        }

        // Only one assignment should succeed
        $this->assertEquals(1, $successCount, 'Only one assignment should succeed');
        $this->assertGreaterThan(0, $failureCount, 'Some assignments should fail due to race condition prevention');

        // Verify order has exactly one delivery
        $order->refresh();
        $this->assertNotNull($order->delivery_id, 'Order should have a delivery assigned');
    }

    /**
     * Test that system user is created with limited privileges
     */
    public function test_system_user_has_limited_privileges()
    {
        $order = $this->createTestOrder();
        $agent = $this->createTestAgent();

        // Trigger assignment which should create system user (pass null to trigger system user creation)
        $this->deliveryRepository->assignDelivery([
            'delivery_agent_user_id' => $agent->id,
            'delivery_fee' => 10.0,
            'assignment_type' => 'AUTOMATIC',
        ], $order, null);

        // Verify system user was created
        $systemUser = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($systemUser, 'System user should be created');

        // Verify system user has only delivery system permission
        $permissions = $systemUser->permissions->pluck('name')->toArray();
        $this->assertContains(Permission::DELIVERY_SYSTEM, $permissions, 
            'System user should have delivery system permission');
        $this->assertNotContains(Permission::SUPER_ADMIN, $permissions, 
            'System user should NOT have super admin permission');
    }

    /**
     * Test round-robin state persistence
     */
    public function test_round_robin_state_persistence()
    {
        $agents = collect();
        for ($i = 0; $i < 3; $i++) {
            $agents->push($this->createTestAgent());
        }

        $repository = new \ReflectionClass($this->deliveryRepository);
        $method = $repository->getMethod('selectAgentRoundRobin');
        $method->setAccessible(true);

        // First selection
        $firstSelected = $method->invoke($this->deliveryRepository, $agents);
        
        // Check state was created
        $state = DeliveryAgentAssignmentState::where('region_key', 'global')->first();
        $this->assertNotNull($state, 'Assignment state should be created');
        $this->assertEquals($firstSelected->id, $state->last_assigned_agent_id);
        $this->assertEquals(1, $state->assignment_count);

        // Second selection should be next agent
        $secondSelected = $method->invoke($this->deliveryRepository, $agents);
        
        $state->refresh();
        $this->assertEquals($secondSelected->id, $state->last_assigned_agent_id);
        $this->assertEquals(2, $state->assignment_count);
        $this->assertNotEquals($firstSelected->id, $secondSelected->id, 
            'Second selection should be different agent');
    }

    /**
     * Test agent availability checking
     */
    public function test_agent_availability_checking()
    {
        $agent = $this->createTestAgent();
        
        $repository = new \ReflectionClass($this->deliveryRepository);
        $method = $repository->getMethod('isAgentStillAvailable');
        $method->setAccessible(true);
        
        $this->assertTrue($method->invoke($this->deliveryRepository, $agent), 
            'Online agent should be available');

        // Set agent offline
        $agent->delivery_agent_profile->availability_status = 'OFFLINE';
        $agent->delivery_agent_profile->save();
        
        $this->assertFalse($method->invoke($this->deliveryRepository, $agent), 
            'Offline agent should not be available');
    }

    /**
     * Test improved least busy agent selection
     */
    public function test_improved_least_busy_agent_selection()
    {
        $agent1 = $this->createTestAgent();
        $agent2 = $this->createTestAgent();
        
        // Create deliveries for agent1 to make them busier
        for ($i = 0; $i < 2; $i++) {
            $order = $this->createTestOrder();
            $this->deliveryRepository->assignDelivery([
                'delivery_agent_user_id' => $agent1->id,
                'delivery_fee' => 10.0,
                'assignment_type' => 'MANUAL',
            ], $order, $agent1);
        }

        $agents = collect([$agent1, $agent2]);
        
        $repository = new \ReflectionClass($this->deliveryRepository);
        $method = $repository->getMethod('selectLeastBusyAgent');
        $method->setAccessible(true);
        
        $selected = $method->invoke($this->deliveryRepository, $agents);
        
        // Should select agent2 (less busy)
        $this->assertEquals($agent2->id, $selected->id, 
            'Should select the least busy agent');
    }

    private function createPermissions(): void
    {
        $permissions = [
            Permission::SUPER_ADMIN,
            Permission::DELIVERY_AGENT,
            Permission::DELIVERY_SYSTEM,
            Permission::CUSTOMER,
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }
    }

    private function createTestOrder(): Order
    {
        $customer = User::factory()->create();
        $shop = Shop::factory()->create();
        
        return Order::create([
            'tracking_number' => 'ORDER-' . rand(10000, 99999),
            'customer_id' => $customer->id,
            'customer_contact' => '1234567890',
            'shop_id' => $shop->id,
            'order_status' => 'order-pending',
            'amount' => 100.0,
            'sales_tax' => 10.0,
            'paid_total' => 110.0,
            'total' => 110.0,
            'payment_status' => 'payment-pending',
            'payment_gateway' => 'CASH_ON_DELIVERY',
            'shipping_address' => [
                'address' => '123 Test St',
                'city' => 'Test City',
                'state' => 'Test State',
                'zip' => '12345',
                'country' => 'Test Country',
            ],
            'billing_address' => [
                'address' => '123 Test St',
                'city' => 'Test City',
                'state' => 'Test State',
                'zip' => '12345',
                'country' => 'Test Country',
            ],
            'requires_delivery' => true,
            'delivery_id' => null,
        ]);
    }

    private function createTestAgent(): User
    {
        $agent = User::factory()->create([
            'is_active' => true,
        ]);
        
        $agent->givePermissionTo(Permission::DELIVERY_AGENT);
        
        DeliveryAgentProfile::factory()->create([
            'user_id' => $agent->id,
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
            'current_location' => ['lat' => 40.7128, 'lng' => -74.0060],
        ]);

        return $agent;
    }
}
