<?php

namespace Marvel\Tests\Unit\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\User;
use Marvel\Database\Models\DeliveryAgentProfile;
use Marvel\Database\Repositories\DeliveryRepository;
use Marvel\Enums\Permission;
use Marvel\Tests\DeliveryTestCase;
use Marvel\Database\Models\Shop;
use Spatie\Permission\Models\Permission as PermissionModel;

class SecurityHardeningTest extends DeliveryTestCase
{
    use RefreshDatabase;

    protected DeliveryRepository $deliveryRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->deliveryRepository = app(DeliveryRepository::class);
    }

    /**
     * Test that system user is created with minimal privileges
     */
    public function test_system_user_has_minimal_privileges()
    {
        // Create a test order and agent to trigger system user creation
        $order = $this->createTestOrder();
        $agent = $this->createTestAgent();

        // Trigger auto assignment which should create system user
        $this->deliveryRepository->assignDelivery([
            'delivery_agent_user_id' => $agent->id,
            'delivery_fee' => 10.0,
            'assignment_type' => 'AUTOMATIC',
        ], $order, $agent);

        // Verify system user was created
        $systemUser = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($systemUser, 'System user should be created');

        // Verify system user has only delivery system permission
        $permissions = $systemUser->permissions->pluck('name')->toArray();
        $this->assertContains(Permission::DELIVERY_SYSTEM, $permissions, 
            'System user should have delivery system permission');
        $this->assertNotContains(Permission::SUPER_ADMIN, $permissions, 
            'System user should NOT have super admin permission');
        
        // Verify system user details
        $this->assertEquals('Delivery System Service', $systemUser->name);
        $this->assertTrue($systemUser->is_active);
        $this->assertNotNull($systemUser->email_verified_at);
    }

    /**
     * Test that delivery system permission exists and works correctly
     */
    public function test_delivery_system_permission_exists()
    {
        // Ensure permission exists
        $permission = PermissionModel::where('name', Permission::DELIVERY_SYSTEM)
            ->where('guard_name', 'api')
            ->first();
        
        $this->assertNotNull($permission, 'Delivery system permission should exist');
        $this->assertEquals('delivery_system', $permission->name);
        $this->assertEquals('api', $permission->guard_name);
    }

    /**
     * Test that system user can perform delivery assignments
     */
    public function test_system_user_can_assign_deliveries()
    {
        $order = $this->createTestOrder();
        $agent = $this->createTestAgent();

        // Get system user
        $systemUser = $this->getSystemUser();
        
        // Verify system user can assign deliveries
        $delivery = $this->deliveryRepository->assignDelivery([
            'delivery_agent_user_id' => $agent->id,
            'delivery_fee' => 10.0,
            'assignment_type' => 'AUTOMATIC',
        ], $order, $systemUser);

        $this->assertNotNull($delivery);
        $this->assertEquals($systemUser->id, $delivery->assigned_by_user_id);
        $this->assertEquals('AUTOMATIC', $delivery->assignment_type);
    }

    /**
     * Test audit trail for system operations
     */
    public function test_system_operations_audit_trail()
    {
        $order = $this->createTestOrder();
        $agent = $this->createTestAgent();

        // Perform assignment
        $delivery = $this->deliveryRepository->assignDelivery([
            'delivery_agent_user_id' => $agent->id,
            'delivery_fee' => 10.0,
            'assignment_type' => 'AUTOMATIC',
        ], $order, $agent);

        // Verify audit trail
        $this->assertNotNull($delivery->assigned_by_user_id, 'Assignment should have assigned_by_user_id');
        
        $assignedBy = User::find($delivery->assigned_by_user_id);
        $this->assertEquals('<EMAIL>', $assignedBy->email, 
            'Assignment should be attributed to system user');

        // Check delivery status log
        $statusLog = $delivery->statusLogs()->first();
        $this->assertNotNull($statusLog, 'Status log should exist');
        $this->assertEquals($assignedBy->id, $statusLog->user_id, 
            'Status log should reference system user');
    }

    /**
     * Test that old super admin system users are handled gracefully
     */
    public function test_legacy_super_admin_system_user_handling()
    {
        // Ensure super admin permission exists
        PermissionModel::firstOrCreate([
            'name' => Permission::SUPER_ADMIN,
            'guard_name' => 'api',
        ]);

        // Create a legacy system user with super admin permissions
        $legacySystemUser = User::create([
            'name' => 'Legacy System User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        $legacySystemUser->givePermissionTo(Permission::SUPER_ADMIN);

        // Verify the existing user is returned (not recreated)
        $systemUser = $this->getSystemUser();
        $this->assertEquals($legacySystemUser->id, $systemUser->id, 
            'Existing system user should be returned');
        
        // Note: In production, you might want to add logic to migrate legacy users
        // to the new permission structure, but for now we just ensure no errors occur
    }

    private function getSystemUser(): User
    {
        $reflection = new \ReflectionClass($this->deliveryRepository);
        $method = $reflection->getMethod('getSystemUser');
        $method->setAccessible(true);
        return $method->invoke($this->deliveryRepository);
    }

    private function createTestOrder(): Order
    {
        $customer = User::factory()->create();
        $shop = Shop::factory()->create();

        return Order::create([
            'tracking_number' => 'ORDER-' . rand(10000, 99999),
            'customer_id' => $customer->id,
            'customer_contact' => '1234567890',
            'shop_id' => $shop->id,
            'order_status' => 'order-pending',
            'amount' => 100.0,
            'sales_tax' => 10.0,
            'paid_total' => 110.0,
            'total' => 110.0,
            'payment_status' => 'payment-pending',
            'payment_gateway' => 'CASH_ON_DELIVERY',
            'shipping_address' => [
                'address' => '123 Test St',
                'city' => 'Test City',
                'state' => 'Test State',
                'zip' => '12345',
                'country' => 'Test Country',
            ],
            'billing_address' => [
                'address' => '123 Test St',
                'city' => 'Test City',
                'state' => 'Test State',
                'zip' => '12345',
                'country' => 'Test Country',
            ],
            'requires_delivery' => true,
            'delivery_id' => null,
        ]);
    }

    private function createTestAgent(): User
    {
        $agent = User::factory()->create([
            'is_active' => true,
        ]);
        
        $agent->givePermissionTo(Permission::DELIVERY_AGENT);
        
        DeliveryAgentProfile::factory()->create([
            'user_id' => $agent->id,
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
            'current_location' => ['lat' => 40.7128, 'lng' => -74.0060],
        ]);

        return $agent;
    }
}
