<?php

namespace Marvel\Tests\Unit\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\User;
use Marvel\Database\Models\Delivery;
use Marvel\Database\Models\DeliveryAgentProfile;
use Marvel\Database\Repositories\DeliveryRepository;
use Marvel\Enums\Permission;
use Marvel\Enums\DeliveryStatus;
use Marvel\Tests\DeliveryTestCase;
use Marvel\Database\Models\Shop;

class DatabaseConstraintsTest extends DeliveryTestCase
{
    use RefreshDatabase;

    protected DeliveryRepository $deliveryRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->deliveryRepository = app(DeliveryRepository::class);
    }

    /**
     * Test that unique constraint prevents duplicate order assignments
     */
    public function test_unique_constraint_prevents_duplicate_order_assignments()
    {
        $order = $this->createTestOrder();
        $agent1 = $this->createTestAgent();
        $agent2 = $this->createTestAgent();

        // First assignment should succeed
        $delivery1 = $this->deliveryRepository->assignDelivery([
            'delivery_agent_user_id' => $agent1->id,
            'delivery_fee' => 10.0,
            'assignment_type' => 'MANUAL',
        ], $order, $agent1);

        $this->assertNotNull($delivery1);

        // Second assignment to same order should fail
        $this->expectException(\Exception::class);
        $this->deliveryRepository->assignDelivery([
            'delivery_agent_user_id' => $agent2->id,
            'delivery_fee' => 10.0,
            'assignment_type' => 'MANUAL',
        ], $order, $agent2);
    }

    /**
     * Test that unique constraint prevents multiple deliveries for same order
     */
    public function test_unique_constraint_prevents_multiple_deliveries_per_order()
    {
        $order = $this->createTestOrder();
        $agent = $this->createTestAgent();

        // Create first delivery
        $delivery1 = Delivery::create([
            'order_id' => $order->id,
            'delivery_agent_user_id' => $agent->id,
            'status' => DeliveryStatus::ASSIGNED,
            'delivery_fee' => 10.0,
            'assigned_at' => now(),
            'assigned_by_user_id' => $agent->id,
            'assignment_type' => 'MANUAL',
        ]);

        $this->assertNotNull($delivery1);

        // Attempt to create second delivery for same order should fail
        $this->expectException(\Illuminate\Database\QueryException::class);
        Delivery::create([
            'order_id' => $order->id,
            'delivery_agent_user_id' => $agent->id,
            'status' => DeliveryStatus::ASSIGNED,
            'delivery_fee' => 15.0,
            'assigned_at' => now(),
            'assigned_by_user_id' => $agent->id,
            'assignment_type' => 'MANUAL',
        ]);
    }

    /**
     * Test agent workload index performance
     */
    public function test_agent_workload_index_performance()
    {
        $agent = $this->createTestAgent();
        
        // Create multiple deliveries for the agent
        for ($i = 0; $i < 10; $i++) {
            $order = $this->createTestOrder();
            Delivery::create([
                'order_id' => $order->id,
                'delivery_agent_user_id' => $agent->id,
                'status' => $i % 2 === 0 ? DeliveryStatus::ASSIGNED : DeliveryStatus::COMPLETED,
                'delivery_fee' => 10.0,
                'assigned_at' => now(),
                'assigned_by_user_id' => $agent->id,
                'assignment_type' => 'AUTOMATIC',
            ]);
        }

        // Test workload query performance
        $startTime = microtime(true);
        
        $activeCount = DB::table('deliveries')
            ->where('delivery_agent_user_id', $agent->id)
            ->whereIn('status', [
                'ASSIGNED',
                'ACCEPTED_BY_AGENT',
                'PICKED_UP',
                'IN_TRANSIT',
                'REACHED_DESTINATION'
            ])
            ->count();

        $endTime = microtime(true);
        $queryTime = ($endTime - $startTime) * 1000; // Convert to milliseconds

        $this->assertEquals(5, $activeCount, 'Should count only active deliveries');
        $this->assertLessThan(100, $queryTime, 'Query should complete in under 100ms');
    }

    /**
     * Test database transaction rollback on assignment failure
     */
    public function test_transaction_rollback_on_assignment_failure()
    {
        $order = $this->createTestOrder();
        
        // Create an invalid agent (no profile)
        $invalidAgent = User::factory()->create(['is_active' => true]);
        $invalidAgent->givePermissionTo(Permission::DELIVERY_AGENT);
        // Note: No delivery agent profile created

        $initialDeliveryCount = Delivery::count();
        $initialOrderDeliveryId = $order->delivery_id;

        // Assignment should fail due to missing profile
        try {
            $this->deliveryRepository->assignDelivery([
                'delivery_agent_user_id' => $invalidAgent->id,
                'delivery_fee' => 10.0,
                'assignment_type' => 'AUTOMATIC',
            ], $order, $invalidAgent);
            
            $this->fail('Assignment should have failed');
        } catch (\Exception $e) {
            // Expected to fail
        }

        // Verify no delivery was created and order wasn't modified
        $this->assertEquals($initialDeliveryCount, Delivery::count(), 
            'No delivery should be created on failure');
        
        $order->refresh();
        $this->assertEquals($initialOrderDeliveryId, $order->delivery_id, 
            'Order delivery_id should not be modified on failure');
    }

    /**
     * Test concurrent assignment prevention with database locks
     */
    public function test_concurrent_assignment_prevention()
    {
        $order = $this->createTestOrder();
        $agent1 = $this->createTestAgent();
        $agent2 = $this->createTestAgent();

        $successCount = 0;
        $failureCount = 0;

        // Simulate concurrent assignments
        for ($i = 0; $i < 5; $i++) {
            try {
                DB::beginTransaction();
                
                $delivery = $this->deliveryRepository->assignDelivery([
                    'delivery_agent_user_id' => $i % 2 === 0 ? $agent1->id : $agent2->id,
                    'delivery_fee' => 10.0,
                    'assignment_type' => 'AUTOMATIC',
                ], $order, $agent1);
                
                if ($delivery) {
                    $successCount++;
                }
                
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                $failureCount++;
            }
        }

        // Only one assignment should succeed
        $this->assertEquals(1, $successCount, 'Only one assignment should succeed');
        $this->assertEquals(4, $failureCount, 'Four assignments should fail');

        // Verify order has exactly one delivery
        $order->refresh();
        $this->assertNotNull($order->delivery_id, 'Order should have a delivery assigned');
        
        $deliveryCount = Delivery::where('order_id', $order->id)->count();
        $this->assertEquals(1, $deliveryCount, 'Only one delivery should exist for the order');
    }

    private function createTestOrder(): Order
    {
        $shop = Shop::factory()->create();
        
        return Order::factory()->create([
            'shop_id' => $shop->id,
            'requires_delivery' => true,
            'delivery_id' => null,
        ]);
    }

    private function createTestAgent(): User
    {
        $agent = User::factory()->create([
            'is_active' => true,
        ]);
        
        $agent->givePermissionTo(Permission::DELIVERY_AGENT);
        
        DeliveryAgentProfile::factory()->create([
            'user_id' => $agent->id,
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
            'current_location' => ['lat' => 40.7128, 'lng' => -74.0060],
        ]);

        return $agent;
    }
}
