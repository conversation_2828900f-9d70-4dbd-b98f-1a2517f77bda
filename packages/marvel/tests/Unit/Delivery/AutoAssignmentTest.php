<?php

namespace Marvel\Tests\Unit\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Marvel\Database\Models\Order;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\User;
use Marvel\Enums\OrderStatus;
use Marvel\Enums\PaymentStatus;
use Marvel\Enums\Permission;
use Marvel\Jobs\AutoAssignDeliveryJob;
use Marvel\Database\Repositories\DeliveryRepository;
use Spatie\Permission\Models\Permission as PermissionModel;
use Tests\TestCase;

class AutoAssignmentTest extends TestCase
{
    use RefreshDatabase;

    protected $shop;
    protected $order;
    protected $agent;
    protected $repository;

    public function setUp(): void
    {
        parent::setUp();

        // Create permissions
        $this->createPermissions();

        // Create shop with location
        $owner = new User([
            'name' => 'Shop Owner',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $owner->save();
        $owner->givePermissionTo(Permission::STORE_OWNER);

        $this->shop = new Shop([
            'name' => 'Test Shop',
            'slug' => 'test-shop',
            'owner_id' => $owner->id,
            'is_active' => true,
            'settings' => [
                'location' => [
                    'lat' => 40.7128,
                    'lng' => -74.0060,
                    'city' => 'New York',
                    'country' => 'USA'
                ]
            ]
        ]);
        $this->shop->save();

        // Create order
        $this->order = new Order([
            'tracking_number' => 'TEST-' . time(),
            'customer_id' => $owner->id,
            'shop_id' => $this->shop->id,
            'status' => OrderStatus::PENDING,
            'payment_status' => PaymentStatus::CASH_ON_DELIVERY,
            'requires_delivery' => true,
            'amount' => 100.00,
            'paid_total' => 100.00,
            'total' => 100.00,
        ]);
        $this->order->save();

        // Create delivery agent
        $this->agent = new User([
            'name' => 'Delivery Agent',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->agent->save();
        $this->agent->givePermissionTo(Permission::DELIVERY_AGENT);

        // Create agent profile with location close to shop
        $this->agent->delivery_agent_profile()->create([
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
            'current_location' => ['lat' => 40.7138, 'lng' => -74.0070], // ~1km away
        ]);

        $this->repository = new DeliveryRepository();
    }

    private function createPermissions()
    {
        $permissions = [
            Permission::SUPER_ADMIN,
            Permission::STORE_OWNER,
            Permission::DELIVERY_AGENT,
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission]);
        }
    }

    /** @test */
    public function it_dispatches_auto_assignment_job_when_order_is_created()
    {
        Queue::fake();

        // Enable auto assignment
        config(['delivery.auto_assignment_enabled' => true]);

        // Create a new order that should trigger auto assignment
        $newOrder = new Order([
            'tracking_number' => 'AUTO-' . time(),
            'customer_id' => $this->order->customer_id,
            'shop_id' => $this->shop->id,
            'status' => OrderStatus::PROCESSING,
            'payment_status' => PaymentStatus::SUCCESS,
            'requires_delivery' => true,
            'amount' => 150.00,
            'paid_total' => 150.00,
            'total' => 150.00,
        ]);
        $newOrder->save();

        // Assert that the auto assignment job was dispatched
        Queue::assertPushed(AutoAssignDeliveryJob::class, function ($job) use ($newOrder) {
            return $job->order->id === $newOrder->id && $job->trigger === 'created';
        });
    }

    /** @test */
    public function it_dispatches_auto_assignment_job_when_order_status_changes()
    {
        Queue::fake();

        // Enable auto assignment
        config(['delivery.auto_assignment_enabled' => true]);

        // Update order to processing status
        $this->order->status = OrderStatus::PROCESSING;
        $this->order->save();

        // Assert that the auto assignment job was dispatched
        Queue::assertPushed(AutoAssignDeliveryJob::class, function ($job) {
            return $job->order->id === $this->order->id && $job->trigger === 'updated';
        });
    }

    /** @test */
    public function it_does_not_dispatch_job_when_auto_assignment_is_disabled()
    {
        Queue::fake();

        // Disable auto assignment
        config(['delivery.auto_assignment_enabled' => false]);

        // Update order to processing status
        $this->order->status = OrderStatus::PROCESSING;
        $this->order->save();

        // Assert that no job was dispatched
        Queue::assertNotPushed(AutoAssignDeliveryJob::class);
    }

    /** @test */
    public function it_does_not_dispatch_job_for_orders_that_do_not_require_delivery()
    {
        Queue::fake();

        // Enable auto assignment
        config(['delivery.auto_assignment_enabled' => true]);

        // Update order to not require delivery
        $this->order->requires_delivery = false;
        $this->order->status = OrderStatus::PROCESSING;
        $this->order->save();

        // Assert that no job was dispatched
        Queue::assertNotPushed(AutoAssignDeliveryJob::class);
    }

    /** @test */
    public function it_can_auto_assign_delivery_to_nearest_agent()
    {
        // Enable auto assignment
        config(['delivery.auto_assignment_enabled' => true]);

        // Test the auto assignment method directly
        $delivery = $this->repository->autoAssignDelivery($this->order);

        $this->assertNotNull($delivery);
        $this->assertEquals($this->agent->id, $delivery->delivery_agent_user_id);
        $this->assertEquals('AUTOMATIC', $delivery->assignment_type);
        $this->assertEquals('ASSIGNED', $delivery->status);

        // Check that order was updated
        $this->order->refresh();
        $this->assertEquals($delivery->id, $this->order->delivery_id);
    }

    /** @test */
    public function it_creates_pending_delivery_when_no_agents_available()
    {
        // Make agent unavailable
        $this->agent->delivery_agent_profile->update([
            'availability_status' => 'OFFLINE'
        ]);

        // Test auto assignment with no available agents
        $delivery = $this->repository->autoAssignDelivery($this->order);

        $this->assertNotNull($delivery);
        $this->assertNull($delivery->delivery_agent_user_id);
        $this->assertEquals('PENDING_ASSIGNMENT', $delivery->status);
        $this->assertEquals('AUTOMATIC', $delivery->assignment_type);
    }

    /** @test */
    public function it_expands_search_radius_on_retry()
    {
        // Move agent far away (outside initial radius)
        $this->agent->delivery_agent_profile->update([
            'current_location' => ['lat' => 40.8500, 'lng' => -73.8000] // ~25km away
        ]);

        // Test auto assignment with retry (should expand radius)
        $delivery = $this->repository->autoAssignDelivery($this->order, [
            'radius' => 15, // Initial radius
            'attempt' => 2, // Second attempt
        ]);

        // Should find the agent with expanded radius
        $this->assertNotNull($delivery);
        $this->assertEquals($this->agent->id, $delivery->delivery_agent_user_id);
    }
}
