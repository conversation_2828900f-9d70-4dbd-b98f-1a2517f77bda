<?php

namespace Marvel\Tests\Unit\Delivery;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Marvel\Database\Models\DeliveryAgentEarning;
use Marvel\Database\Models\User;
use Marvel\Database\Repositories\DeliveryAgentEarningRepository;
use Marvel\Enums\Permission;
use Spatie\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class DeliveryAgentEarningRepositoryTest extends TestCase
{
    use RefreshDatabase;

    protected $repository;
    protected $agent;

    public function setUp(): void
    {
        parent::setUp();

        $this->repository = app(DeliveryAgentEarningRepository::class);

        // Create permissions
        $this->createPermissions();

        // Create delivery agent
        $this->agent = new User([
            'name' => 'Delivery Agent',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true,
        ]);
        $this->agent->save();
        $this->agent->givePermissionTo(Permission::DELIVERY_AGENT);

        // Create agent profile
        $this->agent->delivery_agent_profile()->create([
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
        ]);
    }

    /** @test */
    public function it_can_get_earnings_for_agent()
    {
        // Create earnings record
        DeliveryAgentEarning::create([
            'delivery_agent_user_id' => $this->agent->id,
            'total_earnings' => 100.00,
            'withdrawn_amount' => 50.00,
            'current_balance' => 50.00,
            'pending_withdrawal_amount' => 0.00,
        ]);

        $earnings = $this->repository->getEarningsForAgent($this->agent);

        $this->assertInstanceOf(DeliveryAgentEarning::class, $earnings);
        $this->assertEquals($this->agent->id, $earnings->delivery_agent_user_id);
        $this->assertEquals(100.00, $earnings->total_earnings);
        $this->assertEquals(50.00, $earnings->current_balance);
    }

    /** @test */
    public function it_creates_earnings_record_if_none_exists()
    {
        $earnings = $this->repository->getEarningsForAgent($this->agent);

        $this->assertInstanceOf(DeliveryAgentEarning::class, $earnings);
        $this->assertEquals($this->agent->id, $earnings->delivery_agent_user_id);
        $this->assertEquals(0.00, $earnings->total_earnings);
        $this->assertEquals(0.00, $earnings->current_balance);
    }

    /** @test */
    public function it_can_update_payment_info()
    {
        $paymentInfo = [
            'payment_info' => [
                'method' => 'MOBILE_MONEY',
                'provider' => 'ORANGE_MONEY',
                'details' => [
                    'phone_number' => '**********',
                    'account_name' => 'Test User',
                ]
            ]
        ];

        $earnings = $this->repository->updatePaymentInfo($paymentInfo, $this->agent);

        $this->assertInstanceOf(DeliveryAgentEarning::class, $earnings);
        $this->assertEquals($this->agent->id, $earnings->delivery_agent_user_id);
        $this->assertEquals($paymentInfo['payment_info'], $earnings->payment_info);
    }

    /**
     * Create permissions needed for testing.
     *
     * @return void
     */
    protected function createPermissions()
    {
        // Create roles if they don't exist
        $adminRole = Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'api']);
        $agentRole = Role::firstOrCreate(['name' => 'delivery_agent', 'guard_name' => 'api']);

        // Create permissions if they don't exist
        $permissions = [
            Permission::SUPER_ADMIN,
            Permission::STORE_OWNER,
            Permission::STAFF,
            Permission::CUSTOMER,
            Permission::DELIVERY_AGENT,
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }

        // Assign permissions to roles if not already assigned
        if (!$adminRole->hasPermissionTo(Permission::SUPER_ADMIN)) {
            $adminRole->givePermissionTo(Permission::SUPER_ADMIN);
        }

        if (!$agentRole->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            $agentRole->givePermissionTo(Permission::DELIVERY_AGENT);
        }
    }


}
