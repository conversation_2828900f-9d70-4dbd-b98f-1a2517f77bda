<?php

namespace Marvel\Tests;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Artisan;
use Marvel\Database\Models\Shop;
use Marvel\Database\Models\User;
use Marvel\Enums\Permission;
use <PERSON><PERSON>\Permission\Models\Permission as PermissionModel;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

abstract class DeliveryTestCase extends TestCase
{
    use DatabaseTransactions, WithFaker;

    /**
     * Indicates whether the default seeder should run before each test.
     *
     * @var bool
     */
    protected $seed = false;

    /**
     * Setup the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Create permissions and roles only once
        static $permissionsCreated = false;

        if (!$permissionsCreated) {
            $this->createPermissions();
            $permissionsCreated = true;
        }
    }

    /**
     * Create a user with the given role.
     *
     * @param string $role
     * @param array $attributes
     * @return User
     */
    protected function createUser(string $role, array $attributes = []): User
    {
        $userData = array_merge([
            'name' => $this->faker->name,
            'email' => $this->faker->unique()->safeEmail,
            'password' => bcrypt('password'),
            'is_active' => true,
        ], $attributes);

        $user = new User($userData);
        $user->save();

        $user->givePermissionTo($role);

        return $user;
    }

    /**
     * Create an admin user.
     *
     * @param array $attributes
     * @return User
     */
    protected function createAdmin(array $attributes = []): User
    {
        return $this->createUser(Permission::SUPER_ADMIN, $attributes);
    }

    /**
     * Create a delivery agent user.
     *
     * @param array $attributes
     * @param array $profileAttributes
     * @return User
     */
    protected function createDeliveryAgent(array $attributes = [], array $profileAttributes = []): User
    {
        $agent = $this->createUser(Permission::DELIVERY_AGENT, $attributes);

        $defaultProfileAttributes = [
            'availability_status' => 'ONLINE',
            'kyc_status' => 'APPROVED',
            'current_location' => ['lat' => 40.7128, 'lng' => -74.0060],
        ];

        $agent->delivery_agent_profile()->create(
            array_merge($defaultProfileAttributes, $profileAttributes)
        );

        return $agent;
    }

    /**
     * Create a shop.
     *
     * @param User|null $owner
     * @param array $attributes
     * @return Shop
     */
    protected function createShop(User $owner = null, array $attributes = []): Shop
    {
        if (!$owner) {
            $owner = $this->createAdmin();
        }

        $shopData = array_merge([
            'name' => $this->faker->company,
            'slug' => $this->faker->slug,
            'description' => $this->faker->paragraph,
            'owner_id' => $owner->id,
            'is_active' => true,
            'address' => [
                'street_address' => $this->faker->streetAddress,
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'zip' => $this->faker->postcode,
                'country' => $this->faker->country,
            ],
        ], $attributes);

        $shop = new Shop($shopData);
        $shop->save();

        return $shop;
    }

    /**
     * Create an order.
     *
     * @param Shop|null $shop
     * @param User|null $customer
     * @param array $attributes
     * @return \Marvel\Database\Models\Order
     */
    protected function createOrder(Shop $shop = null, User $customer = null, array $attributes = [])
    {
        if (!$shop) {
            $shop = $this->createShop();
        }

        if (!$customer) {
            $customer = $this->createUser(Permission::CUSTOMER);
        }

        $orderData = array_merge([
            'tracking_number' => 'ORDER-' . rand(10000, 99999),
            'customer_id' => $customer->id,
            'shop_id' => $shop->id,
            'order_status' => 'order-processing',
            'payment_status' => 'payment-success',
            'amount' => $this->faker->randomFloat(2, 10, 500),
            'requires_delivery' => true,
            'delivery_id' => null,
        ], $attributes);

        $order = new \Marvel\Database\Models\Order($orderData);
        $order->save();

        return $order;
    }

    /**
     * Create permissions needed for testing.
     *
     * @return void
     */
    protected function createPermissions()
    {
        // Create roles if they don't exist
        $adminRole = Role::firstOrCreate(['name' => 'super_admin', 'guard_name' => 'api']);
        $agentRole = Role::firstOrCreate(['name' => 'delivery_agent', 'guard_name' => 'api']);
        $customerRole = Role::firstOrCreate(['name' => 'customer', 'guard_name' => 'api']);
        $storeOwnerRole = Role::firstOrCreate(['name' => 'store_owner', 'guard_name' => 'api']);
        $staffRole = Role::firstOrCreate(['name' => 'staff', 'guard_name' => 'api']);

        // Create permissions if they don't exist
        $permissions = [
            Permission::SUPER_ADMIN,
            Permission::STORE_OWNER,
            Permission::STAFF,
            Permission::CUSTOMER,
            Permission::DELIVERY_AGENT,
            Permission::DELIVERY_SYSTEM,
        ];

        foreach ($permissions as $permission) {
            PermissionModel::firstOrCreate(['name' => $permission, 'guard_name' => 'api']);
        }

        // Assign permissions to roles if not already assigned
        if (!$adminRole->hasPermissionTo(Permission::SUPER_ADMIN)) {
            $adminRole->givePermissionTo(Permission::SUPER_ADMIN);
        }

        if (!$agentRole->hasPermissionTo(Permission::DELIVERY_AGENT)) {
            $agentRole->givePermissionTo(Permission::DELIVERY_AGENT);
        }

        if (!$customerRole->hasPermissionTo(Permission::CUSTOMER)) {
            $customerRole->givePermissionTo(Permission::CUSTOMER);
        }

        if (!$storeOwnerRole->hasPermissionTo(Permission::STORE_OWNER)) {
            $storeOwnerRole->givePermissionTo(Permission::STORE_OWNER);
        }

        if (!$staffRole->hasPermissionTo(Permission::STAFF)) {
            $staffRole->givePermissionTo(Permission::STAFF);
        }
    }
}
