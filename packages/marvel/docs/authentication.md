# Marvel Authentication System

## Overview
Marvel implements a multi-guard authentication system using Laravel Sanctum for API authentication and session-based authentication for web routes. The system supports multiple authentication methods including token-based, email/password, and OTP-based authentication.

## Architecture

### Guards Configuration
The system uses two main authentication guards:

1. API Guard (Default):
```php
'api' => [
    'driver' => 'sanctum',
    'provider' => 'users',
]
```

2. Web Guard:
```php
'web' => [
    'driver' => 'session',
    'provider' => 'users',
]
```

### User Model
The authentication system uses the `Marvel\Database\Models\User` model which:
- Implements `MustVerifyEmail` for email verification
- Uses `HasApiTokens` for Sanctum token management
- Includes role-based permissions via `HasRoles`
- Uses `api` as the default guard

## Authentication Flows

### 1. Token-Based Authentication (Primary Method)

#### Login Process
```php
POST /api/token
```

Request:
```json
{
    "email": "<EMAIL>",
    "password": "password"
}
```

Response:
```json
{
    "token": "plain_text_token",
    "permissions": ["permission1", "permission2"],
    "email_verified": true,
    "role": "customer"
}
```

Implementation in `UserController@token`:
```php
if (!$user || !Hash::check($request->password, $user->password)) {
    return ["token" => null, "permissions" => []];
}
return [
    "token" => $user->createToken('auth_token')->plainTextToken,
    "permissions" => $user->getPermissionNames(),
    "email_verified" => $user->hasVerifiedEmail(),
    "role" => $user->getRoleNames()->first()
];
```

### 2. GraphQL Authentication

The system provides GraphQL mutations for authentication operations through `AuthMutator`:

```graphql
mutation {
    token(email: String!, password: String!): AuthResponse
    logout: Boolean
    register(input: RegisterInput!): AuthResponse
    verifyOtpCode(input: VerifyOtpInput!): AuthResponse
    otpLogin(input: OtpInput!): AuthResponse
}
```

### 3. OTP-Based Authentication

The system supports OTP-based authentication through configurable gateways:

```php
'active_otp_gateway' => env('ACTIVE_OTP_GATEWAY', 'twilio')
```

## Authentication Endpoints

### Public Authentication Endpoints

```
POST /register
- Register a new user
- Body: { name, email, password }

POST /token
- Login and get authentication token
- Body: { email, password }

POST /logout
- Logout user and invalidate token
- Requires: Authentication token

POST /forget-password
- Initiate password reset process
- Body: { email }

POST /verify-forget-password-token
- Verify password reset token
- Body: { email, token }

POST /reset-password
- Reset password using token
- Body: { email, token, password }

POST /social-login-token
- Login via social authentication
- Body: { provider, access_token }

POST /send-otp-code
- Request OTP code
- Body: { phone_number }

POST /verify-otp-code
- Verify OTP code
- Body: { phone_number, code }

POST /otp-login
- Login using OTP
- Body: { phone_number, code }

GET /email/verify/{id}/{hash}
- Verify email address
- URL Parameters: id, hash

POST /email/verification-notification
- Resend verification email
- Requires: Authentication token
- Middleware: ['auth:sanctum', 'throttle:6,1']
```

### Protected Authentication Endpoints

#### Customer Routes
```
Middleware: ['can:customer', 'auth:sanctum', 'email.verified']

GET /me
- Get current user profile

PUT /users/{id}
- Update user profile
- URL Parameters: id
- Body: User data

POST /change-password
- Change user password
- Body: { old_password, new_password }

POST /update-contact
- Update user contact information
- Body: Contact details

POST /update-email
- Update user email address
- Body: { email }
```

#### Staff/Store Owner Routes
```
Middleware: ['permission:staff|store_owner', 'auth:sanctum', 'email.verified']

[No specific authentication endpoints, but requires authentication for access]
```

#### Super Admin Routes
```
Middleware: ['permission:super_admin', 'auth:sanctum', 'email.verified']

POST /users/block-user
- Block/ban a user
- Body: { id }

POST /users/unblock-user
- Unblock/activate a user
- Body: { id }

POST /users/make-admin
- Make or revoke admin status
- Body: { user_id }

GET /admin/list
- Get list of admin users

GET /customers/list
- Get list of customer users

GET /my-staffs
- Get list of staff users

GET /all-staffs
- Get list of all staff users
```

### Response Formats

#### Successful Authentication Response
```json
{
    "token": "plain_text_token",
    "permissions": ["permission1", "permission2"],
    "email_verified": true,
    "role": "customer"
}
```

#### Error Response
```json
{
    "message": "ERROR_MESSAGE",
    "status": STATUS_CODE
}
```

### Rate Limiting

- Email verification notification: 6 requests per minute
- Other endpoints: Default Laravel rate limiting

### Security Headers

All authentication endpoints should include:
```
Accept: application/json
Content-Type: application/json
```

For protected routes, add:
```
Authorization: Bearer <token>
```

## Middleware Stack

### API Routes
1. `AcceptJson` - Ensures JSON responses
2. `AttemptAuthentication` - Handles authentication attempts
3. `EnsureEmailIsVerified` - Verifies email when required

### Web Routes
1. `Authenticate` - Handles web authentication
2. `RedirectIfAuthenticated` - Manages guest access

## Security Features

### 1. Email Verification
- Implements Laravel's email verification
- Required for certain operations
- Verification status tracked in responses

### 2. Rate Limiting
Applied through Laravel's throttle middleware:
```php
'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class
```

### 3. Permission System
- Role-based access control
- Permission checking at field level
- Integrated with GraphQL resolvers

## Error Handling

### Authentication Errors
```php
protected $error_handlers = [
    \Nuwave\Lighthouse\Execution\AuthenticationErrorHandler::class,
    \Nuwave\Lighthouse\Execution\AuthorizationErrorHandler::class,
]
```

### Common Error Responses
1. Invalid Credentials:
```json
{
    "token": null,
    "permissions": []
}
```

2. Email Not Verified:
```json
{
    "message": "EMAIL_NOT_VERIFIED",
    "status": 409
}
```

## Best Practices

### Token Management
1. Store tokens securely
2. Implement token refresh mechanism
3. Handle token revocation on logout

### Security Recommendations
1. Use HTTPS for all authentication requests
2. Implement password policies
3. Monitor failed authentication attempts
4. Regular security audits

### Implementation Example

#### Protected Route
```php
Route::middleware(['auth:sanctum'])->group(function () {
    // Protected routes here
});
```

#### Authentication Check
```php
if (Auth::guard('api')->check()) {
    // User is authenticated
}
```

## Configuration Guide

### Environment Variables
```env
ACTIVE_OTP_GATEWAY=twilio
SANCTUM_STATEFUL_DOMAINS=your-domain.com
SESSION_DRIVER=redis
```

### Sanctum Configuration
```php
'sanctum' => [
    'guard' => 'api',
    'expiration' => null,
    'middleware' => [
        'verify_csrf_token' => App\Http\Middleware\VerifyCsrfToken::class,
        'encrypt_cookies' => App\Http\Middleware\EncryptCookies::class,
    ],
]
```

## Troubleshooting

### Common Issues
1. Token Mismatch
   - Check token expiration
   - Verify correct guard usage
   - Confirm proper middleware configuration

2. Authentication Failures
   - Verify credentials format
   - Check user active status
   - Confirm email verification status

3. Permission Issues
   - Verify role assignments
   - Check permission configurations
   - Review middleware stack
