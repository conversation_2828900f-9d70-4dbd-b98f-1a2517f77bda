Internal Delivery System Documentation Analysis

I. New Tables
delivery_agent_profiles (Extends users for agent-specific data)
Purpose: Stores information specific to users acting as delivery agents.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
user_id: BIGINT UNSIGNED (FK to users.id, UNIQUE, Cascade on Delete) - Links to the core user record
availability_status: ENUM('ONLINE', 'OFFLINE', 'ON_DELIVERY') (Default: 'OFFLINE') - Agent sets this in the app
current_location: POINT, Nullable (Spatial Index) - For live tracking, updated frequently
kyc_status: ENUM('PENDING', 'SUBMITTED', 'APPROVED', 'REJECTED') (Default: 'PENDING')
kyc_documents: JSON, Nullable - Could store references/metadata about KYC docs stored in media
kyc_rejection_reason: TEXT, Nullable - If KYC is rejected
active_vehicle_id: BIGINT UNSIGNED, Nullable (FK to vehicles.id, Set Null on Delete) - Vehicle currently used
performance_rating: FLOAT, Nullable (e.g., 0.0 to 5.0) - Calculated based on deliveries/reviews
total_deliveries_completed: INT UNSIGNED (Default: 0)
last_check_in_at: TIMESTAMP, Nullable - Timestamp of last warehouse check-in
created_at: TIMESTAMP
updated_at: TIMESTAMP
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
vehicles
Purpose: Stores details about vehicles used by delivery agents.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
delivery_agent_user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete) - Owner of the vehicle
type: ENUM('BIKE', 'CAR', 'VAN', 'OTHER')
make: VARCHAR, Nullable
model: VARCHAR, Nullable
registration_number: VARCHAR, Unique
color: VARCHAR, Nullable
vehicle_documents: JSON, Nullable - References/metadata for registration/insurance docs in media
is_verified: BOOLEAN (Default: false) - Admin verifies vehicle docs
verification_notes: TEXT, Nullable
is_active: BOOLEAN (Default: true) - Agent might temporarily disable a vehicle
created_at: TIMESTAMP
updated_at: TIMESTAMP
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
deliveries
Purpose: Represents a single delivery task, linking an order to an agent and tracking its progress.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
order_id: BIGINT UNSIGNED (FK to orders.id, Cascade on Delete) - The order being delivered
delivery_agent_user_id: BIGINT UNSIGNED, Nullable (FK to users.id, Set Null on Delete) - The assigned agent
status: ENUM('PENDING_ASSIGNMENT', 'ASSIGNED', 'REJECTED_BY_AGENT', 'ACCEPTED_BY_AGENT', 'PICKED_UP', 'IN_TRANSIT', 'REACHED_DESTINATION', 'DELIVERED', 'COMPLETED', 'CANCELLED', 'FAILED_DELIVERY') (Default: 'PENDING_ASSIGNMENT', Index)
pickup_address: JSON, Nullable - Snapshot of warehouse/shop address at time of assignment
delivery_address: JSON, Nullable - Snapshot of customer address at time of assignment
estimated_delivery_time: TIMESTAMP, Nullable
assigned_at: TIMESTAMP, Nullable
accepted_at: TIMESTAMP, Nullable
picked_up_at: TIMESTAMP, Nullable
reached_destination_at: TIMESTAMP, Nullable
delivered_at: TIMESTAMP, Nullable - When package handed over
completed_at: TIMESTAMP, Nullable - When POD/Payment confirmed
cancelled_at: TIMESTAMP, Nullable
cancellation_reason: TEXT, Nullable
failed_at: TIMESTAMP, Nullable
failure_reason: TEXT, Nullable
delivery_fee: DECIMAL(10, 2), Nullable - Amount earned by agent for this delivery
notes_by_agent: TEXT, Nullable
notes_by_admin: TEXT, Nullable
proof_of_delivery: JSON, Nullable - Reference to media (photo/signature)
pod_type: ENUM('SIGNATURE', 'PHOTO', 'CODE', 'NONE'), Nullable
assigned_by_user_id: BIGINT UNSIGNED, Nullable (FK to users.id, Set Null on Delete) - Admin who assigned manually
assignment_type: ENUM('MANUAL', 'AUTOMATIC') (Default: 'AUTOMATIC')
created_at: TIMESTAMP
updated_at: TIMESTAMP
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
Index: (order_id)
Index: (delivery_agent_user_id, status)
delivery_status_logs
Purpose: Provides an audit trail of all status changes for a delivery.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
delivery_id: BIGINT UNSIGNED (FK to deliveries.id, Cascade on Delete)
status: ENUM(...) - Same ENUM values as deliveries.status
user_id: BIGINT UNSIGNED, Nullable (FK to users.id, Set Null on Delete) - User who triggered the change (agent/admin)
notes: TEXT, Nullable - Optional context for the status change
location: POINT, Nullable - Agent location at the time of status update
created_at: TIMESTAMP - Timestamp of the status change
(No updated_at needed for logs)
delivery_agent_earnings (Mirrors balances for shops)
Purpose: Tracks earnings and balance for delivery agents.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
delivery_agent_user_id: BIGINT UNSIGNED (FK to users.id, UNIQUE, Cascade on Delete)
total_earnings: DECIMAL(12, 2) (Default: 0)
withdrawn_amount: DECIMAL(12, 2) (Default: 0)
current_balance: DECIMAL(12, 2) (Default: 0)
pending_withdrawal_amount: DECIMAL(12, 2) (Default: 0) - Amount requested but not yet processed
payment_info: JSON, Nullable - Agent's preferred withdrawal details (bank, mobile money, etc.)
created_at: TIMESTAMP
updated_at: TIMESTAMP
delivery_agent_withdrawals (Mirrors withdraws for shops)
Purpose: Stores withdrawal requests made by delivery agents.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
delivery_agent_user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete)
amount: DECIMAL(10, 2)
status: ENUM('PENDING', 'APPROVED', 'PROCESSING', 'COMPLETED', 'REJECTED') (Default: 'PENDING', Index)
payment_method_details: JSON, Nullable - Snapshot of payment info used for this withdrawal
transaction_reference: VARCHAR, Nullable - Reference from payment processor
processed_by_user_id: BIGINT UNSIGNED, Nullable (FK to users.id, Set Null on Delete) - Admin who processed
notes: TEXT, Nullable - Admin notes regarding processing/rejection
requested_at: TIMESTAMP (Default: CURRENT_TIMESTAMP)
processed_at: TIMESTAMP, Nullable
created_at: TIMESTAMP
updated_at: TIMESTAMP
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
delivery_agent_location_history (Optional, for detailed tracking analysis)
Purpose: Stores historical location points for agents. Can grow very large.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
delivery_agent_user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete)
location: POINT (Spatial Index)
timestamp: TIMESTAMP (Index) - When location was recorded
delivery_id: BIGINT UNSIGNED, Nullable (FK to deliveries.id, Set Null on Delete) - Optional: Link location to a specific active delivery
created_at: TIMESTAMP
Index: (delivery_agent_user_id, timestamp)
II. Modifications to Existing Tables
users
No direct column changes needed if using Roles. Ensure a 'Delivery Agent' role can be created in the roles table.
Consider adding an index to email and potentially phone_number if OTP relies on it and it's added/used consistently.
orders
Add delivery_id: BIGINT UNSIGNED, Nullable (FK to deliveries.id, Set Null on Delete) - Direct link to the active/latest delivery attempt for this order.
Add requires_delivery: BOOLEAN (Default: true) - Flag to indicate if the order needs physical delivery (vs. digital or pickup-by-customer). Useful for filtering orders needing assignment. Set based on product types (is_digital) or shipping options chosen.
Consider modifying payment_gateway ENUM/VARCHAR to include 'PAY_ON_DELIVERY'.
Add Index: (requires_delivery, order_status, payment_status) - For finding orders needing delivery assignment.
roles (Spatie)
Ensure a role named 'Delivery Agent' (or similar) exists or can be created.
permissions (Spatie)
Create specific permissions for agents, e.g.:
access delivery agent app
view assigned deliveries
update delivery status
view earnings
request withdrawal
update availability status
manage vehicles (maybe add vehicle, edit vehicle)
submit kyc
Assign these permissions to the 'Delivery Agent' role via role_has_permissions.
media (Spatie)
Will be used polymorphically to store:
KYC documents (linked to User or DeliveryAgentProfile model, collection names like kyc_id, kyc_license, kyc_address_proof).
Vehicle documents (linked to Vehicle model, collection names like vehicle_registration, vehicle_insurance).
Proof of Delivery (linked to Delivery model, collection name proof_of_delivery).
III. Implementation Notes & Considerations
Roles vs. Type Column: Using Spatie Roles for 'Delivery Agent' is generally preferred over adding a type column to users as it's more flexible and aligns with the existing permission system.
KYC/Vehicle Documents: Storing actual files in the media table is standard. The JSON columns in delivery_agent_profiles and vehicles can store metadata, verification status per document, or references if needed, but might be redundant if the media table's custom_properties are used effectively.
Location Tracking: Decide between storing only the latest location (in delivery_agent_profiles) or historical locations (in delivery_agent_location_history). Storing only the latest is simpler and sufficient for live map views. History is better for analysis but requires more storage and management. Using a spatial index (POINT/GEOMETRY type) is crucial for performance.
Pay-on-Delivery: The flow needs careful implementation. The agent's app should trigger a confirmation (maybe customer enters OTP sent to their phone, or agent confirms cash received), which updates the backend (deliveries.status to 'DELIVERED', then potentially orders.payment_status to 'SUCCESS' and deliveries.status to 'COMPLETED').
Earnings Calculation: Decide when the delivery_fee is calculated and added to delivery_agent_earnings.current_balance. Likely upon deliveries.status changing to 'COMPLETED' after admin/system validation (daily check-in).
Warehouse Entity: If you have multiple distinct warehouses agents interact with, you might need a warehouses table storing their details (address, contact) and link deliveries or shops to them. For now, the pickup address is assumed to be the shop address associated with the order.
ENUM Values: Define the ENUM lists clearly and consistently.
Indexes: Add indexes strategically to foreign keys and columns used in frequent lookups (status, timestamps, location).
Dependencies: Ensure chosen services (Twilio/Firebase Auth, Maps API, FCM/Pusher, Payment Gateways) are integrated correctly.
This schema provides a robust foundation for the delivery system, integrating well with your existing structure while keeping delivery-specific concerns organized. Remember to create corresponding migrations for these changes.


Breaking down the justification for the proposed schema changes, explaining the reasoning behind each new table and modification, and considering alternatives where applicable.
Overarching Principles:
Separation of Concerns: Keep data related to distinct concepts (users, shops, products, deliveries) in separate tables. This improves clarity, maintainability, and reduces unrelated data in core tables.
Leverage Existing Patterns: Where the new feature mirrors existing functionality (like earnings/withdrawals for shops), adopt the established schema pattern for consistency.
Data Integrity: Use foreign keys, appropriate data types (ENUMs, POINT, DECIMAL), and non-nullable constraints where necessary. Store snapshots of critical data (addresses, payment info) to avoid issues if source data changes.
Scalability & Performance: Consider indexing, data types (avoiding TEXT searches where possible), and potential data volume (e.g., location history).
Integration: Ensure new tables link correctly to existing ones (users, orders) and utilize existing systems (media, Spatie Permissions).
Justification for New Tables:
delivery_agent_profiles
Why: Delivery agents are fundamentally users, but have a lot of specific information (KYC status, availability, location, vehicle, performance).
Alternative: Adding all these columns directly to the users table.
Reasoning: Rejected the alternative because it would heavily pollute the users table with many columns that are NULL for most users (customers, vendors, admins). This violates the Single Responsibility Principle and makes the users table unwieldy. Creating a separate profile table linked 1:1 with users (via user_id FK) is a cleaner, more modular approach, mirroring the existing user_profiles table. It keeps agent-specific data logically grouped.
vehicles
Why: Agents use specific vehicles, which need verification and tracking (type, registration, documents). An agent might have multiple vehicles over time or even concurrently (though likely uses one actively).
Alternative: Storing vehicle details as a JSON blob within delivery_agent_profiles.
Reasoning: Rejected the JSON alternative because vehicle information is structured (make, model, registration), needs unique constraints (registration number), and requires its own verification status. A dedicated table allows for proper indexing, querying (e.g., find all bikes), and managing multiple vehicles per agent cleanly. The 1:N relationship (User -> Vehicles) is best represented by a separate table. active_vehicle_id in delivery_agent_profiles links to the currently used one.
deliveries
Why: This is the core operational table representing a single delivery task. It needs to link an order to an agent and track its lifecycle (status, timestamps, locations, proof).
Alternative: Adding delivery status/agent info directly to the orders table.
Reasoning: Rejected the alternative because an order might theoretically require multiple delivery attempts (e.g., first attempt fails, reassigned). Mixing delivery lifecycle data directly into orders complicates the order's primary purpose (recording the purchase). A separate deliveries table allows tracking each attempt individually if needed, keeps orders focused, and provides a dedicated space for delivery-specific details like proof of delivery, cancellation reasons, agent notes, etc. The FK order_id links it back.
delivery_status_logs
Why: Provides a crucial audit trail. Knowing when, by whom (agent/admin), and potentially where each status change occurred is vital for tracking, accountability, and debugging.
Alternative: Relying only on the timestamp columns (picked_up_at, delivered_at, etc.) in the deliveries table.
Reasoning: The alternative is insufficient. It doesn't capture who made the change, intermediate statuses (like 'REACHED_DESTINATION'), failed attempts, or context (notes/location). A dedicated log table provides a complete, immutable history, which is standard practice for tracking state transitions in operational systems.
delivery_agent_earnings
Why: Agents earn money based on deliveries and need a balance tracked separately from vendors.
Alternative: Trying to adapt the existing balances table (used for shops).
Reasoning: Rejected adapting balances because the structure is tied to shop_id. While conceptually similar, mixing shop and agent balances in one table would require complex logic or adding nullable agent IDs, making it messy. Following the established pattern by creating a dedicated delivery_agent_earnings table linked to user_id is cleaner, maintains separation, and is consistent with the balances approach for shops.
delivery_agent_withdrawals
Why: Agents need to request withdrawals of their earnings, mirroring the process for shops.
Alternative: Adapting the existing withdraws table.
Reasoning: Same reasoning as for earnings. The existing withdraws table is tied to shop_id. A dedicated delivery_agent_withdrawals table linked to user_id is the clean, consistent approach, mirroring the existing pattern. Storing payment_method_details as a JSON snapshot ensures the withdrawal uses the details provided at that time, even if the agent updates their primary payment info later.
delivery_agent_location_history (Optional)
Why: If detailed historical tracking (route playback, analysis) is needed beyond just the current location.
Alternative: Not storing history, only the current location in delivery_agent_profiles.
Reasoning: This is presented as optional due to the significant storage implications. Storing only the current location (delivery_agent_profiles.current_location) covers the primary need (live map view). This history table is only justified if the business requirement for detailed historical analysis outweighs the storage and performance costs. Using POINT and spatial indexes is critical if implemented.
Justification for Modifications to Existing Tables:
orders
delivery_id (FK): Provides a direct, indexed link to the relevant deliveries record for quick lookups from an order. Essential for relating the order to its delivery task.
requires_delivery (BOOLEAN): Crucial for efficiently finding orders that need a delivery agent assigned. Without this, you'd have to infer based on product types or shipping methods, which is less direct and potentially slower.
Index on (requires_delivery, order_status, payment_status): Optimizes the query for finding pending orders that need delivery assignment.
payment_gateway update: Necessary if 'PAY_ON_DELIVERY' is a distinct method managed by the internal system.
users / roles / permissions (Spatie)
Why: Use the existing robust permission system.
Alternative: Adding an is_delivery_agent boolean flag or user_type ENUM to users.
Reasoning: Using Spatie Roles ('Delivery Agent') and Permissions (update delivery status, etc.) is far more flexible and scalable than hardcoding flags or types. It allows granular control over what agents can do and aligns perfectly with how permissions are already managed in the application.
media
Why: Leverage the existing, powerful media library for handling all file uploads (KYC, vehicle docs, POD).
Alternative: Storing file paths directly in the new tables or using the simpler attachments table.
Reasoning: Spatie Media Library is already integrated and handles variations, storage disks, and polymorphic relationships elegantly. Reusing it is efficient and consistent. Storing paths directly is less robust, and attachments seems less feature-rich than media.
In summary, the proposed schema aims for a balance between leveraging existing structures, ensuring clear separation of concerns for new features, maintaining data integrity, and providing the necessary data points and relationships to fulfill the requirements of the internal delivery system efficiently and scalably. The choices prioritize maintainability and consistency with the established architecture.


A potential implementation plan/roadmap for integrating the Internal Delivery System into the existing Laravel API project, based on the schema we defined.
Phase 1: Foundation & Core Agent Setup (Prerequisites)
Objective: Set up the database structure, models, basic authentication, and agent profile management.
Tasks:
Migrations:
Create migrations for all new tables: delivery_agent_profiles, vehicles, deliveries, delivery_status_logs, delivery_agent_earnings, delivery_agent_withdrawals, (delivery_agent_location_history if chosen).
Create migrations to modify existing tables: orders (add delivery_id, requires_delivery), potentially users if any minor tweaks needed.
Run migrations: php artisan migrate.
Models:
Create Eloquent models for all new tables (DeliveryAgentProfile, Vehicle, Delivery, etc.).
Define relationships (belongsTo, hasOne, hasMany, morphMany for media) in each model.
Set up $fillable properties.
Define casts for JSON, POINT, ENUMs (consider using dedicated Enum classes in PHP 8.1+), DATETIME/TIMESTAMP.
Update Order model with new relationships/columns.
Update User model with relationships to DeliveryAgentProfile, Vehicles, Deliveries, etc.
Roles & Permissions (Spatie):
Create a 'Delivery Agent' role.
Define necessary permissions (e.g., access delivery agent app, update delivery status, manage vehicles, request withdrawal, submit kyc).
Create a seeder to create the role and permissions, and assign permissions to the role. Run php artisan db:seed --class=DeliveryAgentPermissionsSeeder.
Agent Registration & Authentication:
Adapt existing user registration/authentication flow (likely Sanctum-based) or create specific endpoints.
Endpoint: POST /api/agent/register (Creates User, DeliveryAgentProfile, assigns 'Delivery Agent' role, potentially triggers OTP).
Endpoint: POST /api/agent/login (Standard Sanctum token login).
Endpoint: POST /api/agent/verify-otp (If using OTP for login/registration).
Endpoint: POST /api/agent/logout.
Integrate OTP service (Twilio/Firebase Auth).
Agent Profile & KYC:
Controller: Api/Agent/ProfileController.
Endpoints:
GET /api/agent/profile (Fetch agent's profile, including KYC status).
PUT /api/agent/profile (Update basic profile info).
POST /api/agent/profile/kyc (Upload KYC documents - uses Spatie Media Library). Requires kyc_status to be 'PENDING' or 'REJECTED'. Updates kyc_status to 'SUBMITTED'.
API Resources: AgentProfileResource.
Vehicle Management:
Controller: Api/Agent/VehicleController.
Endpoints:
GET /api/agent/vehicles (List agent's vehicles).
POST /api/agent/vehicles (Add a new vehicle, upload documents via Media Library).
GET /api/agent/vehicles/{vehicle} (View specific vehicle).
PUT /api/agent/vehicles/{vehicle} (Update vehicle details).
DELETE /api/agent/vehicles/{vehicle} (Soft delete).
POST /api/agent/vehicles/{vehicle}/set-active (Sets active_vehicle_id on delivery_agent_profiles).
API Resources: VehicleResource.
Availability Status:
Controller: Api/Agent/AvailabilityController.
Endpoint: PUT /api/agent/availability (Updates availability_status on delivery_agent_profiles).
Endpoint: GET /api/agent/availability (Fetch current status).
Phase 2: Core Delivery Workflow
Objective: Implement the process of assigning, accepting, and updating the status of deliveries.
Tasks:
Delivery Assignment (Manual First):
Admin Endpoint: POST /api/admin/orders/{order}/assign-delivery (Admin assigns an order to a specific delivery_agent_user_id). Creates a deliveries record, sets status to 'ASSIGNED', updates orders.delivery_id. Requires admin permissions.
Logic: Needs validation (order requires delivery, agent is available/verified, etc.).
Agent Delivery Viewing & Acceptance:
Controller: Api/Agent/DeliveryController.
Endpoints:
GET /api/agent/deliveries (List deliveries assigned to the agent, filter by status: 'ASSIGNED', 'ACCEPTED', 'PICKED_UP', etc.).
GET /api/agent/deliveries/{delivery} (View specific delivery details - order info, addresses).
POST /api/agent/deliveries/{delivery}/accept (Agent accepts, changes status to 'ACCEPTED_BY_AGENT').
POST /api/agent/deliveries/{delivery}/reject (Agent rejects, changes status to 'REJECTED_BY_AGENT', potentially needs admin notification).
API Resources: DeliveryResource, OrderSummaryResource (for embedding in DeliveryResource).
Delivery Status Updates:
Controller: Api/Agent/DeliveryStatusController.
Endpoints (using POST/PUT):
POST /api/agent/deliveries/{delivery}/pickup (Status -> 'PICKED_UP').
POST /api/agent/deliveries/{delivery}/in-transit (Status -> 'IN_TRANSIT').
POST /api/agent/deliveries/{delivery}/reached (Status -> 'REACHED_DESTINATION').
POST /api/agent/deliveries/{delivery}/deliver (Status -> 'DELIVERED', requires POD if applicable).
POST /api/agent/deliveries/{delivery}/fail (Status -> 'FAILED_DELIVERY', requires reason).
Logic: Each endpoint updates deliveries.status and corresponding timestamp (picked_up_at, etc.). Creates a record in delivery_status_logs. Validate transitions (can't go from ASSIGNED to DELIVERED).
Location: Capture agent's current location (from app request header/body) and store in delivery_status_logs. Update delivery_agent_profiles.current_location.
Proof of Delivery (POD):
Endpoint: POST /api/agent/deliveries/{delivery}/proof-of-delivery (Upload photo/signature via Media Library, link to deliveries record). Called before or during the /deliver endpoint call.
Pay-on-Delivery (POD) Confirmation:
Endpoint: POST /api/agent/deliveries/{delivery}/confirm-payment (Agent confirms cash received or triggers customer app confirmation).
Logic: This should update orders.payment_status to 'SUCCESS' and potentially deliveries.status to 'COMPLETED'. Needs careful design based on exact flow (cash vs. in-app customer payment).
Phase 3: Financials & Admin Oversight
Objective: Implement earnings tracking, withdrawal process, and admin views/actions.
Tasks:
Earnings Calculation:
Logic: Determine when earnings are credited. Likely via an Observer on the Delivery model or a Job triggered when status becomes 'COMPLETED' (after admin check-in/validation).
Calculate delivery_fee (based on distance, order value, fixed fee - TBD).
Update delivery_agent_earnings (increment total_earnings, current_balance).
Agent Earnings View:
Controller: Api/Agent/EarningsController.
Endpoints:
GET /api/agent/earnings/balance (Show current balance).
GET /api/agent/earnings/history (List completed deliveries with earnings).
GET /api/agent/earnings/withdrawals (List past withdrawal requests).
API Resources: AgentEarningsResource, WithdrawalResource.
Withdrawal Request:
Controller: Api/Agent/WithdrawalController.
Endpoints:
POST /api/agent/withdrawals (Agent requests withdrawal, specifying amount). Creates delivery_agent_withdrawals record with 'PENDING' status. Decrements current_balance and increments pending_withdrawal_amount in delivery_agent_earnings.
GET /api/agent/payment-info (View current saved payment info).
PUT /api/agent/payment-info (Update preferred withdrawal details - stored in delivery_agent_earnings.payment_info).
Admin Management - Agents:
Controller: Api/Admin/DeliveryAgentController.
Endpoints:
GET /api/admin/agents (List all delivery agents, filter by status, KYC status).
GET /api/admin/agents/{user} (View specific agent details, profile, vehicles, KYC docs).
POST /api/admin/agents/{user}/approve-kyc (Update kyc_status to 'APPROVED').
POST /api/admin/agents/{user}/reject-kyc (Update kyc_status to 'REJECTED', add reason).
POST /api/admin/agents/{user}/verify-vehicle/{vehicle}.
POST /api/admin/agents/{user}/toggle-activation (Activate/Deactivate agent user account).
Admin Management - Deliveries:
Controller: Api/Admin/DeliveryController.
Endpoints:
GET /api/admin/deliveries (List all deliveries, filter by status, agent, date range).
GET /api/admin/deliveries/{delivery} (View full delivery details, including status logs).
POST /api/admin/deliveries/{delivery}/cancel (Admin cancels a delivery).
GET /api/admin/orders/pending-assignment (List orders needing delivery assignment).
Admin Management - Withdrawals:
Controller: Api/Admin/AgentWithdrawalController.
Endpoints:
GET /api/admin/agent-withdrawals (List pending/processed withdrawals).
POST /api/admin/agent-withdrawals/{withdrawal}/approve (Status -> 'APPROVED').
POST /api/admin/agent-withdrawals/{withdrawal}/process (Status -> 'PROCESSING', potentially trigger payment gateway).
POST /api/admin/agent-withdrawals/{withdrawal}/complete (Status -> 'COMPLETED', add transaction ref). Updates delivery_agent_earnings (decrement pending_withdrawal_amount, increment withdrawn_amount).
POST /api/admin/agent-withdrawals/{withdrawal}/reject (Status -> 'REJECTED', add notes). Reverts amounts in delivery_agent_earnings.
Phase 4: Advanced Features & Polish
Objective: Implement real-time updates, automated assignment, mapping, and notifications.
Tasks:
Real-time Updates (WebSockets):
Integrate Laravel WebSockets or Pusher.
Create Events (e.g., NewDeliveryAssigned, DeliveryStatusUpdated, AgentLocationUpdated).
Broadcast these events when relevant actions occur (assignment, status change, location update).
Define private channels (e.g., agent.{userId}, admin.deliveries).
Frontend app listens to these channels.
Automated Assignment Algorithm:
Service Class: DeliveryAssignmentService.
Logic: Develop algorithm (e.g., nearest available agent, round-robin, load balancing). Triggered when an order becomes ready for delivery (perhaps via Observer or scheduled task).
Find suitable agents (availability_status = 'ONLINE', verified, potentially based on location/zone).
Create deliveries record with assignment_type = 'AUTOMATIC'.
Mapping & Routing Integration:
Integrate Google Maps/Mapbox API.
Endpoint: GET /api/agent/deliveries/{delivery}/route (Provide route from agent's current location -> pickup -> delivery). Requires agent's current location.
Admin View: Display agent locations on a map (requires fetching current_location periodically or via WebSockets).
Push Notifications (FCM/Pusher Beams):
Integrate FCM or similar.
Send notifications to agent app for: New assignment, delivery cancellation.
Send notifications to admin/customer for: Delivery status updates (Picked Up, Delivered, Failed).
Trigger notifications via Listeners attached to Events or directly in controllers/services.
In-App Calling:
Backend provides necessary phone numbers (customer, shop/warehouse) within the DeliveryResource or a dedicated endpoint. Actual call initiation is handled by the mobile app using the device's capabilities or a VoIP SDK.
Daily Check-in:
Endpoint: POST /api/agent/check-in (Agent performs end-of-day check-in).
Logic: Updates last_check_in_at. Could trigger validation jobs or admin notifications.
Admin Validation: May involve manual review or an admin endpoint POST /api/admin/validate-agent-day/{user}/{date}. This step might trigger the final crediting of earnings for completed deliveries of that day.
Phase 5: Testing & Deployment
Objective: Ensure quality, performance, and deploy the API changes.
Tasks:
Unit & Feature Testing (PHPUnit/Pest):
Write tests for model relationships, service logic, controller actions, API endpoint responses, and authorization rules.
Mock external services (OTP, Maps, Payment Gateways).
Integration Testing: Test flows involving multiple components (e.g., assignment -> accept -> pickup -> deliver -> earning credited).
Security Audit: Review authentication, authorization, input validation, protection against common vulnerabilities (SQLi, XSS - less relevant for API, but input sanitization matters).
Performance Optimization: Identify slow queries (Laravel Debugbar, Telescope), optimize database indexes, consider caching strategies (Redis/Memcached) for frequently accessed, non-volatile data.
Documentation: Update API documentation (Swagger/OpenAPI).
Deployment: Deploy changes to staging/production environments (migrations, code deployment, queue worker setup/restart, web server configuration).
General Considerations Throughout:
Error Handling: Implement consistent error responses (e.g., using Laravel's exception handler, standard JSON error formats).
Logging: Log critical events, errors, and decisions (e.g., assignment logic, withdrawal processing).
Configuration: Use .env and config files for external service keys, settings, etc.
Code Quality: Follow PSR standards, use Larastan/PHPStan for static analysis, keep controllers thin, use services/repositories for business logic.
This roadmap provides a structured approach. Tasks within phases can often be parallelized. Adjust priorities based on core business needs (e.g., getting manual assignment and status updates working is likely higher priority than the automated algorithm initially).

