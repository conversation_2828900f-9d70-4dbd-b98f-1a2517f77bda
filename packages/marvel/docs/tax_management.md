# Tax Management in Marvel Package

This documentation provides a comprehensive guide to how taxes are managed in the Marvel package. It covers tax configuration, implementation details, calculation methods, and integration with the checkout process.

## Table of Contents

1. [Overview](#overview)
2. [Tax Classes](#tax-classes)
3. [Tax Configuration](#tax-configuration)
4. [Tax Calculation](#tax-calculation)
5. [Tax Application](#tax-application)
6. [Tax Settings Management](#tax-settings-management)
7. [Tax API Endpoints](#tax-api-endpoints)
8. [GraphQL Integration](#graphql-integration)
9. [Examples](#examples)
10. [Best Practices](#best-practices)

## Overview

The Marvel package implements a flexible tax management system that allows for:

- Creating and managing tax classes with different rates
- Configuring taxes based on geographic locations (country, state, city, zip)
- Setting global tax rates
- Applying taxes to products and shipping
- Configuring tax settings through the admin interface

The tax system is integrated with the checkout process to automatically calculate and apply taxes to orders based on the configured tax settings.

## Tax Classes

### Tax Class Model

The tax system is built around the `Tax` model, which represents a tax class with specific rates and rules:

```php
// From Tax.php
class Tax extends Model
{
    protected $table = 'tax_classes';

    public $guarded = [];

    protected static function boot()
    {
        parent::boot();
        // Order by updated_at desc
        static::addGlobalScope('order', function (Builder $builder) {
            $builder->orderBy('updated_at', 'desc');
        });
    }
}
```

### Tax Class Schema

The tax class database schema includes the following fields:

```php
// From migration file
Schema::create('tax_classes', function (Blueprint $table) {
    $table->id();
    $table->string('country')->nullable();
    $table->string('state')->nullable();
    $table->string('zip')->nullable();
    $table->string('city')->nullable();
    $table->double('rate');
    $table->string('name')->nullable();
    $table->integer('is_global')->nullable();
    $table->integer('priority')->nullable();
    $table->boolean('on_shipping')->default(1);
    $table->timestamps();
});
```

Key fields in the tax class schema:

- **country**: The country where the tax applies
- **state**: The state/province where the tax applies
- **zip**: The ZIP/postal code where the tax applies
- **city**: The city where the tax applies
- **rate**: The tax rate as a percentage
- **name**: The name of the tax class
- **is_global**: Whether the tax is applied globally
- **priority**: The priority of the tax class (for when multiple tax classes could apply)
- **on_shipping**: Whether the tax applies to shipping charges

## Tax Configuration

### Settings Integration

Taxes are configured through the application settings. The tax class to use is specified in the `taxClass` field of the settings options:

```php
// From settings.graphql
type SettingsOptions {
    // Other settings...
    taxClass: String
    // Other settings...
}
```

This field stores the ID of the tax class that should be used for tax calculations.

### Default Tax Configuration

The Marvel package includes default tax classes in the seed data:

```sql
-- From tax_classes.sql
INSERT INTO `tax_classes` (`id`, `country`, `state`, `zip`, `city`, `rate`, `name`, `is_global`, `priority`, `on_shipping`, `created_at`, `updated_at`) VALUES
(1, 'United States', 'ny', '10001', 'ny', 2, 'Global', NULL, NULL, 1, '2021-10-25 05:05:58', '2021-11-28 07:25:09'),
(2, 'USA', 'NY', '1001', 'NY', 5, 'USA Tax', NULL, NULL, 1, '2021-11-28 07:38:04', '2021-11-28 07:38:04');
```

## Tax Calculation

### Calculation Process

The tax calculation process is handled by the `CheckoutRepository` class. The main methods involved are:

1. `calculateTax()`: Calculates the tax amount for an order
2. `getTaxClass()`: Retrieves the tax class to use for calculation
3. `getTotalTax()`: Calculates the total tax amount based on the order amount and tax rate

```php
// From CheckoutRepository.php
public function calculateTax($request, $shipping_charge, $amount)
{
    $tax_class = $this->getTaxClass($request);
    if ($tax_class) {
        return $this->getTotalTax($amount, $tax_class);
    }
    return $tax_class;
}

protected function getTaxClass($request)
{
    try {
        $settings = Settings::getData();

        // Get tax settings from settings
        $tax_class = $settings['options']['taxClass'];
        return Tax::findOrFail($tax_class);
    } catch (\Throwable $th) {
        return 0;
    }
}

protected function getTotalTax($amount, $tax_class)
{
    return ($amount * $tax_class->rate) / 100;
}
```

### Location-Based Tax Calculation

The Marvel package includes support for location-based tax calculation, although this feature is currently commented out in the code:

```php
// From CheckoutRepository.php (commented code)
// switch ($tax_type) {
//     case 'global':
//         return Tax::where('is_global', '=', true)->first();
//         break;
//     case 'billing_address':
//         $billing_address = $request['billing_address'];
//         return $this->getTaxClassByAddress($billing_address);
//         break;
//     case 'shipping_address':
//         $shipping_address = $request['shipping_address'];
//         return $this->getTaxClassByAddress($shipping_address);
//         break;
// }
```

The `getTaxClassByAddress()` method is implemented to find a tax class based on the address:

```php
protected function getTaxClassByAddress($address)
{
    return Tax::where('country', '=', $address['country'])
        ->orWhere('state', '=', $address['state'])
        ->orWhere('city', '=', $address['city'])
        ->orWhere('zip', '=', $address['zip'])
        ->orderBy('priority', 'asc')
        ->first();
}
```

## Tax Application

### Checkout Process

Taxes are applied during the checkout process. The `CheckoutRepository` calculates the tax amount and adds it to the order total:

```php
// From CheckoutRepository.php
public function verify($request)
{
    $user = $request->user();
    $wallet = $user->wallet ?? null;
    $settings = Settings::getData();
    $minimumOrderAmount = isset($settings['options']['minimumOrderAmount']) ? $settings['options']['minimumOrderAmount'] : 0;
    $unavailable_products = $this->checkStock($request['products']);
    $amount = $this->getOrderAmount($request, $unavailable_products);
    $shipping_charge = !empty($settings['options']['freeShipping']) && $settings['options']['freeShippingAmount'] <= $amount ? 0 : $this->calculateShippingCharge($request, $amount);
    $tax = $this->calculateTax($request, $shipping_charge, $amount);
    $total = $amount + $tax + $shipping_charge;
    if ($total < $minimumOrderAmount) {
        throw new HttpException(400, 'Minimum order amount is ' . $minimumOrderAmount);
    }
    return [
        'total_tax'            => $tax,
        'shipping_charge'      => $shipping_charge,
        'unavailable_products' => $unavailable_products,
        'wallet_amount' => isset($wallet->available_points) ? $wallet->available_points : 0,
        'wallet_currency' => isset($wallet->available_points) ? $this->walletPointsToCurrency($wallet->available_points) : 0
    ];
}
```

### Order Creation

When an order is created, the calculated tax amount is stored in the order's `sales_tax` field:

```php
// From OrderRepository.php (implied from the code)
$order->sales_tax = $tax;
```

## Tax Settings Management

### Admin Interface

Tax settings are managed through the admin interface. The `SettingsController` handles updating the application settings, including the tax class:

```php
// From SettingsController.php
public function update(SettingsRequest $request, $id)
{
    $settings = $this->repository->first();
    if (isset($settings->id)) {
        return $this->repository->update($request->only(['options']), $settings->id);
    } else {
        return $this->repository->create(['options' => $request['options']]);
    }
}
```

### Tax Class Management

Tax classes are managed through the `TaxController`, which provides CRUD operations for tax classes:

```php
// From TaxController.php
public function index(Request $request)
{
    return $this->repository->all();
}

public function store(CreateTaxRequest $request)
{
    $validateData = $request->validated();
    return $this->repository->create($validateData);
}

public function update(UpdateTaxRequest $request, $id)
{
    try {
        $validatedData = $request->validated();
        return $this->repository->findOrFail($id)->update($validatedData);
    } catch (MarvelException $e) {
        throw new MarvelException(NOT_FOUND);
    }
}

public function destroy($id)
{
    try {
        return $this->repository->findOrFail($id)->delete();
    } catch (MarvelException $e) {
        throw new MarvelException(NOT_FOUND);
    }
}
```

## Tax API Endpoints

### REST API

The Marvel package provides REST API endpoints for managing tax classes:

- `GET /tax-classes`: Get all tax classes
- `POST /tax-classes`: Create a new tax class
- `GET /tax-classes/{id}`: Get a specific tax class
- `PUT /tax-classes/{id}`: Update a tax class
- `DELETE /tax-classes/{id}`: Delete a tax class

### Request Validation

Tax class requests are validated using the `CreateTaxRequest` and `UpdateTaxRequest` classes:

```php
// From CreateTaxRequest.php
public function rules()
{
    return [
        'name' => ['required', 'string'],
        'country' => ['nullable', 'string'],
        'state' => ['nullable', 'string'],
        'zip' => ['nullable', 'string'],
        'city' => ['nullable', 'string'],
        'rate' => ['required', 'numeric'],
        'is_global' => ['boolean'],
        'priority' => ['integer'],
        'on_shipping' => ['boolean'],
    ];
}
```

## GraphQL Integration

### Tax Class Schema

The Marvel package provides GraphQL types and queries for tax classes:

```graphql
# From tax_class.graphql
type Tax {
    id: ID!
    name: String!
    rate: Float!
    is_global: Boolean
    country: String
    state: String
    zip: String
    city: String
    priority: Int
    on_shipping: Boolean
}

extend type Query {
    taxClasses(
        text: String @where(operator: "like", key: "name")
        orderBy: _
            @orderBy(
                columns: [
                    "updated_at"
                    "created_at"
                    "name"
                    "rate"
                    "country"
                    "state"
                ]
            )
    ): [Tax!]! @all
    taxClass(id: ID! @eq): Tax @find
}

input CreateTaxInput {
    name: String!
    rate: Float!
    is_global: Boolean
    country: String
    state: String
    zip: String
    city: String
    priority: Int
    on_shipping: Boolean
}

input UpdateTaxInput {
    id: ID!
    name: String!
    rate: Float!
    is_global: Boolean
    country: String
    state: String
    zip: String
    city: String
    priority: Int
    on_shipping: Boolean
}
```

### Settings Schema

The tax class is referenced in the settings GraphQL schema:

```graphql
# From settings.graphql
type SettingsOptions {
    // Other fields...
    taxClass: String
    // Other fields...
}

input SettingsOptionsInput {
    // Other fields...
    taxClass: String
    // Other fields...
}
```

## Examples

### Creating a Tax Class

```php
// Example of creating a tax class
$taxClass = Tax::create([
    'name' => 'California Tax',
    'country' => 'United States',
    'state' => 'CA',
    'rate' => 7.25,
    'is_global' => false,
    'priority' => 1,
    'on_shipping' => true,
]);
```

### Calculating Tax for an Order

```php
// Example of calculating tax for an order
$checkoutRepository = new CheckoutRepository();
$request = [
    'products' => [
        [
            'product_id' => 1,
            'order_quantity' => 2,
            'subtotal' => 100,
        ],
    ],
    'amount' => 100,
];
$shipping_charge = 10;
$amount = 100;
$tax = $checkoutRepository->calculateTax($request, $shipping_charge, $amount);
$total = $amount + $tax + $shipping_charge;
```

### Setting the Tax Class in Settings

```php
// Example of setting the tax class in settings
$settings = Settings::first();
$options = $settings->options;
$options['taxClass'] = 1; // ID of the tax class
$settings->options = $options;
$settings->save();
```

## Best Practices

### Tax Configuration

1. **Create Appropriate Tax Classes**: Create tax classes for each jurisdiction where you need to collect taxes.
2. **Set Priorities**: If you have overlapping tax jurisdictions, set priorities to determine which tax class takes precedence.
3. **Consider Shipping Taxes**: Decide whether taxes should apply to shipping charges by setting the `on_shipping` field.

### Tax Calculation

1. **Verify Tax Calculations**: Always verify that tax calculations are correct, especially when dealing with multiple tax jurisdictions.
2. **Handle Edge Cases**: Consider edge cases like zero-tax jurisdictions or tax-exempt products.
3. **Keep Tax Rates Updated**: Tax rates can change, so make sure to keep your tax classes updated with the latest rates.

### Tax Compliance

1. **Consult Tax Professionals**: Tax laws are complex and vary by jurisdiction. Consult with tax professionals to ensure compliance.
2. **Document Tax Collection**: Keep records of tax collection for audit purposes.
3. **Consider Tax Automation Services**: For businesses operating in multiple jurisdictions, consider integrating with tax automation services like Avalara or TaxJar.
