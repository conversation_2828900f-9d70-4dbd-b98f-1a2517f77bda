### The Refactoring Plan: From `DeliveryRepository` to a Service-Oriented Architecture

**Guiding Principles:**
1.  **Work in small, safe steps.** Each step should result in a fully working application.
2.  **Use existing tests and write new ones.** The refactoring is only successful if the application's behavior remains unchanged.
3.  **Commit frequently.** After each successful step, commit your changes. This makes it easy to revert if something goes wrong.
4.  **Communicate with the team.** If you're on a team, ensure everyone knows the refactoring is in progress to avoid merge conflicts and confusion.

---

### Phase 1: The Foundation - Isolate Pure Logic

This phase is the safest and provides immediate benefits in testability. We will extract the "pure" functions that don't depend on the database or application state.

**Step 1: Create the `RoutingCalculationService`**
1.  Create a new file: `packages/marvel/src/Services/RoutingCalculationService.php`.
2.  **Move** the following methods from `DeliveryRepository` to this new service. They are pure mathematical functions.
    *   `calculateDistance()`
    *   `calculateBoundingBox()`
    *   `calculateCentralPoint()`
    *   `calculateMaxDistanceBetweenShops()`
3.  Make these methods `public`.
4.  In `DeliveryRepository`, inject this new service via the constructor:
    ```php
    // DeliveryRepository.php
    private RoutingCalculationService $routingService;

    public function __construct(RoutingCalculationService $routingService)
    {
        parent::__construct(app()); // Or however your BaseRepository is constructed
        $this->routingService = $routingService;
    }
    ```
5.  Replace all internal calls (e.g., `$this->calculateDistance(...)`) with calls to the new service (e.g., `$this->routingService->calculateDistance(...)`).
6.  **Write Unit Tests:** Create a test file `packages/marvel/tests/Unit/Services/RoutingCalculationServiceTest.php`. Write tests for each of the four methods with known inputs and expected outputs. This is easy because they are pure functions.
7.  **Run all existing application tests.** Ensure nothing has broken.
8.  **Commit:** `refactor: Create RoutingCalculationService and move geospatial calculations`.

---

### Phase 2: Create the Service Layer and Extract Query Logic

Now we build the new home for our business logic and start moving the "finder" methods.

**Step 2: Create the `AgentDiscoveryService`**
1.  Create a new file: `app/Services/AgentDiscoveryService.php`.
2.  Inject the `RoutingCalculationService` and the `DeliveryRepository` into its constructor.
3.  **Move** the following methods from `DeliveryRepository` to `AgentDiscoveryService`:
    *   `findNearestAvailableAgents()`
    *   `findNearestAvailableAgentsForSingleOrder()`
    *   `findNearestAgentsForMultiVendor()`
    *   `findSplitDeliveryAgents()`
    *   The agent selection strategy methods (`selectAgentForAutoAssignment`, `selectAgentRoundRobin`, etc.).
    *   `getAvailableAgentsBaseQuery()`
4.  **Refactor the Call Sites:** Go to your controllers (or wherever `findNearest...` methods were being called) and change them to call the new `AgentDiscoveryService` instead of the repository. You will need to inject this new service into the controllers.
5.  **Write Feature/Unit Tests:** Create a test file for `AgentDiscoveryService`. You can now test the agent finding logic in more isolation. You'll need to mock the `DeliveryRepository` to return sample agent data and the `RoutingCalculationService` to return pre-calculated distances.
6.  **Run all tests.**
7.  **Commit:** `refactor: Create AgentDiscoveryService and move agent discovery logic`.

---

### Phase 3: Extract Command Logic

This is the core of the refactoring, moving the methods that change the state of the system.

**Step 3: Create the `DeliveryStatusService`**
1.  Create `packages/marvel/src/Services/DeliveryStatusService.php`.
2.  Inject the `DeliveryRepository`.
3.  **Move** the following methods from `DeliveryRepository` to `DeliveryStatusService`:
    *   `updateDeliveryStatus()`
    *   `addProofOfDelivery()`
    *   `confirmPayment()`
    *   `validateStatusTransition()`
4.  **Refactor Call Sites:** Update controllers to use `DeliveryStatusService`.
5.  **Write Tests:** Test the status transition logic. You can pass a mock `Delivery` model and assert that the correct methods are called on the repository and that exceptions are thrown for invalid transitions.
6.  **Run all tests.**
7.  **Commit:** `refactor: Create DeliveryStatusService for delivery state management`.

**Step 4: Create the `DeliveryAssignmentService`**
1.  Create `packages/marvel/src/Services/DeliveryAssignmentService.php`.
2.  Inject the `DeliveryRepository`, `AgentDiscoveryService`, and `DeliveryStatusService` (if needed for logging, etc.).
3.  **Move** the remaining complex business logic methods from `DeliveryRepository` to this service:
    *   `assignDelivery()`
    *   `assignConsolidatedDelivery()`
    *   `assignSplitDeliveries()`
    *   `autoAssignDelivery()` and its private helpers.
    *   `createPendingDelivery()`
    *   `getSystemUser()` (and rename it to `findOrCreateSystemUser()`).
4.  **Refactor Call Sites:** This is the final major update to the controllers. All assignment logic now goes through this service.
5.  **Write Tests:** This service is an orchestrator. Your tests will verify that it correctly calls the `AgentDiscoveryService` and then calls the appropriate methods on the `DeliveryRepository`.
6.  **Run all tests.**
7.  **Commit:** `refactor: Create DeliveryAssignmentService to handle all delivery assignments`.

---

### Phase 4: The Final Cleanup

The old repository is now a shell of its former self. Let's clean it up and solidify the new structure.

**Step 5: Slim Down the `DeliveryRepository`**
1.  Open `DeliveryRepository.php`. It should now be much smaller.
2.  **Remove** all the methods that were moved.
3.  **Keep** only the simple, data-centric methods:
    *   `model()`
    *   `boot()`
    *   `getDeliveriesForAgent()` (This is a simple query, fine for a repo)
    *   `getRecentDeliveriesForAgent()`
    *   `getDeliveryCountsForAgent()`
    *   The underlying `create()` and `update()` methods from the base repository are used by the services.
4.  Remove the injected `RoutingCalculationService` from its constructor. The repository no longer needs it.
5.  Review all remaining private methods. If any are left that were helpers for the moved public methods, delete them.
6.  **Run all tests.** The application should function identically.
7.  **Commit:** `refactor: Final cleanup of DeliveryRepository, removing all business logic`.

**Step 6: Introduce DTOs (Optional but Recommended)**
1.  Create a directory `packages/marvel/src/DataTransferObjects/Delivery`.
2.  Create DTOs like `AssignDeliveryData`, `UpdateStatusData`, etc., to replace the use of generic `array $data` in your new service methods.
3.  Refactor the service methods to accept these DTOs instead of arrays. This improves type safety and readability.
4.  Update the controllers to create these DTOs from the validated request data before passing them to the services.
5.  **Run all tests.**
6.  **Commit:** `feat: Introduce DTOs for delivery services to improve type safety`.
