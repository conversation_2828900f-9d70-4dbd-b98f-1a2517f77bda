# Delivery Agent Location Tracking

This document describes the real-time location tracking system for delivery agents in the Marvel delivery system.

## Overview

The location tracking system allows delivery agents to update their current location in real-time, enabling:
- Accurate proximity-based delivery assignment
- Real-time tracking for customers and administrators
- Location history for analytics and optimization
- Efficient route planning and agent management

## API Endpoints

### Update Agent Location

**Endpoint:** `POST /agent/location`

**Authentication:** Required (Delivery Agent permission)

**Rate Limiting:** 120 requests per minute

**Request Body:**
```json
{
    "lat": 40.7128,
    "lng": -74.0060,
    "timestamp": "2024-01-15T10:30:00Z",
    "accuracy": 5.0,
    "speed": 25.5,
    "heading": 180.0
}
```

**Request Parameters:**
- `lat` (required): Latitude coordinate (-90 to 90)
- `lng` (required): Longitude coordinate (-180 to 180)
- `timestamp` (optional): GPS timestamp (defaults to current time)
- `accuracy` (optional): GPS accuracy in meters
- `speed` (optional): Current speed in km/h
- `heading` (optional): Direction in degrees (0-360)

**Response (Success):**
```json
{
    "success": true,
    "message": "Location updated successfully",
    "data": {
        "current_location": {
            "lat": 40.7128,
            "lng": -74.0060,
            "timestamp": "2024-01-15T10:30:00Z",
            "accuracy": 5.0,
            "speed": 25.5,
            "heading": 180.0
        },
        "last_check_in_at": "2024-01-15T10:30:00Z"
    }
}
```

**Response (No Significant Change):**
```json
{
    "success": true,
    "message": "Check-in updated",
    "data": {
        "current_location": {
            "lat": 40.7128,
            "lng": -74.0060,
            "timestamp": "2024-01-15T10:25:00Z"
        },
        "last_check_in_at": "2024-01-15T10:30:00Z"
    }
}
```

### Get Current Location

**Endpoint:** `GET /agent/location`

**Authentication:** Required (Delivery Agent permission)

**Response:**
```json
{
    "success": true,
    "data": {
        "current_location": {
            "lat": 40.7128,
            "lng": -74.0060,
            "timestamp": "2024-01-15T10:30:00Z",
            "accuracy": 5.0,
            "speed": 25.5,
            "heading": 180.0
        },
        "availability_status": "ONLINE",
        "last_check_in_at": "2024-01-15T10:30:00Z"
    }
}
```

## Business Rules

### Location Update Restrictions

1. **Online Status Required**: Only agents with `availability_status = 'ONLINE'` can update their location
2. **Profile Required**: Agent must have a valid `DeliveryAgentProfile`
3. **Permission Required**: User must have `DELIVERY_AGENT` permission

### Smart Location Updates

1. **Distance Threshold**: Location is only updated if the agent has moved more than 10 meters
2. **Time-based Check-ins**: If location hasn't changed significantly, only the `last_check_in_at` timestamp is updated
3. **History Optimization**: Location history is only saved if:
   - Agent moved more than 50 meters, OR
   - More than 5 minutes have passed since last history entry

## Location History

The system automatically maintains a location history for each agent:

**Table:** `delivery_agent_location_histories`

**Fields:**
- `delivery_agent_user_id`: Agent's user ID
- `location`: JSON containing coordinates and metadata
- `timestamp`: GPS timestamp
- `delivery_id`: Associated delivery (null for general tracking)
- `created_at`: Record creation time

## Real-time Broadcasting

Location updates trigger the `AgentLocationUpdated` event, which broadcasts to:

1. **Agent Channel**: `agent.{user_id}` - For the agent's own apps
2. **Admin Channel**: `admin.agent-tracking` - For admin dashboards
3. **Delivery Channel**: `delivery.agent.{user_id}` - For delivery tracking

**Event Data:**
```json
{
    "agent_id": 123,
    "agent_name": "John Doe",
    "location": {
        "lat": 40.7128,
        "lng": -74.0060,
        "timestamp": "2024-01-15T10:30:00Z"
    },
    "availability_status": "ONLINE",
    "timestamp": "2024-01-15T10:30:00Z"
}
```

## Integration with Delivery Assignment

The location data is used by the delivery assignment algorithm:

1. **Proximity Calculation**: Uses Haversine formula to calculate distance between agent and shop
2. **Availability Filter**: Only considers agents with `current_location` data
3. **Radius Search**: Default search radius is 15km (configurable)

## Error Handling

### Common Error Responses

**401 Unauthorized:**
```json
{
    "message": "Unauthenticated."
}
```

**403 Forbidden:**
```json
{
    "message": "This action is unauthorized."
}
```

**404 Profile Not Found:**
```json
{
    "message": "Delivery agent profile not found"
}
```

**400 Offline Agent:**
```json
{
    "message": "Location update not allowed",
    "details": "Only online agents can update their location"
}
```

**422 Validation Error:**
```json
{
    "message": "The given data was invalid.",
    "errors": {
        "lat": ["Latitude must be between -90 and 90 degrees"],
        "lng": ["Longitude must be between -180 and 180 degrees"]
    }
}
```

## Implementation Notes

### Mobile App Integration

1. **Update Frequency**: Recommended 30-60 seconds for online agents
2. **Battery Optimization**: Use GPS only when agent is online
3. **Offline Handling**: Queue location updates when network is unavailable
4. **Background Updates**: Continue tracking when app is backgrounded

### Performance Considerations

1. **Rate Limiting**: 120 requests per minute per agent
2. **Database Optimization**: Uses JSON fields for location data
3. **History Pruning**: Consider implementing cleanup for old location history
4. **Caching**: Current location is cached in agent profile

### Security

1. **Authentication Required**: All endpoints require valid authentication
2. **Permission Checks**: Validates delivery agent permissions
3. **Input Validation**: Strict coordinate validation
4. **Rate Limiting**: Prevents abuse and excessive requests

## Testing

Run the location tracking tests:

```bash
php artisan test packages/marvel/tests/Feature/Delivery/AgentLocationApiTest.php
```

## Configuration

Location tracking behavior can be configured in `packages/marvel/src/Config/delivery.php`:

```php
'location_tracking' => [
    'update_threshold_meters' => 10,
    'history_threshold_meters' => 50,
    'history_time_threshold_minutes' => 5,
    'default_search_radius_km' => 15,
],
```
