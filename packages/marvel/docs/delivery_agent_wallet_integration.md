# Delivery Agent Wallet Integration

This document outlines the integration of the existing wallet system with the delivery agent earnings system, allowing agents to receive earnings directly in their wallet.

## Overview

The wallet integration provides delivery agents with the option to:
- Receive earnings directly in their wallet instead of their earnings account
- Convert earnings to wallet points using a configurable conversion rate
- Use wallet points for purchases within the platform

## Implementation Details

### Database Changes

1. **Added fields to DeliveryAgentProfile**:
   - `use_wallet_for_earnings` (boolean): Indicates whether the agent prefers to use the wallet for earnings
   - `wallet_points_conversion_rate` (decimal): Custom conversion rate for the agent (optional)

### Model Updates

1. **DeliveryAgentProfile**:
   - Added relationship with Wallet model
   - Added new fields to fillable and casts arrays

2. **DeliveryObserver**:
   - Updated to handle wallet integration
   - Added methods to credit earnings to wallet or earnings account based on agent preference

### API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/agent/profile/wallet-preference` | Toggle wallet preference for earnings |
| GET | `/agent/wallet` | Get the agent's wallet information |

### Wallet Preference Toggle

Agents can toggle their preference to receive earnings in their wallet:

```json
// Request
POST /api/agent/profile/wallet-preference
{
  "use_wallet_for_earnings": true,
  "wallet_points_conversion_rate": 10 // Optional
}

// Response
{
  "message": "Wallet preference updated successfully",
  "profile": {
    // Profile data including wallet preference
  }
}
```

### Wallet Information

Agents can view their wallet information:

```json
// Request
GET /api/agent/wallet

// Response
{
  "wallet": {
    "id": 1,
    "total_points": 1000,
    "points_used": 200,
    "available_points": 800,
    // Other wallet data
  },
  "conversion_rate": 10,
  "currency_amount": 80 // Available points converted to currency
}
```

## Earnings Flow with Wallet Integration

1. **Delivery Completion**:
   - When a delivery is completed, the system checks the agent's wallet preference
   - If wallet is preferred, earnings are converted to points and added to the wallet
   - If wallet is not preferred, earnings are added to the earnings account

2. **Points Conversion**:
   - Earnings are converted to points using the agent's custom conversion rate or the system default
   - For example, with a conversion rate of 10, $5 in earnings would be converted to 50 points

3. **Wallet Usage**:
   - Agents can use their wallet points for purchases within the platform
   - The existing wallet system handles point deduction and usage

## Benefits

1. **Flexibility**: Agents can choose how they want to receive their earnings
2. **Integration**: Leverages the existing wallet system instead of creating a separate one
3. **Incentives**: Platform can offer bonuses or promotions through wallet points
4. **Retention**: Encourages agents to spend earnings within the platform

## Future Enhancements

1. **Partial Allocation**: Allow agents to specify what percentage of earnings goes to wallet vs. earnings account
2. **Automatic Conversion**: Option to automatically convert earnings to wallet points after reaching a threshold
3. **Special Promotions**: Offer bonus points for deliveries during peak hours or in underserved areas
