# Automatic Delivery Assignment System

This document describes the automatic delivery assignment system implemented in the Marvel package.

## Overview

The automatic delivery assignment system automatically assigns delivery agents to orders when they reach a deliverable state. The system uses geographic proximity to find the nearest available agents and assigns them to orders without manual intervention. It supports both single-vendor and multi-vendor orders with intelligent consolidation logic.

## Features

- **Automatic Assignment**: Orders are automatically assigned to the nearest available delivery agent
- **Multi-Vendor Support**: Handles complex multi-vendor orders with consolidation options
- **Geographic Proximity**: Uses Haversine formula with bounding box optimization for performance
- **Multiple Selection Strategies**: Supports different agent selection algorithms
- **Intelligent Consolidation**: Automatically determines if multi-vendor orders should be consolidated
- **Retry Mechanism**: Automatically retries with expanded radius if no agents are found
- **Fallback Handling**: Creates pending deliveries for manual assignment when automatic assignment fails
- **Performance Optimized**: Uses bounding box filtering and efficient distance calculations
- **Configurable**: Fully configurable through environment variables and config files

## How It Works

### 1. Order Observer

The `OrderObserver` monitors order changes and triggers automatic assignment when:
- Order is created with deliverable status
- Order status changes to a deliverable state (`PROCESSING`, `AT_LOCAL_FACILITY`, `PENDING`)
- Payment status becomes ready (`SUCCESS`, `CASH_ON_DELIVERY`)
- `requires_delivery` is set to `true`

### 2. Auto Assignment Job

The `AutoAssignDeliveryJob` is queued to handle the actual assignment:
- Runs asynchronously to avoid blocking order creation
- Implements retry logic with exponential backoff
- Handles failures gracefully with fallback mechanisms

### 3. Agent Discovery and Selection

The system finds available agents using these criteria:
- `availability_status` = `ONLINE`
- `kyc_status` = `APPROVED`
- Has valid `current_location` data
- Within the configured search radius
- Has `DELIVERY_AGENT` permission

#### Performance Optimization
- **Bounding Box Filtering**: Pre-filters agents using geographic bounding boxes before precise distance calculation
- **Haversine Distance**: Uses accurate geographic distance calculation for final agent selection
- **Efficient Queries**: Optimized database queries with proper indexing on location fields

### 4. Multi-Vendor Order Handling

For orders with multiple vendors (parent orders with children):

#### Consolidation Decision Logic
The system automatically determines whether to consolidate deliveries based on:
- **Maximum Distance Between Shops**: Configurable threshold (default: 20km)
- **Central Point Calculation**: Geometric center of all shop locations
- **Shop-to-Center Distance**: Maximum allowed distance from any shop to central point (default: 10km)

#### Consolidation Options
- **Consolidated Delivery**: Single agent picks up from all shops in sequence
- **Split Deliveries**: Separate agents assigned to each shop/order
- **Hybrid Approach**: Combination based on geographic clustering

### 5. Selection Strategies

Multiple strategies are available for selecting agents:
- **Nearest** (default): Select the closest agent
- **Round Robin**: Distribute assignments evenly across available agents
- **Least Busy**: Select agent with fewest active deliveries
- **Best Rating**: Select highest rated agent based on performance metrics

## Configuration

### Environment Variables

```env
# Enable/disable automatic assignment
DELIVERY_AUTO_ASSIGNMENT_ENABLED=true

# Search radius in kilometers
DELIVERY_AUTO_ASSIGNMENT_RADIUS=15

# Retry configuration
DELIVERY_AUTO_ASSIGNMENT_RETRY_ATTEMPTS=3
DELIVERY_AUTO_ASSIGNMENT_RETRY_DELAY=5

# Assignment delay (minutes to wait before assignment)
DELIVERY_AUTO_ASSIGNMENT_DELAY=0

# Queue configuration
DELIVERY_AUTO_ASSIGNMENT_QUEUE=default

# Agent selection strategy
DELIVERY_AGENT_SELECTION_STRATEGY=nearest

# Fee calculation
DELIVERY_BASE_FEE=10.0
DELIVERY_PER_KM_FEE=1.0

# Maximum search radius
DELIVERY_MAX_SEARCH_RADIUS=50

# Multi-vendor consolidation settings
DELIVERY_MAX_CONSOLIDATION_DISTANCE=20.0
DELIVERY_MAX_SHOP_TO_CENTER_DISTANCE=10.0
DELIVERY_CONSOLIDATED_FEE_MULTIPLIER=1.5
DELIVERY_MULTI_PICKUP_FEE=5.0

# Performance monitoring
DELIVERY_TRACK_METRICS=true
DELIVERY_ASSIGNMENT_TIMEOUT=30
```

### Config File

The system uses `config/delivery.php` for configuration. See the file for all available options.

## API Endpoints

### Find Nearest Agents

```
GET /api/admin/orders/{order}/nearest-agents?radius=15
```

**Parameters:**
- `radius` (optional): Search radius in kilometers (default: 15, max: 100)

**Single Vendor Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Agent Name",
      "email": "<EMAIL>",
      "distance": 2.5,
      "delivery_agent_profile": {
        "availability_status": "ONLINE",
        "current_location": {"lat": 40.7128, "lng": -74.0060},
        "kyc_status": "APPROVED",
        "performance_rating": 4.8,
        "total_deliveries_completed": 150
      }
    }
  ],
  "meta": {
    "order_id": 123,
    "shop_location": {"lat": 40.7128, "lng": -74.0060},
    "search_radius_km": 15,
    "is_multi_vendor": false,
    "total_agents_found": 1
  }
}
```

**Multi-Vendor Consolidated Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Agent Name",
      "distance": 3.2
    }
  ],
  "meta": {
    "order_id": 123,
    "search_radius_km": 15,
    "is_multi_vendor": true,
    "should_consolidate": true,
    "central_point": {"lat": 40.7128, "lng": -74.0060},
    "max_distance_between_shops": 8.5,
    "total_agents_found": 1
  }
}
```

**Multi-Vendor Split Response:**
```json
{
  "success": true,
  "data": {
    "shop_1": {
      "shop_id": 1,
      "agents": [{"id": 1, "name": "Agent A", "distance": 2.1}]
    },
    "shop_2": {
      "shop_id": 2,
      "agents": [{"id": 2, "name": "Agent B", "distance": 1.8}]
    }
  },
  "meta": {
    "order_id": 123,
    "search_radius_km": 15,
    "is_multi_vendor": true,
    "should_consolidate": false,
    "total_agents_found": 2
  }
}
```

### Assign Multi-Vendor Delivery

```
POST /api/admin/orders/{order}/assign-multi-vendor-delivery
```

**Request Body:**
```json
{
  "is_consolidated": true,
  "delivery_agent_id": 1,
  "notes": "Consolidated pickup from 3 shops",
  "pickup_sequence": [1, 2, 3]
}
```

**Or for split deliveries:**
```json
{
  "is_consolidated": false,
  "agent_assignments": {
    "order_1": 1,
    "order_2": 2
  }
}
```

## Database Schema

### Deliveries Table

The `assignment_type` field indicates how the delivery was assigned:
- `MANUAL`: Assigned manually by admin
- `AUTOMATIC`: Assigned automatically by the system

### System User

A system user is automatically created for automatic assignments:
- Email: `<EMAIL>`
- Name: `System Auto Assignment`
- Has `SUPER_ADMIN` permissions

## Monitoring and Logging

### Logs

The system logs important events:
- Auto assignment job dispatch
- Successful assignments
- Failed assignments
- Retry attempts

### Metrics

Track these metrics for monitoring:
- Assignment success rate
- Average assignment time
- Agent utilization
- Failed assignment reasons

## Current Limitations and Future Improvements

### Current Limitations

1. **Multi-Vendor Auto Assignment**: Currently only supports single-vendor orders for automatic assignment
2. **Real-Time Agent Tracking**: No integration with real-time agent location updates
3. **Time-Based Availability**: Doesn't consider agent working hours or time-based availability
4. **Load Balancing**: Limited load balancing across agents
5. **Route Optimization**: No advanced route optimization for multi-pickup deliveries

### Planned Improvements

1. **Enhanced Multi-Vendor Support**:
   - Automatic assignment for multi-vendor orders
   - Intelligent consolidation decisions
   - Route optimization for pickup sequences

2. **Real-Time Integration**:
   - WebSocket integration for live agent tracking
   - Dynamic availability updates
   - Real-time assignment notifications

3. **Advanced Analytics**:
   - Agent performance metrics
   - Assignment success rate tracking
   - Delivery time predictions

4. **Machine Learning Integration**:
   - Predictive agent selection
   - Dynamic radius adjustment
   - Demand forecasting

## Error Handling

### Retry Logic

The system retries failed assignments with:
- Exponential backoff delay (5, 10, 20 minutes)
- Expanded search radius on retry (50% increase per attempt)
- Maximum retry attempts (configurable, default: 3)

### Fallback Mechanisms

When automatic assignment fails:
1. Creates a `PENDING_ASSIGNMENT` delivery record
2. Logs the failure reason with detailed context
3. Allows manual assignment by admin
4. Sends notifications to administrators

### Non-Retryable Errors

Some errors don't trigger retries:
- Missing shop location data
- Invalid coordinates
- Order doesn't have associated shop
- System configuration errors

## Testing

Run the automatic assignment tests:

```bash
php artisan test packages/marvel/tests/Unit/Delivery/AutoAssignmentTest.php
```

## Troubleshooting

### Common Issues

1. **No agents found**: Check agent availability status and location data
2. **Jobs not processing**: Ensure queue workers are running
3. **Assignment not triggered**: Verify order status and configuration
4. **Invalid coordinates**: Check shop location data format

### Debug Commands

```bash
# Check queue status
php artisan queue:work

# View failed jobs
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all
```

## Performance Considerations

### Database Optimization
- **Indexing**: Ensure proper indexes on location fields and agent status
- **Bounding Box Queries**: Use geographic bounding boxes to pre-filter agents
- **Connection Pooling**: Use database connection pooling for high-load scenarios

### Caching Strategy
- **Agent Location Cache**: Cache frequently accessed agent locations
- **Configuration Cache**: Cache delivery configuration settings
- **Shop Location Cache**: Cache shop location data

### Queue Management
- **Redis Queue**: Use Redis for queue management in production
- **Worker Scaling**: Scale queue workers based on order volume
- **Priority Queues**: Implement priority queues for urgent deliveries

### Monitoring
- **Performance Metrics**: Track assignment times and success rates
- **Resource Usage**: Monitor CPU and memory usage during peak times
- **Database Performance**: Monitor query performance and optimization

## Security

### Access Control
- **System User**: Restricted system user for automatic operations
- **API Authentication**: All endpoints require proper authentication
- **Permission Checks**: Role-based access control for all operations

### Data Protection
- **Location Privacy**: Agent location data is encrypted and access-controlled
- **Audit Logging**: All assignments and changes are logged for audit
- **Secure Communication**: All API communications use HTTPS

### Compliance
- **GDPR Compliance**: Agent location data handling follows privacy regulations
- **Data Retention**: Configurable data retention policies for logs and metrics

## Technical Implementation Details

### Core Classes and Methods

#### DeliveryRepository
- `findNearestAvailableAgents()`: Finds agents within radius for single-vendor orders
- `findNearestAgentsForMultiVendor()`: Handles multi-vendor order agent discovery
- `autoAssignDelivery()`: Main automatic assignment logic
- `createPendingDelivery()`: Creates pending deliveries for failed assignments

#### OrderObserver
- `created()`: Triggers assignment on order creation
- `updated()`: Triggers assignment on status changes
- `shouldTriggerAutoAssignment()`: Determines if assignment should be triggered

#### AutoAssignDeliveryJob
- `handle()`: Main job execution logic
- `shouldProceedWithAssignment()`: Pre-execution validation
- `handleAssignmentFailure()`: Retry and fallback logic

### Distance Calculation Algorithm

The system uses the Haversine formula for accurate geographic distance calculation:

```php
public function calculateDistance($lat1, $lng1, $lat2, $lng2)
{
    $earthRadius = 6371; // Earth's radius in kilometers

    $lat1Rad = deg2rad($lat1);
    $lng1Rad = deg2rad($lng1);
    $lat2Rad = deg2rad($lat2);
    $lng2Rad = deg2rad($lng2);

    $deltaLat = $lat2Rad - $lat1Rad;
    $deltaLng = $lng2Rad - $lng1Rad;

    $a = sin($deltaLat / 2) * sin($deltaLat / 2) +
         cos($lat1Rad) * cos($lat2Rad) *
         sin($deltaLng / 2) * sin($deltaLng / 2);

    $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

    return $earthRadius * $c;
}
```

### Bounding Box Optimization

For performance, the system uses bounding box filtering before precise distance calculation:

```php
// Calculate bounding box for efficient pre-filtering
$latRange = $radiusKm / 111; // Approximate km per degree latitude
$lngRange = $radiusKm / (111 * cos(deg2rad($centerLat)));

$bbox = [
    'minLat' => $centerLat - $latRange,
    'maxLat' => $centerLat + $latRange,
    'minLng' => $centerLng - $lngRange,
    'maxLng' => $centerLng + $lngRange
];
```

### Multi-Vendor Consolidation Logic

The system determines consolidation feasibility using:

1. **Maximum Distance Check**: Distance between any two shops
2. **Central Point Calculation**: Geometric center of all shop locations
3. **Radius Validation**: All shops within acceptable distance from center

```php
// Calculate central point (geometric center)
$centralPoint = [
    'lat' => $totalLat / $shopCount,
    'lng' => $totalLng / $shopCount
];

// Check if consolidation is feasible
$shouldConsolidate = $maxDistanceBetweenShops <= $maxConsolidationDistance &&
                    $maxShopToCenterDistance <= $maxShopToCenterThreshold;
```
