# Delivery System Improvements

This document outlines the improvements made to the delivery system's financial handling, focusing on transaction tracking, payment flexibility, and scheduled payments.

## Overview

The improvements address several key areas:

1. **Transaction Tracking**: A unified system to track all financial transactions
2. **Payment Provider Flexibility**: Support for various payment methods including Mobile Money
3. **Scheduled Payments**: Automated periodic payments to delivery agents

## Transaction Tracking System

### Transaction Model

We've created a comprehensive transaction model that tracks all financial movements:

```php
class DeliveryAgentTransaction extends Model
{
    // Transaction types
    public const TYPE_EARNING = 'EARNING';
    public const TYPE_WITHDRAWAL = 'WITHDRAWAL';
    public const TYPE_REFUND = 'REFUND';
    public const TYPE_ADJUSTMENT = 'ADJUSTMENT';

    // Reference types
    public const REF_DELIVERY = 'DELIVERY';
    public const REF_WITHDRAWAL = 'WITHDRAWAL';
    public const REF_MANUAL = 'MANUAL';

    // Status types
    public const STATUS_PENDING = 'PENDING';
    public const STATUS_COMPLETED = 'COMPLETED';
    public const STATUS_FAILED = 'FAILED';
    public const STATUS_CANCELLED = 'CANCELLED';

    // Payment methods
    public const METHOD_MOBILE_MONEY = 'MOBILE_MONEY';
    public const METHOD_BANK_TRANSFER = 'BANK_TRANSFER';
    public const METHOD_CASH = 'CASH';
    public const METHOD_OTHER = 'OTHER';
}
```

### Transaction Repository

The `DeliveryAgentTransactionRepository` provides methods for:

- Creating earning transactions
- Creating withdrawal transactions
- Creating manual adjustment transactions
- Retrieving transaction history

### Automatic Transaction Creation

Transactions are automatically created when:

1. **Earnings are credited**: When a delivery is completed
2. **Withdrawals are requested**: When an agent requests a withdrawal
3. **Withdrawals are processed**: When an admin completes a withdrawal
4. **Manual adjustments**: When an admin makes a manual balance adjustment

## Payment Provider Flexibility

### Multiple Payment Methods

The system now supports various payment methods:

- **Mobile Money**: Orange Money, MTN Mobile Money, etc.
- **Bank Transfers**: Traditional bank transfers
- **Cash**: Manual cash payments
- **Other**: Any other payment method

### Payment Information Structure

Payment information is stored in a flexible JSON format:

```json
{
  "method": "MOBILE_MONEY",
  "provider": "ORANGE_MONEY",
  "details": {
    "phone_number": "+**********",
    "account_name": "John Doe"
  }
}
```

### Provider-Specific Fields

Different providers can have different required fields:

- **Orange Money**: Phone number, account name
- **Bank Transfer**: Bank name, account number, account name, branch
- **MTN Mobile Money**: Phone number, account name

## Scheduled Payments

### Command for Automated Payments

We've created a command for processing scheduled payments:

```bash
php artisan marvel:process-scheduled-payments
```

Options:
- `--min-amount`: Minimum amount for automatic payment (default: 50)
- `--payment-method`: Filter by payment method
- `--dry-run`: Run without making actual payments

### Scheduling the Command

The command can be scheduled in Laravel's scheduler:

```php
// In app/Console/Kernel.php
protected function schedule(Schedule $schedule)
{
    // Run weekly on Sunday at midnight
    $schedule->command('marvel:process-scheduled-payments')
             ->weekly()
             ->sundays()
             ->at('00:00');
}
```

### Payment Processing Flow

1. Find all active agents with balances above the minimum amount
2. Check if they have valid payment information
3. Create withdrawal requests automatically
4. Update earnings and create transaction records
5. Admin can then process these withdrawals through the admin interface

## API Endpoints

### Agent Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/agent/transactions` | List all transactions |
| GET | `/agent/transactions/{id}` | View transaction details |

### Admin Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/admin/agent-transactions` | List all transactions |
| GET | `/admin/agent-transactions/{id}` | View transaction details |
| POST | `/admin/agents/{id}/create-adjustment` | Create a manual balance adjustment |

## Integration with Existing System

These improvements integrate with the existing payment infrastructure:

1. **Transaction Records**: Complement the existing order and payment records
2. **Payment Methods**: Extend the existing payment gateway types
3. **Admin Interface**: Follow the same patterns as other admin functionality

## Future Enhancements

Potential future enhancements include:

1. **Direct API Integration**: Connect directly with Mobile Money APIs
2. **Batch Processing**: Process multiple payments in a single operation
3. **Payment Analytics**: Provide insights into payment patterns and trends
4. **Tax Handling**: Automatically calculate and withhold taxes
5. **Multi-Currency Support**: Handle payments in different currencies
