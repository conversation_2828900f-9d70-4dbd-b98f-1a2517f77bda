# Payment and Transaction System in Marvel Package

This document provides an overview of how the Marvel package handles payments, transactions, and financial operations. It serves as a reference for understanding the existing payment infrastructure before implementing new financial features.

## Table of Contents

1. [Payment Gateways](#payment-gateways)
2. [Payment Processing Flow](#payment-processing-flow)
3. [Wallet System](#wallet-system)
4. [Payment Intents](#payment-intents)
5. [Transaction Handling](#transaction-handling)
6. [Payment Status Management](#payment-status-management)
7. [Integration with Delivery System](#integration-with-delivery-system)

## Payment Gateways

### Supported Payment Gateways

The Marvel package supports multiple payment gateways through a flexible gateway architecture:

```php
// From PaymentGatewayType.php
public const CASH_ON_DELIVERY    = 'CASH_ON_DELIVERY';
public const CASH                = 'CASH';
public const FULL_WALLET_PAYMENT = 'FULL_WALLET_PAYMENT';
public const STRIPE              = 'STRIPE';
public const PAYPAL              = 'PAYPAL';
public const RAZORPAY            = 'RAZORPAY';
public const MOLLIE              = 'MOLLIE';
public const PAYSTACK            = 'PAYSTACK';
public const XENDIT              = 'XENDIT';
public const IYZICO              = 'IYZICO';
public const BKASH               = 'BKASH';
public const PAYMONGO            = 'PAYMONGO';
public const FLUTTERWAVE         = 'FLUTTERWAVE';
```

### Gateway Configuration

Payment gateways are configured in `packages/marvel/config/shop.php`:

```php
'active_payment_gateway' => env('ACTIVE_PAYMENT_GATEWAY', 'stripe'),

'razorpay' => [
    'key_id'         => env('RAZORPAY_KEY_ID'),
    'key_secret'     => env('RAZORPAY_KEY_SECRET'),
    'webhook_secret' => env('RAZORPAY_WEBHOOK_SECRET_KEY')
],

'mollie' => [
    'mollie_key'  => env('MOLLIE_KEY'),
    'webhook_url' => env('MOLLIE_WEBHOOK_URL', url('webhooks/mollie')),
],

'stripe' => [
    'api_secret'     => env('STRIPE_API_KEY'),
    'webhook_secret' => env('STRIPE_WEBHOOK_SECRET_KEY')
],
// Additional gateways...
```

## Payment Processing Flow

### Payment Interface

All payment gateways implement the `PaymentInterface` which standardizes methods like:

- `createCustomer`
- `attachPaymentMethodToCustomer`
- `getIntent`
- `confirmPaymentIntent`
- `handleWebHooks`
- `verify`

### Order Creation with Payment

When an order is created, the payment flow is as follows:

1. The order is created with a unique tracking number
2. If using wallet points, they are deducted from the user's wallet
3. A payment intent is created based on the selected payment gateway
4. The order status is updated based on the payment method:
   - For COD/Cash: Order status is set to PROCESSING
   - For other payment methods: Order status is set to PENDING and payment status to PENDING

```php
// From OrderRepository.php
if ($payment_gateway_type === PaymentGatewayType::CASH_ON_DELIVERY || $payment_gateway_type === PaymentGatewayType::CASH) {
    $this->orderStatusManagementOnCOD($order, OrderStatus::PENDING, OrderStatus::PROCESSING);
} else {
    $this->orderStatusManagementOnPayment($order, OrderStatus::PENDING, PaymentStatus::PENDING);
}
```

### Payment Intent Creation

Payment intents are created through the gateway-specific implementation:

```php
// From PaymentTrait.php
public function createPaymentIntent(Order $order, Request $request, string $payment_gateway): array
{
    $created_intent = [
        "amount" => $order->paid_total - intval($order?->wallet?->amount),
        "order_tracking_number" => $order->tracking_number,
    ];
    if ($request->user() !== null) {
        $created_intent["user_email"] = $order->customer->email;
    }

    if ($request->user() !== null && strtoupper($payment_gateway) === PaymentGatewayType::STRIPE) {
        $customer = $this->createPaymentCustomer($request);
        $created_intent["customer"] = $customer["customer_id"];
    }
    
    // Additional gateway-specific logic...
}
```

## Wallet System

### Wallet Model

The system includes a wallet feature for users:

```php
// From Wallet.php
class Wallet extends Model
{
    protected $table = 'wallets';

    public $guarded = [];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }
}
```

### Wallet Structure

The wallet table includes:

```php
// From migration file
Schema::create('wallets', function (Blueprint $table) {
    $table->id();
    $table->double('total_points')->default(0);
    $table->double('points_used')->default(0);
    $table->double('available_points')->default(0);
    $table->unsignedBigInteger('customer_id')->nullable();
    $table->foreign('customer_id')->references('id')->on('users')->onDelete('cascade');
    $table->timestamps();
});
```

### Wallet Points Conversion

The system converts between currency and wallet points:

```php
// From WalletsTrait.php
private function currencyToWalletPoints($currency)
{
    $currencyToWalletRatio = $this->currencyToWalletRatio();
    $points = $currency * $currencyToWalletRatio;
    return intval($points);
}

public function walletPointsToCurrency($points)
{
    $currencyToWalletRatio = $this->currencyToWalletRatio();
    $currency = $points / $currencyToWalletRatio;
    return round($currency, 2);
}
```

### Wallet Usage in Orders

Wallets can be used for full or partial payment of orders:

```php
// From OrderRepository.php
if ($useWalletPoints && $user) {
    $wallet = $user->wallet;
    $amount = $this->walletPointsToCurrency($wallet->available_points);
    
    if ($amount !== null && $amount <= 0) {
        $request['order_status'] = OrderStatus::COMPLETED;
        $request['payment_gateway'] = PaymentGatewayType::FULL_WALLET_PAYMENT;
        $request['payment_status'] = PaymentStatus::SUCCESS;
        $order = $this->createOrder($request);
        $this->storeOrderWalletPoint($request['paid_total'], $order->id);
        $this->manageWalletAmount($request['paid_total'], $user->id);
        return $order;
    }
}
```

## Payment Intents

### Payment Intent Model

Payment intents are stored in the database:

```php
// From PaymentIntent.php
class PaymentIntent extends Model
{
    use SoftDeletes;
    use TranslationTrait;

    protected $table = 'payment_intents';
    public $guarded = [];
    protected $casts = [
        'payment_intent_info' => 'json',
    ];

    public function order(): belongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }
}
```

### Payment Intent Creation

Payment intents are created and saved:

```php
// From PaymentTrait.php
public function savePaymentIntent($order, $payment_gateway, $request)
{
    return PaymentIntent::create([
        'order_id'            => $order->id,
        "tracking_number"     => $order->tracking_number,
        "payment_gateway"     => $payment_gateway,
        "payment_intent_info" => $this->createPaymentIntent($order, $request, $payment_gateway),
    ]);
}
```

## Transaction Handling

The system doesn't have a dedicated transactions table but tracks financial operations through:

1. **Orders**: Records the main financial transactions
2. **Payment Intents**: Stores payment processing details
3. **Wallets**: Tracks user wallet balances and usage
4. **Order Wallet Points**: Records wallet points used in orders

```php
// From migration file
Schema::create('order_wallet_points', function (Blueprint $table) {
    $table->id();
    $table->double('amount')->nullable();
    $table->unsignedBigInteger('order_id')->nullable();
    $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
    $table->timestamps();
});
```

## Payment Status Management

### Payment Status Enum

The system defines various payment statuses:

```php
// From PaymentStatus.php
public const PENDING                = 'payment-pending';
public const PROCESSING             = 'payment-processing';
public const SUCCESS                = 'payment-success';
public const FAILED                 = 'payment-failed';
public const REVERSAL               = 'payment-reversal';
public const REFUNDED               = 'payment-refunded';
public const CASH_ON_DELIVERY       = 'payment-cash-on-delivery';
public const CASH                   = 'payment-cash';
public const WALLET                 = 'payment-wallet';
public const AWAITING_FOR_APPROVAL  = 'payment-awaiting-for-approval';
public const DEFAULT_PAYMENT_STATUS = 'payment-pending';
```

### Status Transitions

Payment status transitions are managed through traits:

```php
// From OrderStatusManagerWithPaymentTrait.php
public function orderStatusManagementOnPayment($order, $orderStatus, $paymentStatus)
{
    $order->order_status = $orderStatus;
    $order->payment_status = $paymentStatus;
    $order->save();
}
```

## Integration with Delivery System

### Delivery Payment Handling

The delivery system we've implemented extends the existing payment infrastructure:

1. **Delivery Fee**: Stored in the `deliveries` table
2. **Agent Earnings**: Calculated based on completed deliveries
3. **Withdrawal System**: Allows agents to request withdrawals of their earnings

### Key Differences

The delivery system's financial handling differs from the main system in several ways:

1. **Manual Processing**: Withdrawals are processed manually by admins
2. **Multi-step Approval**: Withdrawals go through PENDING → APPROVED → PROCESSING → COMPLETED states
3. **No Direct Gateway Integration**: Payments to agents are handled outside the system
4. **Earnings Calculation**: Automatically calculated when deliveries are completed

### Future Integration Opportunities

The delivery system could be further integrated with the main payment system:

1. **Automated Payments**: Integrate with payment gateways for automatic agent payments
2. **Transaction Records**: Create a unified transaction history
3. **Wallet Integration**: Allow agents to receive earnings in a wallet
4. **Payment Scheduling**: Implement automatic periodic payments

## Conclusion

The Marvel package has a robust payment and transaction system that supports multiple payment gateways, wallet functionality, and flexible payment processing. The delivery system we've implemented extends this infrastructure with agent earnings and withdrawal management, while maintaining compatibility with the existing architecture.

When implementing new financial features, it's important to follow the established patterns and leverage the existing payment infrastructure where possible.
