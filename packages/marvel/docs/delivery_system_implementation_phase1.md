# Delivery System Implementation - Phase 1

This document outlines the implementation details of Phase 1 of the delivery system for the Marvel package.

## Overview

Phase 1 focuses on the foundational components of the delivery system:
- Database structure
- Role and permission setup
- Agent profile and KYC management
- Vehicle management
- Availability status management

## Database Structure

### Migrations

We've created the following migrations in `packages/marvel/database/migrations/delivery/`:

1. **`2025_04_03_050224_create_delivery_agent_profiles_table.php`**
   - Stores delivery agent profile information
   - Includes fields for availability status, current location, KYC status, and performance metrics

2. **`2025_04_03_050245_create_vehicles_table.php`**
   - Stores vehicle information for delivery agents
   - Includes fields for vehicle type, registration details, and verification status

3. **`2025_04_03_050256_create_deliveries_table.php`**
   - Stores delivery information
   - Includes fields for tracking delivery status, addresses, timestamps, and fees

4. **`2025_04_03_050314_create_delivery_status_logs_table.php`**
   - Logs changes to delivery status
   - Includes fields for status, location, and timestamps

5. **`2025_04_03_050324_create_delivery_agent_earnings_table.php`**
   - Tracks earnings for delivery agents
   - Includes fields for total earnings, withdrawn amounts, and current balance

6. **`2025_04_03_050334_create_delivery_agent_withdrawals_table.php`**
   - Tracks withdrawal requests from delivery agents
   - Includes fields for amount, status, and payment details

7. **`2025_04_03_050351_create_delivery_agent_location_history_table.php`**
   - Tracks location history of delivery agents
   - Includes fields for location, timestamp, and associated delivery

8. **`2025_04_03_050438_add_delivery_fields_to_orders_table.php`**
   - Adds delivery-related fields to the existing orders table
   - Includes fields for delivery ID and delivery requirement flag

9. **`2025_04_03_050933_add_foreign_key_to_orders_table.php`**
   - Adds foreign key constraint to the orders table
   - Resolves circular dependency between orders and deliveries tables

10. **`2025_04_03_053224_create_delivery_agent_role_and_permissions.php`**
    - Adds the delivery agent role and permission
    - Assigns appropriate permissions to the role

## Models

We've created the following models in `packages/marvel/src/Database/Models/`:

1. **`DeliveryAgentProfile.php`**
   - Implements HasMedia for KYC document uploads
   - Defines relationships with User, Vehicle, and other delivery-related models

2. **`Vehicle.php`**
   - Implements HasMedia for vehicle document uploads
   - Defines relationship with delivery agent

3. **`Delivery.php`**
   - Implements HasMedia for proof of delivery uploads
   - Defines relationships with Order, User, and status logs

4. **`DeliveryStatusLog.php`**
   - Tracks status changes for deliveries
   - Defines relationships with Delivery and User

5. **`DeliveryAgentEarning.php`**
   - Tracks earnings for delivery agents
   - Defines relationships with User and withdrawals

6. **`DeliveryAgentWithdrawal.php`**
   - Manages withdrawal requests
   - Defines relationships with User

7. **`DeliveryAgentLocationHistory.php`**
   - Tracks location history
   - Defines relationships with User and Delivery

We've also updated existing models:
- **`Order.php`** - Added relationship with Delivery
- **`User.php`** - Added relationships with delivery agent models

## Roles and Permissions

We've extended the existing role and permission system:

1. Added `DELIVERY_AGENT` to the `Role` enum in `packages/marvel/src/Enums/Role.php`
2. Added `DELIVERY_AGENT` to the `Permission` enum in `packages/marvel/src/Enums/Permission.php`
3. Created a migration to add the role and permission to the database

This follows the project's existing pattern where each role has a corresponding permission with the same name.

## Repositories

We've created the following repositories in `packages/marvel/src/Database/Repositories/`:

1. **`DeliveryAgentProfileRepository.php`**
   - Manages delivery agent profiles
   - Provides methods for updating profiles, KYC status, and availability status

2. **`VehicleRepository.php`**
   - Manages vehicles
   - Provides methods for creating, updating, and setting active vehicles

## API Resources

We've created the following API resources in `packages/marvel/src/Http/Resources/`:

1. **`DeliveryAgentProfileResource.php`**
   - Transforms DeliveryAgentProfile model to JSON response
   - Includes media URLs for KYC documents

2. **`VehicleResource.php`**
   - Transforms Vehicle model to JSON response
   - Includes media URLs for vehicle documents

## Request Validation

We've created the following request validation classes in `packages/marvel/src/Http/Requests/`:

1. **`DeliveryAgentProfileUpdateRequest.php`**
   - Validates profile update requests
   - Includes rules for location and KYC documents

2. **`VehicleCreateRequest.php`**
   - Validates vehicle creation requests
   - Includes rules for vehicle type, registration, and documents

3. **`VehicleUpdateRequest.php`**
   - Validates vehicle update requests
   - Includes rules for vehicle details and documents

4. **`AvailabilityUpdateRequest.php`**
   - Validates availability status update requests
   - Includes rules for status and location

## Controllers

We've created the following controllers in `packages/marvel/src/Http/Controllers/Agent/`:

1. **`ProfileController.php`**
   - Manages delivery agent profiles
   - Provides endpoints for viewing and updating profiles
   - Handles KYC document submission

2. **`VehicleController.php`**
   - Manages vehicles
   - Provides endpoints for CRUD operations on vehicles
   - Handles setting active vehicles

3. **`AvailabilityController.php`**
   - Manages availability status
   - Provides endpoints for viewing and updating availability status
   - Enforces business rules for going online (KYC approval, active vehicle)

## Routes

We've added the following routes in `packages/marvel/src/Rest/Routes.php`:

```php
/**
 * Delivery Agent Routes
 */
Route::group(['prefix' => 'agent', 'middleware' => ['api', 'auth:sanctum']], function () {
    // Agent Profile Routes
    Route::get('profile', [AgentProfileController::class, 'show']);
    Route::put('profile', [AgentProfileController::class, 'update']);
    Route::post('profile/kyc', [AgentProfileController::class, 'submitKyc']);
    
    // Vehicle Routes
    Route::apiResource('vehicles', AgentVehicleController::class);
    Route::post('vehicles/{vehicle}/set-active', [AgentVehicleController::class, 'setActive']);
    
    // Availability Routes
    Route::get('availability', [AgentAvailabilityController::class, 'show']);
    Route::put('availability', [AgentAvailabilityController::class, 'update']);
});
```

## Constants

We've created a constants file in `packages/marvel/src/Helpers/DeliveryConstants.php` to define error messages and other constants used throughout the delivery system.

## API Endpoints

### Agent Profile Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/agent/profile` | Get the delivery agent profile |
| PUT | `/agent/profile` | Update the delivery agent profile |
| POST | `/agent/profile/kyc` | Submit KYC documents |

### Vehicle Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/agent/vehicles` | List all vehicles |
| POST | `/agent/vehicles` | Create a new vehicle |
| GET | `/agent/vehicles/{id}` | Get a specific vehicle |
| PUT | `/agent/vehicles/{id}` | Update a vehicle |
| DELETE | `/agent/vehicles/{id}` | Delete a vehicle |
| POST | `/agent/vehicles/{id}/set-active` | Set a vehicle as active |

### Availability Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/agent/availability` | Get current availability status |
| PUT | `/agent/availability` | Update availability status |

## Business Rules

1. **KYC Verification**
   - Delivery agents must submit KYC documents (ID, license, address proof)
   - KYC status progresses from PENDING → SUBMITTED → APPROVED/REJECTED
   - Agents cannot go online until KYC is approved

2. **Vehicle Management**
   - Delivery agents can have multiple vehicles
   - Vehicles must be verified by admin before use
   - Only one vehicle can be active at a time

3. **Availability Status**
   - Agents can set their status to ONLINE, OFFLINE, or ON_DELIVERY
   - To go ONLINE, agents must have:
     - Approved KYC
     - An active vehicle
     - Verified vehicle

## Next Steps

The next phases will implement:
- Delivery Assignment
- Delivery Status Updates
- Earnings & Withdrawals
- Admin Dashboard for Delivery Management
