# Stripe Integration in Marvel Package

This documentation provides a comprehensive guide to the Stripe payment gateway integration in the Marvel package. It covers configuration, implementation details, webhook handling, and troubleshooting.

## Table of Contents

1. [Overview](#overview)
2. [Requirements](#requirements)
3. [Configuration](#configuration)
4. [Implementation Details](#implementation-details)
5. [Payment Flow](#payment-flow)
6. [Webhook Handling](#webhook-handling)
7. [Card Management](#card-management)
8. [Testing](#testing)
9. [Troubleshooting](#troubleshooting)
10. [Examples](#examples)

## Overview

The Marvel package integrates Stripe as one of its primary payment gateways, allowing customers to make payments using credit cards and other payment methods supported by Stripe. The integration uses the official `stripe/stripe-php` package (version 13.1.0) to interact with Stripe's API.

Stripe integration in the Marvel package supports:
- Creating payment intents
- Processing payments
- Handling webhooks for payment status updates
- Saving and managing customer payment methods (cards)
- Customer management

## Requirements

To use Stripe integration in the Marvel package, you need:

1. A Stripe account
2. Stripe API keys (Secret Key)
3. Properly configured webhook endpoints
4. <PERSON><PERSON> 9 or 10

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```
# Payment -> Stripe
ACTIVE_PAYMENT_GATEWAY=Stripe
DEFAULT_CURRENCY=USD

# Stripe API Keys
STRIPE_API_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET_KEY=whsec_your_webhook_secret_key

# Frontend URLs
SHOP_URL=https://your-frontend-domain.com
```

### Configuration in `shop.php`

The Stripe configuration is defined in `packages/marvel/config/shop.php`:

```php
'stripe' => [
    'api_secret'     => env('STRIPE_API_KEY'),
    'webhook_secret' => env('STRIPE_WEBHOOK_SECRET_KEY')
],
```

### Setting Up Stripe Webhooks

1. Log in to the [Stripe Dashboard](https://dashboard.stripe.com/)
2. Navigate to Developers > Webhooks
3. Click "Add endpoint"
4. Enter your webhook URL: `https://your-domain.com/webhooks/stripe`
5. Select the following events:
   - `charge.succeeded`
   - `charge.pending`
   - `charge.failed`
   - `charge.refunded`
6. Save the webhook and copy the Signing Secret
7. Add the Signing Secret to your `.env` file as `STRIPE_WEBHOOK_SECRET_KEY`

## Implementation Details

### Stripe Class

The Stripe integration is implemented in the `Marvel\Payments\Stripe` class, which extends the `Base` class and implements the `PaymentInterface`. This class handles all Stripe-specific payment operations.

Key methods in the Stripe class:

- `__construct()`: Initializes the Stripe client with the API key from configuration
- `createCustomer($request)`: Creates a Stripe customer for the authenticated user
- `attachPaymentMethodToCustomer($retrieved_payment_method, $request)`: Attaches a payment method to a customer
- `detachPaymentMethodToCustomer($retrieved_payment_method)`: Detaches a payment method from a customer
- `getIntent($data)`: Creates a payment intent with Stripe
- `retrievePaymentIntent($payment_intent_id)`: Retrieves a payment intent by ID
- `confirmPaymentIntent($payment_intent_id, $data)`: Confirms a payment intent
- `handleWebHooks($request)`: Processes webhook events from Stripe
- `setIntent($data)`: Creates a setup intent for saving payment methods

### Database Models

The Stripe integration uses the following database models:

1. **PaymentGateway**: Stores customer information for payment gateways
   - Contains the Stripe customer ID
   - Related to the User model

2. **PaymentMethod**: Stores payment method information
   - Contains card details (last4, expiry, etc.)
   - Related to the PaymentGateway model

3. **PaymentIntent**: Stores payment intent information
   - Contains the Stripe payment intent ID and other payment details
   - Related to the Order model

## Payment Flow

The Stripe payment flow in the Marvel package works as follows:

1. **Create Payment Intent**:
   - When a customer checks out, the system creates a payment intent via the `getIntent()` method
   - This generates a Stripe payment intent with the specified amount and currency
   - The method returns a client secret that the frontend uses to complete the payment

2. **Frontend Payment Processing**:
   - The frontend uses Stripe.js or Stripe Elements to collect payment details
   - The payment is confirmed using the client secret from the payment intent

3. **Verify Payment**:
   - Stripe sends webhook events to the configured webhook URL
   - The system processes these events and updates the order status accordingly

4. **Update Order Status**:
   - Based on the payment status from Stripe, the order status is updated
   - For successful payments, the order status is set to `PROCESSING`

## Webhook Handling

The Marvel package handles Stripe webhooks through the `WebHookController` and the `Stripe` class:

1. Webhook events are received at `/webhooks/stripe`
2. The `WebHookController::stripe()` method passes the request to `Payment::handleWebHooks()`
3. The `handleWebHooks()` method in the `Stripe` class:
   - Verifies the webhook signature using the webhook secret
   - Processes the event based on its type
   - Updates the order status accordingly

### Supported Webhook Events

The system handles the following Stripe webhook events:

- `charge.succeeded`: Payment was completed successfully
  - Updates order status to `PROCESSING`
  - Updates payment status to `SUCCESS`

- `charge.pending`: Payment is pending
  - Updates order status to `PENDING`
  - Updates payment status to `AWAITING_FOR_APPROVAL`

- `charge.failed`: Payment failed
  - Updates order status to `PENDING`
  - Updates payment status to `FAILED`

- `charge.refunded`: Payment was refunded
  - Updates order status to `REFUNDED`
  - Updates payment status to `REFUNDED`

## Card Management

The Marvel package provides functionality for saving and managing customer payment methods (cards):

### Saving a Card

1. The frontend collects card details using Stripe Elements
2. A setup intent is created using the `setIntent()` method
3. The card is attached to the customer using the `attachPaymentMethodToCustomer()` method
4. The card details are stored in the `payment_methods` table

### Retrieving Saved Cards

Saved cards can be retrieved through the `PaymentMethodController`:

```php
// From PaymentMethodController.php
public function index()
{
    return $this->repository->all();
}
```

### Setting a Default Card

Customers can set a default card for future payments:

```php
// From PaymentMethodController.php
public function setDefaultCard(Request $request)
{
    return $this->repository->setDefaultCard($request);
}
```

### Deleting a Card

Customers can delete saved cards:

```php
// From PaymentMethodController.php
public function destroy($id)
{
    return $this->repository->delete($id);
}
```

## Testing

To test Stripe integration:

1. Use Stripe test API keys in your `.env` file
2. Create a test order and proceed to checkout
3. Select Stripe as the payment method
4. Complete the payment using Stripe test card numbers:
   - `4242 4242 4242 4242` for successful payments
   - `4000 0000 0000 0002` for declined payments
   - `4000 0000 0000 9995` for insufficient funds
5. Verify that the order status is updated correctly

### Testing Webhooks Locally

For local development, you can use tools like [Stripe CLI](https://stripe.com/docs/stripe-cli) to forward webhook events to your local server:

1. Install Stripe CLI
2. Run `stripe listen --forward-to http://localhost:8000/webhooks/stripe`
3. Use the webhook signing secret provided by the CLI in your `.env` file

## Troubleshooting

### Common Issues

1. **Payment Intent Creation Fails**:
   - Check your Stripe API key
   - Ensure the currency is supported by Stripe
   - Verify that the amount is valid (not zero or negative)

2. **Webhook Verification Fails**:
   - Ensure the webhook secret in your `.env` file matches the one in the Stripe Dashboard
   - Check that the webhook URL is accessible from the internet
   - Verify that your webhook is configured to receive the correct events

3. **Card Saving Fails**:
   - Check that the customer was created successfully
   - Ensure the payment method ID is valid
   - Verify that the card is not already attached to the customer

### Debugging

To debug Stripe integration issues:

1. Check the Laravel logs for detailed error messages
2. Enable debug mode in your Laravel application
3. Use Stripe's dashboard to inspect payment intents, customers, and webhook events
4. Use Stripe CLI to test webhook events locally

## Examples

### Creating a Stripe Payment Intent

The following example shows how to create a Stripe payment intent:

```php
// In your controller
public function createPayment(Request $request)
{
    $data = [
        'amount' => $request->amount,
        'order_tracking_number' => $request->order_id,
        // If you have a customer ID
        'customer' => $request->customer_id,
    ];
    
    try {
        $stripe = new \Marvel\Payments\Stripe();
        $intent = $stripe->getIntent($data);
        
        // Store the payment intent
        PaymentIntent::create([
            'order_id' => $request->order_id,
            'tracking_number' => $request->order_id,
            'payment_gateway' => 'STRIPE',
            'payment_intent_info' => $intent,
        ]);
        
        // Return the client secret
        return response()->json([
            'client_secret' => $intent['client_secret'],
            'payment_id' => $intent['payment_id'],
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
        ], 400);
    }
}
```

### Saving a Customer's Card

The following example shows how to save a customer's card:

```php
// In your controller
public function saveCard(Request $request)
{
    try {
        // Create a setup intent
        $stripe = new \Marvel\Payments\Stripe();
        $setupIntent = $stripe->setIntent([]);
        
        // Return the client secret to the frontend
        return response()->json([
            'client_secret' => $setupIntent['client_secret'],
            'intent_id' => $setupIntent['intent_id'],
        ]);
        
        // After the frontend confirms the setup intent, save the payment method
        $paymentMethod = $request->payment_method_id;
        $stripe->attachPaymentMethodToCustomer($paymentMethod, $request);
        
        // Save the payment method to the database
        PaymentMethod::create([
            'method_key' => $paymentMethod,
            'payment_gateway_id' => $request->payment_gateway_id,
            'default_card' => $request->default_card,
            'fingerprint' => $request->fingerprint,
            'owner_name' => $request->owner_name,
            'network' => $request->network,
            'type' => $request->type,
            'last4' => $request->last4,
            'expires' => $request->expires,
            'origin' => $request->origin,
            'verification_check' => $request->verification_check,
        ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Card saved successfully',
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
        ], 400);
    }
}
```

### Processing a Stripe Webhook

The webhook processing is handled automatically by the `WebHookController` and the `Stripe` class. The system will:

1. Verify the webhook signature
2. Process the event based on its type
3. Update the order status accordingly
4. Return a 200 response to acknowledge receipt of the webhook

No additional code is required to handle webhooks beyond the existing implementation.
