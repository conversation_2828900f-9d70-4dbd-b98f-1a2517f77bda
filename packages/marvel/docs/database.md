Database Schema Documentation (Based on Migrations)
This document outlines the database schema created and modified by the provided Laravel migration files. It's intended to provide a clear overview of the tables, columns, and relationships, which can be used as a basis for creating an Entity Relationship Diagram (ERD).
Conventions:
id (usually BIGINT UNSIGNED PRIMARY KEY): Standard auto-incrementing primary key.
timestamps(): Adds created_at (TIMESTAMP) and updated_at (TIMESTAMP) columns, both nullable.
softDeletes(): Adds a deleted_at (TIMESTAMP, Nullable) column for soft deletion tracking.
nullable(): Indicates the column can store NULL values.
default(value): Indicates a default value if none is provided.
FK: Foreign Key.

1. Permission System Tables (Spatie Laravel Permission)
(Migration: 2020_04_17_194830_create_permission_tables.php)
permissions
Purpose: Stores individual permission definitions.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
name: VARCHAR
guard_name: VARCHAR
created_at: TIMESTAMP
updated_at: TIMESTAMP
roles
Purpose: Stores role definitions.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
name: <PERSON><PERSON>HAR
guard_name: <PERSON><PERSON><PERSON><PERSON>
created_at: TIMES<PERSON><PERSON>
updated_at: TIMESTAMP
model_has_permissions (Pivot Table - Polymorphic)
Purpose: Assigns direct permissions to models (e.g., a User).
Columns:
permission_id: BIGINT UNSIGNED (FK to permissions.id, Cascade on Delete)
model_type: VARCHAR (e.g., 'App\Models\User')
model_id: BIGINT UNSIGNED (Represents the ID of the model, e.g., users.id)
Primary Key: (permission_id, model_id, model_type)
Index: (model_id, model_type)
model_has_roles (Pivot Table - Polymorphic)
Purpose: Assigns roles to models (e.g., a User).
Columns:
role_id: BIGINT UNSIGNED (FK to roles.id, Cascade on Delete)
model_type: VARCHAR (e.g., 'App\Models\User')
model_id: BIGINT UNSIGNED (Represents the ID of the model, e.g., users.id)
Primary Key: (role_id, model_id, model_type)
Index: (model_id, model_type)
role_has_permissions (Pivot Table)
Purpose: Assigns permissions to roles.
Columns:
permission_id: BIGINT UNSIGNED (FK to permissions.id, Cascade on Delete)
role_id: BIGINT UNSIGNED (FK to roles.id, Cascade on Delete)
Primary Key: (permission_id, role_id)

2. Core E-commerce Tables (Marvel)
(Migration: 2020_06_02_051901_create_marvel_tables.php)
shipping_classes
Purpose: Defines different shipping cost rules.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
name: VARCHAR
amount: DOUBLE
is_global: VARCHAR (Default: 'true') - Note: Stored as string, consider BOOLEAN.
type: ENUM (ShippingType values, Default: 'fixed')
created_at: TIMESTAMP
updated_at: TIMESTAMP
coupons
Purpose: Stores discount coupon details.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
code: VARCHAR
description: TEXT, Nullable
image: JSON, Nullable
type: ENUM (CouponType values, Default: 'DEFAULT_COUPON')
amount: FLOAT (Default: 0)
minimum_cart_amount: FLOAT (Default: 0)
active_from: VARCHAR - Note: Consider DATETIME/TIMESTAMP.
expire_at: VARCHAR - Note: Consider DATETIME/TIMESTAMP.
language: VARCHAR (Default: DEFAULT_LANGUAGE) - Added later
target: BOOLEAN (Default: false) - Added later
is_approve: BOOLEAN (Default: false) - Added later
shop_id: BIGINT UNSIGNED, Nullable (FK to shops.id, Cascade on Delete) - Added later
user_id: BIGINT UNSIGNED, Nullable (FK to users.id, Cascade on Delete) - Added later
created_at: TIMESTAMP
updated_at: TIMESTAMP
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
types
Purpose: Defines types or groups for products/manufacturers (e.g., 'Grocery', 'Clothing').
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
name: VARCHAR
settings: JSON, Nullable - Added later
slug: VARCHAR
icon: VARCHAR, Nullable
promotional_sliders: JSON, Nullable
images: JSON, Nullable
language: VARCHAR (Default: DEFAULT_LANGUAGE) - Added later
created_at: TIMESTAMP
updated_at: TIMESTAMP
authors
Purpose: Stores author information (likely for book products).
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
name: VARCHAR
is_approved: BOOLEAN (Default: false)
image: JSON, Nullable
cover_image: JSON, Nullable
slug: VARCHAR
language: VARCHAR (Default: DEFAULT_LANGUAGE) - Added later
bio: TEXT, Nullable
quote: TEXT, Nullable
born: VARCHAR, Nullable
death: VARCHAR, Nullable
languages: VARCHAR, Nullable
socials: JSON, Nullable
created_at: TIMESTAMP
updated_at: TIMESTAMP
manufacturers
Purpose: Stores manufacturer/brand information.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
name: VARCHAR
is_approved: BOOLEAN (Default: false)
image: JSON, Nullable
cover_image: JSON, Nullable
slug: VARCHAR
language: VARCHAR (Default: DEFAULT_LANGUAGE) - Added later
type_id: BIGINT UNSIGNED (FK to types.id, Cascade on Delete)
description: TEXT, Nullable
website: VARCHAR, Nullable
socials: JSON, Nullable
created_at: TIMESTAMP
updated_at: TIMESTAMP
products
Purpose: Core table for product information.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
name: VARCHAR
slug: VARCHAR
description: TEXT, Nullable
type_id: BIGINT UNSIGNED (FK to types.id, Cascade on Delete)
price: DOUBLE, Nullable
shop_id: BIGINT UNSIGNED, Nullable (FK to shops.id, Cascade on Delete) - Added later
sale_price: DOUBLE, Nullable
language: VARCHAR (Default: DEFAULT_LANGUAGE) - Added later
min_price: FLOAT, Nullable - Added later
max_price: FLOAT, Nullable - Added later
sku: VARCHAR, Nullable
quantity: INTEGER (Default: 0)
sold_quantity: INTEGER (Default: 0) - Added later
in_stock: BOOLEAN (Default: true)
is_taxable: BOOLEAN (Default: false)
in_flash_sale: INTEGER (Default: false) - Added later, consider BOOLEAN
shipping_class_id: BIGINT UNSIGNED, Nullable (FK to shipping_classes.id)
status: ENUM (ProductStatus values, Default: 'DRAFT')
visibility: ENUM (ProductVisibilityStatus values, Default: 'public') - Added later
product_type: ENUM (ProductType values, Default: 'SIMPLE')
unit: VARCHAR
height: VARCHAR, Nullable
width: VARCHAR, Nullable
length: VARCHAR, Nullable
image: JSON, Nullable
video: JSON, Nullable - Added later
gallery: JSON, Nullable
author_id: BIGINT UNSIGNED, Nullable (FK to authors.id, Cascade on Delete) - Added later
manufacturer_id: BIGINT UNSIGNED, Nullable (FK to manufacturers.id, Cascade on Delete) - Added later
is_digital: BOOLEAN (Default: 0) - Added later
is_external: BOOLEAN (Default: 0) - Added later
external_product_url: VARCHAR, Nullable - Added later
external_product_button_text: VARCHAR, Nullable - Added later
blocked_dates: VARCHAR, Nullable - Added later
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
orders
Purpose: Stores customer order information.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
tracking_number: VARCHAR, Unique
customer_id: BIGINT UNSIGNED, Nullable (FK to users.id)
customer_contact: VARCHAR
customer_name: VARCHAR, Nullable
shop_id: BIGINT UNSIGNED, Nullable (FK to shops.id, Cascade on Delete) - Added later
parent_id: BIGINT UNSIGNED, Nullable (FK to orders.id, Cascade on Delete) - Added later
coupon_id: BIGINT UNSIGNED, Nullable
amount: DOUBLE
sales_tax: DOUBLE, Nullable
paid_total: DOUBLE, Nullable
total: DOUBLE, Nullable
note: LONGTEXT, Nullable - Added later
cancelled_amount: DECIMAL (Default: 0) - Added later
cancelled_tax: DECIMAL (Default: 0) - Added later
cancelled_delivery_fee: DECIMAL (Default: 0) - Added later
language: VARCHAR (Default: DEFAULT_LANGUAGE) - Added later
discount: DOUBLE, Nullable
payment_gateway: VARCHAR, Nullable
altered_payment_gateway: VARCHAR, Nullable
shipping_address: JSON, Nullable
billing_address: JSON, Nullable
logistics_provider: BIGINT UNSIGNED, Nullable
delivery_fee: DOUBLE, Nullable
delivery_time: VARCHAR, Nullable
order_status: ENUM (OrderStatus values, Default: 'pending')
payment_status: ENUM (PaymentStatus values, Default: 'pending')
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
order_product (Pivot Table)
Purpose: Links products to orders, storing quantity and price at the time of order.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
order_id: BIGINT UNSIGNED (FK to orders.id, Cascade on Delete)
product_id: BIGINT UNSIGNED (FK to products.id, Cascade on Delete)
variation_option_id: BIGINT UNSIGNED, Nullable (FK to variation_options.id) - Added later
order_quantity: VARCHAR - Note: Consider INTEGER.
unit_price: DOUBLE
subtotal: DOUBLE
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
categories
Purpose: Stores product categories, potentially hierarchical.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
name: VARCHAR
slug: VARCHAR
language: VARCHAR (Default: DEFAULT_LANGUAGE) - Added later
icon: VARCHAR, Nullable
image: JSON, Nullable
banner_image: JSON, Nullable
details: TEXT, Nullable
parent: BIGINT UNSIGNED, Nullable (FK to categories.id, Cascade on Delete)
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
category_product (Pivot Table)
Purpose: Links products to categories (Many-to-Many).
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
product_id: BIGINT UNSIGNED (FK to products.id, Cascade on Delete)
category_id: BIGINT UNSIGNED (FK to categories.id, Cascade on Delete)
attributes
Purpose: Defines product attributes (e.g., 'Color', 'Size').
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
slug: VARCHAR
name: VARCHAR
shop_id: BIGINT UNSIGNED, Nullable (FK to shops.id, Cascade on Delete) - Added later
language: VARCHAR (Default: DEFAULT_LANGUAGE) - Added later
created_at: TIMESTAMP
updated_at: TIMESTAMP
attribute_values
Purpose: Stores the possible values for attributes (e.g., 'Red', 'XL').
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
slug: VARCHAR
attribute_id: BIGINT UNSIGNED (FK to attributes.id, Cascade on Delete)
value: VARCHAR
meta: VARCHAR, Nullable - Added later
language: VARCHAR (Default: DEFAULT_LANGUAGE) - Added later
created_at: TIMESTAMP
updated_at: TIMESTAMP
attribute_product (Pivot Table)
Purpose: Links attribute values to products (Many-to-Many). Used for filterable attributes on simple products.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
attribute_value_id: BIGINT UNSIGNED (FK to attribute_values.id, Cascade on Delete)
product_id: BIGINT UNSIGNED (FK to products.id, Cascade on Delete)
created_at: TIMESTAMP
updated_at: TIMESTAMP
tax_classes
Purpose: Defines tax rates based on location or other criteria.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
country: VARCHAR, Nullable
state: VARCHAR, Nullable
zip: VARCHAR, Nullable
city: VARCHAR, Nullable
rate: DOUBLE
name: VARCHAR, Nullable
is_global: INTEGER, Nullable - Note: Consider BOOLEAN.
priority: INTEGER, Nullable
on_shipping: BOOLEAN (Default: 1)
created_at: TIMESTAMP
updated_at: TIMESTAMP
address
Purpose: Stores customer addresses (shipping/billing).
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
title: VARCHAR
type: VARCHAR (e.g., 'billing', 'shipping')
default: BOOLEAN (Default: false)
address: JSON (Structured address details)
location: JSON, Nullable (Coordinates)
customer_id: BIGINT UNSIGNED (FK to users.id)
created_at: TIMESTAMP
updated_at: TIMESTAMP
settings
Purpose: Stores application-wide settings, likely JSON blobs.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
options: JSON
language: VARCHAR, Unique (Default: DEFAULT_LANGUAGE) - Added later
created_at: TIMESTAMP
updated_at: TIMESTAMP
user_profiles
Purpose: Stores additional profile information for users.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
avatar: JSON, Nullable
bio: TEXT, Nullable
socials: JSON, Nullable
contact: VARCHAR, Nullable
notifications: JSON, Nullable - Added later
customer_id: BIGINT UNSIGNED (FK to users.id)
created_at: TIMESTAMP
updated_at: TIMESTAMP
users (Standard Laravel User Table - Modified)
Purpose: Stores user authentication and basic info.
Columns (Relevant additions/changes):
id: BIGINT UNSIGNED PRIMARY KEY (Assumed from standard Laravel)
name: VARCHAR (Assumed)
email: VARCHAR, Unique (Assumed)
password: VARCHAR, Nullable (Changed later)
is_active: BOOLEAN (Default: 1)
shop_id: BIGINT UNSIGNED, Nullable (FK to shops.id, Cascade on Delete) - Added later
remember_token: VARCHAR(100), Nullable (Assumed)
created_at: TIMESTAMP (Assumed)
updated_at: TIMESTAMP (Assumed)
attachments
Purpose: Stores information about uploaded files (generic).
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
url: VARCHAR (Default: '')
created_at: TIMESTAMP
updated_at: TIMESTAMP

3. Media Library Tables (Spatie Media Library)
(Migration: 2020_10_26_163529_create_media_table.php)
media (Polymorphic)
Purpose: Stores information about files attached to models (e.g., product images, avatars).
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
model_type: VARCHAR (e.g., 'App\Models\Product')
model_id: BIGINT UNSIGNED (ID of the related model)
uuid: UUID, Nullable
collection_name: VARCHAR (Group name for media, e.g., 'images', 'downloads')
name: VARCHAR (Media name)
file_name: VARCHAR (Original file name)
mime_type: VARCHAR, Nullable
disk: VARCHAR (Storage disk name)
conversions_disk: VARCHAR, Nullable (Disk for derived files)
size: BIGINT UNSIGNED (File size in bytes)
manipulations: JSON (Applied image transformations)
generated_conversions: JSON (Info about created image versions)
custom_properties: JSON (User-defined metadata)
responsive_images: JSON (Data for responsive image generation)
order_column: INTEGER UNSIGNED, Nullable (For ordering media within a collection)
created_at: TIMESTAMP, Nullable
updated_at: TIMESTAMP, Nullable

4. Multi-Vendor & Advanced Product Features
(Migration: 2021_04_17_051901_create_new_marvel_tables.php)
variation_options
Purpose: Stores specific variations of a variable product (e.g., 'Red - XL' shirt).
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
title: VARCHAR
image: JSON, Nullable - Added later
price: VARCHAR - Note: Consider DOUBLE/DECIMAL.
sale_price: VARCHAR, Nullable - Note: Consider DOUBLE/DECIMAL.
language: VARCHAR (Default: DEFAULT_LANGUAGE) - Added later
quantity: BIGINT UNSIGNED
sold_quantity: INTEGER (Default: 0) - Added later
is_disable: BOOLEAN (Default: false)
sku: VARCHAR, Nullable
options: JSON (Describes the specific attribute values for this variation, e.g., [{ "name": "Color", "value": "Red" }, { "name": "Size", "value": "XL" }])
product_id: BIGINT UNSIGNED, Nullable (FK to products.id, Cascade on Delete)
digital_file_tracker: VARCHAR, Nullable - Added later
is_digital: BOOLEAN (Default: 0) - Added later
created_at: TIMESTAMP
updated_at: TIMESTAMP
tags
Purpose: Stores product tags.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
name: VARCHAR
slug: VARCHAR
language: VARCHAR (Default: DEFAULT_LANGUAGE) - Added later
icon: VARCHAR, Nullable
image: JSON, Nullable
details: TEXT, Nullable
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
product_tag (Pivot Table)
Purpose: Links products to tags (Many-to-Many).
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
product_id: BIGINT UNSIGNED (FK to products.id, Cascade on Delete)
tag_id: BIGINT UNSIGNED (FK to tags.id, Cascade on Delete)
shops
Purpose: Stores information about individual vendor shops.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
owner_id: BIGINT UNSIGNED (FK to users.id)
name: VARCHAR, Nullable
slug: VARCHAR, Nullable
description: TEXT, Nullable
cover_image: JSON, Nullable
logo: JSON, Nullable
is_active: BOOLEAN (Default: false)
address: JSON, Nullable
settings: JSON, Nullable
notifications: JSON, Nullable - Added later
created_at: TIMESTAMP
updated_at: TIMESTAMP
balances
Purpose: Tracks earnings and withdrawal information for shops.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
shop_id: BIGINT UNSIGNED (FK to shops.id, Cascade on Delete)
admin_commission_rate: DOUBLE, Nullable
total_earnings: DOUBLE (Default: 0)
withdrawn_amount: DOUBLE (Default: 0)
current_balance: DOUBLE (Default: 0)
is_custom_commission: BOOLEAN (Default: false) - Added later
payment_info: JSON, Nullable
created_at: TIMESTAMP
updated_at: TIMESTAMP
user_shop (Pivot Table)
Purpose: Links users to shops (Many-to-Many, likely for staff/managers).
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete)
shop_id: BIGINT UNSIGNED (FK to shops.id, Cascade on Delete)
category_shop (Pivot Table)
Purpose: Links categories to shops (Many-to-Many, possibly restricting categories per shop).
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
shop_id: BIGINT UNSIGNED (FK to shops.id, Cascade on Delete)
category_id: BIGINT UNSIGNED (FK to categories.id, Cascade on Delete)
withdraws
Purpose: Stores withdrawal requests made by shops.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
shop_id: BIGINT UNSIGNED (FK to shops.id, Cascade on Delete)
amount: FLOAT
payment_method: VARCHAR, Nullable
status: ENUM (WithdrawStatus values, Default: 'PENDING')
details: TEXT, Nullable
note: TEXT, Nullable
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
providers
Purpose: Stores social login provider information for users.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete)
provider_user_id: VARCHAR (User ID from the social provider)
provider: VARCHAR (Name of the provider, e.g., 'google', 'facebook')
created_at: TIMESTAMP
updated_at: TIMESTAMP
banners
Purpose: Stores promotional banners, likely associated with product types.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
type_id: BIGINT UNSIGNED (FK to types.id, Cascade on Delete)
title: TEXT
description: TEXT, Nullable
image: JSON, Nullable
created_at: TIMESTAMP
updated_at: TIMESTAMP
payment_gateways
Purpose: Stores customer references for specific payment gateways (e.g., Stripe customer ID).
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete)
customer_id: VARCHAR (Customer ID from the gateway)
gateway_name: VARCHAR, Nullable
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
payment_methods
Purpose: Stores saved payment methods (e.g., credit card details).
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
method_key: VARCHAR, Unique (Identifier for the payment method)
payment_gateway_id: BIGINT UNSIGNED, Nullable (FK to payment_gateways.id, Cascade on Delete)
default_card: BOOLEAN, Nullable (Default: false)
fingerprint: VARCHAR, Unique
owner_name: VARCHAR, Nullable
network: VARCHAR, Nullable (e.g., 'Visa', 'Mastercard')
type: VARCHAR, Nullable (e.g., 'card')
last4: VARCHAR, Nullable
expires: VARCHAR, Nullable (e.g., 'MM/YY')
origin: VARCHAR, Nullable
verification_check: VARCHAR, Nullable
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
payment_intents
Purpose: Stores information about payment attempts/intents.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
order_id: BIGINT UNSIGNED, Nullable (FK to orders.id, Cascade on Delete)
tracking_number: VARCHAR, Nullable
payment_gateway: VARCHAR, Nullable
payment_intent_info: JSON, Nullable (Details from the payment gateway)
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP

5. Wallet, Refunds & Points System
(Migration: 2021_08_08_051901_create_wallet_table.php)
wallets
Purpose: Manages customer points/store credit.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
total_points: DOUBLE (Default: 0)
points_used: DOUBLE (Default: 0)
available_points: DOUBLE (Default: 0)
customer_id: BIGINT UNSIGNED, Nullable (FK to users.id, Cascade on Delete)
created_at: TIMESTAMP
updated_at: TIMESTAMP
order_wallet_points
Purpose: Records points earned or used for a specific order.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
amount: DOUBLE, Nullable (Points value)
order_id: BIGINT UNSIGNED, Nullable (FK to orders.id, Cascade on Delete)
created_at: TIMESTAMP
updated_at: TIMESTAMP
refunds
Purpose: Stores refund requests and their status.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
amount: DOUBLE (Default: 0)
status: ENUM (RefundStatus values, Default: 'PENDING')
title: VARCHAR, Nullable
description: TEXT, Nullable
images: JSON, Nullable
order_id: BIGINT UNSIGNED, Nullable (FK to orders.id, Cascade on Delete)
customer_id: BIGINT UNSIGNED, Nullable (FK to users.id, Cascade on Delete)
refund_policy_id: BIGINT UNSIGNED, Nullable (FK to refund_policies.id, Set Null on Delete) - Added later
shop_id: BIGINT UNSIGNED, Nullable (FK to shops.id, Cascade on Delete)
refund_reason_id: BIGINT UNSIGNED, Nullable (FK to refund_reasons.id, Set Null on Delete) - Added later
created_at: TIMESTAMP
updated_at: TIMESTAMP

6. Digital Products & Downloads
(Migration: 2021_09_26_051901_create_product_type_table.php)
digital_files (Polymorphic)
Purpose: Stores details about downloadable files associated with products or variations.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
attachment_id: BIGINT UNSIGNED (Likely FK to attachments.id or media.id, needs clarification)
url: VARCHAR (Download URL or path)
file_name: VARCHAR
fileable_type: VARCHAR (e.g., 'App\Models\Product', 'App\Models\VariationOption')
fileable_id: BIGINT UNSIGNED (ID of the related product/variation)
created_at: TIMESTAMP
updated_at: TIMESTAMP
ordered_files
Purpose: Tracks which customers have purchased access to which digital files via which order.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
purchase_key: VARCHAR (Unique key granting download access)
digital_file_id: BIGINT UNSIGNED (FK to digital_files.id, Cascade on Delete)
tracking_number: VARCHAR, Nullable (FK to orders.tracking_number, Cascade on Delete)
customer_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete)
created_at: TIMESTAMP
updated_at: TIMESTAMP
download_tokens
Purpose: Generates temporary, secure tokens for downloading files.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
token: VARCHAR (The unique download token)
digital_file_id: BIGINT UNSIGNED, Nullable (FK to digital_files.id, Cascade on Delete)
payload: TEXT, Nullable (Additional data associated with the token)
user_id: BIGINT UNSIGNED (User who generated/owns the token)
created_at: TIMESTAMP
updated_at: TIMESTAMP

7. Reviews, Questions, Wishlists & Feedback
(Migration: 2021_10_12_193855_create_reviews_table.php)
reviews
Purpose: Stores customer reviews for products.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
order_id: BIGINT UNSIGNED, Nullable (FK to orders.id, Cascade on Delete) - Added later
user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete)
shop_id: BIGINT UNSIGNED (FK to shops.id, Cascade on Delete)
product_id: BIGINT UNSIGNED (FK to products.id, Cascade on Delete)
variation_option_id: BIGINT UNSIGNED, Nullable (FK to variation_options.id, Cascade on Delete) - Added later
comment: LONGTEXT
rating: DOUBLE, Nullable
photos: JSON, Nullable
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
questions
Purpose: Stores customer questions about products and vendor answers.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete)
shop_id: BIGINT UNSIGNED (FK to shops.id, Cascade on Delete)
product_id: BIGINT UNSIGNED (FK to products.id, Cascade on Delete)
question: TEXT
answer: TEXT, Nullable
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
feedbacks (Polymorphic)
Purpose: Stores positive/negative feedback on various models (e.g., reviews, questions).
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete)
model_type: VARCHAR
model_id: BIGINT UNSIGNED
positive: BOOLEAN, Nullable
negative: BOOLEAN, Nullable
created_at: TIMESTAMP
updated_at: TIMESTAMP
abusive_reports (Polymorphic)
Purpose: Stores reports of abusive content related to various models.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete)
model_type: VARCHAR
model_id: BIGINT UNSIGNED
message: TEXT
created_at: TIMESTAMP
updated_at: TIMESTAMP
wishlists
Purpose: Stores products added to users' wishlists.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete)
product_id: BIGINT UNSIGNED (FK to products.id, Cascade on Delete)
variation_option_id: BIGINT UNSIGNED, Nullable (FK to variation_options.id, Cascade on Delete) - Added later
created_at: TIMESTAMP
updated_at: TIMESTAMP

8. Rental & Booking Features
(Migration: 2022_01_19_051901_create_rental_tables.php)
products_meta
Purpose: Stores additional, flexible key-value metadata for products, likely used for rental specifics.
Columns:
id: INTEGER UNSIGNED PRIMARY KEY
product_id: BIGINT UNSIGNED (FK to products.id, Cascade on Delete)
type: VARCHAR (Default: 'null')
key: VARCHAR, Index
value: TEXT, Nullable
created_at: TIMESTAMP
updated_at: TIMESTAMP
availabilities (Polymorphic Booking)
Purpose: Tracks booked time slots or quantities for bookable items (products/variations).
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
from: VARCHAR - Note: Consider DATETIME/TIMESTAMP.
to: VARCHAR - Note: Consider DATETIME/TIMESTAMP.
language: VARCHAR (Default: DEFAULT_LANGUAGE) - Added later
booking_duration: VARCHAR
order_quantity: INTEGER
bookable_type: VARCHAR (e.g., 'App\Models\Product')
bookable_id: BIGINT UNSIGNED (ID of the product/variation being booked)
order_id: BIGINT UNSIGNED, Nullable (FK to orders.id, Cascade on Delete)
product_id: BIGINT UNSIGNED, Nullable (FK to products.id, Cascade on Delete) - Note: Redundant if bookable_id/type covers products?
created_at: TIMESTAMP
updated_at: TIMESTAMP
resources
Purpose: Defines reusable resources, possibly for rentals (e.g., 'Helmet', 'GPS', 'Pickup Location').
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
name: VARCHAR
slug: VARCHAR
language: VARCHAR (Default: DEFAULT_LANGUAGE) - Added later
icon: VARCHAR, Nullable
details: TEXT, Nullable
image: JSON, Nullable
is_approved: BOOLEAN (Default: false)
price: DOUBLE, Nullable
type: ENUM (ResourceType values)
created_at: TIMESTAMP
updated_at: TIMESTAMP
dropoff_location_product (Pivot Table)
Purpose: Links 'Dropoff Location' type resources to products.
Columns:
resource_id: BIGINT UNSIGNED, Nullable (FK to resources.id, Cascade on Delete)
product_id: BIGINT UNSIGNED, Nullable (FK to products.id, Cascade on Delete)
pickup_location_product (Pivot Table)
Purpose: Links 'Pickup Location' type resources to products.
Columns:
resource_id: BIGINT UNSIGNED, Nullable (FK to resources.id, Cascade on Delete)
product_id: BIGINT UNSIGNED, Nullable (FK to products.id, Cascade on Delete)
feature_product (Pivot Table)
Purpose: Links 'Feature' type resources to products.
Columns:
resource_id: BIGINT UNSIGNED, Nullable (FK to resources.id, Cascade on Delete)
product_id: BIGINT UNSIGNED, Nullable (FK to products.id, Cascade on Delete)
deposit_product (Pivot Table)
Purpose: Links 'Deposit' type resources to products.
Columns:
resource_id: BIGINT UNSIGNED, Nullable (FK to resources.id, Cascade on Delete)
product_id: BIGINT UNSIGNED, Nullable (FK to products.id, Cascade on Delete)
person_product (Pivot Table)
Purpose: Links 'Person' type resources (e.g., capacity) to products.
Columns:
resource_id: BIGINT UNSIGNED, Nullable (FK to resources.id, Cascade on Delete)
product_id: BIGINT UNSIGNED, Nullable (FK to products.id, Cascade on Delete)

9. Language & Translation Features
(Migration: 2022_01_31_051901_create_marvel_languages_tables.php)
languages
Purpose: Stores available languages for translation.
Columns:
id: INTEGER UNSIGNED PRIMARY KEY
flag: JSON (Image/icon for the language flag)
language_code: VARCHAR (e.g., 'en', 'es')
language_name: VARCHAR (e.g., 'English', 'Spanish')
created_at: TIMESTAMP
updated_at: TIMESTAMP
translations
Purpose: Links translated content items to their original source items.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
item_type: VARCHAR (Model class name, e.g., 'App\Models\Product')
item_id: BIGINT UNSIGNED (ID of the translated item)
translation_item_id: BIGINT UNSIGNED, Nullable (ID of the original source item)
language_code: VARCHAR (Language of the item_id record)
source_language_code: VARCHAR (Language of the translation_item_id record, Default: DEFAULT_LANGUAGE)
created_at: TIMESTAMP
updated_at: TIMESTAMP
(Note: This migration also adds a language column with a default value to many existing tables like products, categories, tags, authors, manufacturers, resources, types, attributes, attribute_values, availabilities, coupons, orders, variation_options, settings.)

10. Delivery Time Options
(Migration: 2022_03_23_051901_create_marvel_delivery_time_tables.php)
delivery_times
Purpose: Defines predefined delivery time slots/options.
Columns:
id: INTEGER UNSIGNED PRIMARY KEY
title: VARCHAR
slug: VARCHAR
icon: VARCHAR
description: TEXT, Nullable
language: VARCHAR (Default: DEFAULT_LANGUAGE)
created_at: TIMESTAMP
updated_at: TIMESTAMP

11. Store Notice System
(Migration: 2022_03_23_051902_create_marvel_store_notice_tables.php)
store_notices
Purpose: Stores admin/vendor announcements or notices.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
priority: ENUM (StoreNoticePriority values, Default: 'low')
notice: TEXT
description: TEXT, Nullable
effective_from: DATETIME (Default: now())
expired_at: DATETIME
type: ENUM (StoreNoticeType values)
created_by: BIGINT UNSIGNED, Nullable (FK to users.id)
updated_by: BIGINT UNSIGNED, Nullable (FK to users.id)
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
store_notice_user (Pivot Table)
Purpose: Links notices specifically to users.
Columns:
store_notice_id: BIGINT UNSIGNED, Nullable (FK to store_notices.id, Cascade on Delete)
user_id: BIGINT UNSIGNED, Nullable (FK to users.id, Cascade on Delete)
store_notice_shop (Pivot Table)
Purpose: Links notices specifically to shops.
Columns:
store_notice_id: BIGINT UNSIGNED, Nullable (FK to store_notices.id, Cascade on Delete)
shop_id: BIGINT UNSIGNED, Nullable (FK to shops.id, Cascade on Delete)
store_notice_read (Pivot Table)
Purpose: Tracks which users have read which notices.
Columns:
store_notice_id: BIGINT UNSIGNED, Nullable (FK to store_notices.id, Cascade on Delete)
user_id: BIGINT UNSIGNED, Nullable (FK to users.id, Cascade on Delete)
is_read: BOOLEAN (Default: false)

12. Messaging System
(Migration: 2022_05_09_070829_create_messages_table.php)
conversations
Purpose: Represents a message thread between a user and a shop.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete)
shop_id: BIGINT UNSIGNED (FK to shops.id, Cascade on Delete)
created_at: TIMESTAMP
updated_at: TIMESTAMP
messages
Purpose: Stores individual messages within a conversation.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
conversation_id: BIGINT UNSIGNED (FK to conversations.id, Cascade on Delete)
user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete - Sender)
body: TEXT (Message content)
created_at: TIMESTAMP
updated_at: TIMESTAMP
participants
Purpose: Tracks participants in a conversation and their read status. Seems slightly complex/redundant given conversations table structure, might be for tracking last read message.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
conversation_id: BIGINT UNSIGNED (FK to conversations.id, Cascade on Delete)
type: ENUM ('shop', 'user')
user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete)
shop_id: BIGINT UNSIGNED (FK to shops.id, Cascade on Delete)
message_id: BIGINT UNSIGNED (FK to messages.id, Cascade on Delete - Likely the last read message)
notify: BOOLEAN (Default: 0)
last_read: TIMESTAMP, Nullable
created_at: TIMESTAMP
updated_at: TIMESTAMP
(Note: This migration also adds a notifications JSON column to user_profiles and shops.)

13. Notification Log
(Migration: 2023_07_12_030502_create_notify_logs_table.php)
notify_logs
Purpose: Stores a log of notifications sent within the system.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
receiver: BIGINT UNSIGNED (FK to users.id, Cascade on Delete)
sender: BIGINT UNSIGNED, Nullable (FK to users.id, Cascade on Delete)
notify_type: TEXT, Nullable (Category of notification, e.g., 'order_update')
notify_receiver_type: TEXT, Nullable (e.g., 'customer', 'vendor')
is_read: BOOLEAN (Default: false)
notify_tracker: TEXT, Nullable (Identifier for the related entity, e.g., order ID)
notify_text: TEXT, Nullable (The notification message content)
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP

14. FAQs
(Migration: 2023_07_19_162433_create_faqs_table.php)
faqs
Purpose: Stores Frequently Asked Questions, potentially global or shop-specific.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete - Author)
shop_id: BIGINT UNSIGNED, Nullable (FK to shops.id, Cascade on Delete - If shop-specific)
faq_title: TEXT
slug: VARCHAR
faq_description: TEXT
faq_type: VARCHAR, Nullable (Category/Type of FAQ)
issued_by: VARCHAR, Nullable (e.g., 'admin', 'vendor')
language: VARCHAR, Nullable
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP

15. Terms and Conditions
(Migration: 2023_07_25_053633_create_terms_and_conditions_table.php)
terms_and_conditions
Purpose: Stores Terms and Conditions documents, potentially global or shop-specific.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
user_id: BIGINT UNSIGNED (FK to users.id, Cascade on Delete - Author)
shop_id: BIGINT UNSIGNED, Nullable (FK to shops.id, Cascade on Delete - If shop-specific)
title: TEXT
slug: VARCHAR
description: TEXT
type: VARCHAR, Nullable (e.g., 'global', 'vendor')
issued_by: VARCHAR, Nullable (e.g., 'admin', 'vendor')
is_approved: BOOLEAN (Default: false)
language: VARCHAR, Nullable
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP

16. Flash Sales
(Migration: 2023_08_14_173253_create_flash_sales_table.php)
flash_sales
Purpose: Defines flash sale events.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
title: VARCHAR
slug: VARCHAR
description: TEXT, Nullable
start_date: DATETIME (Default: now())
end_date: DATETIME
sale_status: BOOLEAN (Default: false)
type: ENUM (FlashSaleType values, Default: 'DEFAULT')
rate: INTEGER, Nullable (Percentage discount, maybe?)
sale_builder: JSON, Nullable (Configuration for the sale)
image: JSON, Nullable
cover_image: JSON, Nullable
language: VARCHAR, Nullable
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
flash_sale_requests
Purpose: Stores vendor requests to include products in a flash sale.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
title: VARCHAR
flash_sale_id: BIGINT UNSIGNED (FK to flash_sales.id, Cascade on Delete) - Added later
request_status: BOOLEAN (Default: false)
note: VARCHAR, Nullable - Added later
language: VARCHAR (Default: DEFAULT_LANGUAGE) - Added later
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
flash_sale_products (Pivot Table)
Purpose: Links approved products directly to a flash sale event.
Columns:
flash_sale_id: BIGINT UNSIGNED (FK to flash_sales.id, Cascade on Delete)
product_id: BIGINT UNSIGNED (FK to products.id, Cascade on Delete)
flash_sale_requests_products (Pivot Table)
Purpose: Links products included in a vendor's flash sale request.
Columns:
flash_sale_requests_id: BIGINT UNSIGNED, Nullable (FK to flash_sale_requests.id, Cascade on Delete)
product_id: BIGINT UNSIGNED, Nullable (FK to products.id, Cascade on Delete)

17. Refund Policies & Reasons
(Migrations: 2023_08_28_114418_create_refund_policies_table.php, 2023_09_07_061715_create_refund_reasons_table.php)
refund_policies
Purpose: Defines specific refund policies (global or vendor).
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
title: VARCHAR
slug: VARCHAR, Unique
description: TEXT, Nullable
target: ENUM (RefundPolicyTarget values, Default: 'VENDOR')
language: VARCHAR (Default: DEFAULT_LANGUAGE)
status: ENUM (RefundPolicyStatus values, Default: 'PENDING')
shop_id: BIGINT UNSIGNED, Nullable (FK to shops.id, Set Null on Delete)
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
refund_reasons
Purpose: Stores predefined reasons for initiating a refund.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
name: VARCHAR
slug: VARCHAR
language: VARCHAR (Default: DEFAULT_LANGUAGE)
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
(Note: These migrations also add refund_policy_id and refund_reason_id foreign keys to the refunds table.)

18. Become Seller Page Content
(Migration: 2023_12_12_162216_create_became_sellers_table.php)
became_sellers
Purpose: Stores content/options specifically for the "Become a Seller" page.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
page_options: JSON (Content/settings for the page)
language: VARCHAR, Unique (Default: DEFAULT_LANGUAGE)
created_at: TIMESTAMP
updated_at: TIMESTAMP

19. Shop Ownership Transfer
(Migration: 2024_01_02_063637_create_transfer_history_table.php)
ownership_transfers
Purpose: Logs requests and status of transferring shop ownership between users.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
transaction_identifier: VARCHAR(50)
from: BIGINT UNSIGNED (FK to users.id, Cascade on Delete - Current Owner)
shop_id: BIGINT UNSIGNED (FK to shops.id, Cascade on Delete)
to: BIGINT UNSIGNED (FK to users.id, Cascade on Delete - New Owner)
message: TEXT, Nullable
created_by: BIGINT UNSIGNED (FK to users.id, Cascade on Delete - User initiating transfer)
status: ENUM (DefaultStatusType values, Default: 'PENDING')
deleted_at: TIMESTAMP, Nullable (Soft Deletes)
created_at: TIMESTAMP
updated_at: TIMESTAMP
Index: (id, transaction_identifier, created_at)
(Note: This migration also adds a visibility ENUM column to the products table.)

20. Commission Tiers
(Migration: 2024_02_07_162216_create_commissions_table.php)
commissions
Purpose: Defines commission levels/tiers, possibly based on balance or performance.
Columns:
id: BIGINT UNSIGNED PRIMARY KEY
level: VARCHAR
sub_level: VARCHAR
description: TEXT
min_balance: INTEGER
max_balance: VARCHAR - Note: Consider INTEGER/DOUBLE.
commission: FLOAT (Commission rate)
image: JSON, Nullable
language: VARCHAR (Default: DEFAULT_LANGUAGE)
created_at: TIMESTAMP
updated_at: TIMESTAMP
(Note: This migration also adds an is_custom_commission BOOLEAN column to the balances table.)

This documentation provides a detailed breakdown of the tables and their columns based on the migrations. It highlights relationships (FKs), data types, and constraints, forming a solid foundation for creating an ERD. Remember that polymorphic relationships (model_has_permissions, media, etc.) require special representation in an ERD (often using notation to indicate the model_type and model_id link to multiple other tables).

