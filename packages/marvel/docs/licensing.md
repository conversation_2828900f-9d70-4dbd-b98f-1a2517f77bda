# Marvel Licensing System

## Overview
Marvel implements a license key verification system to validate legitimate usage of the application. The system verifies license keys against RedQ's verification service.

## File Structure

### Core Files
```
packages/marvel/
├── src/
│   ├── Console/
│   │   ├── InstallCommand.php      # Installation command with license verification
│   │   └── SettingsDataSeed.php    # Settings seeder with license checks
│   ├── Http/
│   │   ├── Controllers/
│   │   │   └── UserController.php  # Handles license verification endpoints
│   │   └── Requests/
│   │       └── LicenseRequest.php  # License request validation
│   ├── Exceptions/
│   │   └── MarvelNotFoundException.php # License-related exceptions
│   └── Verification/
│       └── MarvelVerification.php  # Core verification logic
├── license.md                      # License terms
└── config/
    └── marvel.php                  # License configuration
```

## License Verification Flow

### Installation Process
1. During installation (`InstallCommand.php`):
   - Prompts for license key
   - Validates key against RedQ's service
   - Stores verification data if valid

### Runtime Verification
1. API endpoint (`UserController.php`):
   - Accepts license key via POST request
   - Verifies key using `MarvelVerification`
   - Returns verification status and settings data

### Settings Seeding
1. During settings setup (`SettingsDataSeed.php`):
   - Checks for existing license
   - Prompts for key if not found
   - Validates and stores verification data

## API Endpoints

### Verify License
```
POST /api/license/verify
```
Parameters:
- `license_key` (required): Valid license key from RedQ
- `language` (optional): Preferred language for response

Response:
```json
{
    "success": true,
    "message": "License verified successfully",
    "data": {
        // Settings data
    }
}
```

## License Key Validation

### Validation Rules
1. Must be a valid key from RedQ
2. Key must not be expired
3. Key must match the domain/environment
4. Key must not exceed usage limits

### Error Handling
- Invalid key format: Returns 422 validation error
- Invalid license: Throws `MarvelNotFoundException`
- Network issues: Throws `MarvelException`

## Implementation Example

### Verifying License Key
```php
use Marvel\Verification\MarvelVerification;

$verification = new MarvelVerification();
$result = $verification->verify($licenseKey);

if (!$result->getTrust()) {
    throw new MarvelNotFoundException(INVALID_LICENSE_KEY);
}
```

### Frontend Integration
```javascript
const verifyLicense = async (licenseKey) => {
    try {
        const response = await axios.post('/api/license/verify', {
            license_key: licenseKey
        });
        return response.data;
    } catch (error) {
        console.error('License verification failed:', error);
        throw error;
    }
};
```

## Configuration

### Environment Variables
```env
APP_NOTICE_DOMAIN=your_domain
LICENSE_KEY=your_license_key
```

### Marvel Config
Location: `config/marvel.php`
```php
return [
    'license' => [
        'key' => env('LICENSE_KEY'),
        'domain' => env('APP_NOTICE_DOMAIN'),
    ]
];
```

## Security Considerations

### Key Storage
- License keys should be stored securely
- Use environment variables in production
- Avoid committing keys to version control

### Verification Process
- All verification requests are encrypted
- Uses secure HTTPS connections
- Implements rate limiting on verification endpoints

## Troubleshooting

### Common Issues
1. Invalid License Key
   - Verify key format
   - Check domain matching
   - Ensure key is active on RedQ

2. Verification Failed
   - Check network connectivity
   - Verify SSL/TLS settings
   - Validate domain configuration

3. Settings Not Updated
   - Check database permissions
   - Verify settings table exists
   - Ensure proper migration run

## Best Practices
1. Always verify license during installation
2. Implement periodic verification checks
3. Handle verification failures gracefully
4. Keep license keys secure
5. Monitor verification attempts

## License Removal/Disable Guide

## Overview
The Marvel licensing system can be disabled or removed through several methods. Choose the appropriate method based on your needs and usage rights.

## Method 1: Environment-Based Disable

### Modify InstallCommand.php
The licensing check is controlled by the `shouldGetLicenseKey()` method in `packages/marvel/src/Console/InstallCommand.php`. You can modify this to bypass checks:

```php
private function shouldGetLicenseKey()
{
    return false; // Always skip license check
}
```

This method is useful for development or testing environments where you don't need to verify licenses.

## Method 2: Config-Based Disable

### Modify marvel.php
In the `config/marvel.php` file, you can set the `enabled` option to `false`:

```php
return [
    'license' => [
        'key' => env('LICENSE_KEY'),
        'domain' => env('APP_NOTICE_DOMAIN'),
        'enabled' => false, // Disable license verification
    ]
];
```

This method allows you to disable license verification without modifying the codebase.

## Method 3: Code-Based Disable

### Modify MarvelVerification.php
You can modify the `MarvelVerification.php` file to always return a successful verification:

```php
public function verify($licenseKey)
{
    return new LicenseResult(true, 'License verification disabled');
}
```

This method is useful if you want to disable license verification in a production environment.

## Method 4: Complete Removal

### 1. Remove License-Related Files
Delete the following files:
```
packages/marvel/
├── src/
│   ├── Verification/
│   │   └── MarvelVerification.php
│   ├── Http/
│   │   └── Requests/
│   │       └── LicenseRequest.php
```

### 2. Remove License Checks
Remove license verification calls from:
- `InstallCommand.php`
- `UserController.php`
- `SettingsDataSeed.php`

### 3. Clean Configuration
Remove license-related configurations from:
```
packages/marvel/config/marvel.php
.env
```

### 4. Update Database
```sql
-- Remove license-related columns from settings table
ALTER TABLE settings DROP COLUMN IF EXISTS license_key;
-- Remove any other license-related tables/columns
```

## Important Notes
- Disabling or removing the license system may violate your usage rights. Always ensure you have the appropriate permissions before making these changes.
- Disabling the license system will allow the application to run without license verification, but it may not be compliant with your license agreement.
- Keep original files backed up in case you need to restore functionality.
- Test thoroughly after any modifications to ensure system stability.

## Additional Resources
- [RedQ License Portal](https://redq.io)
- [License Management Documentation](https://redq.io/docs/licenses)
- [Support Contact](https://redq.io/contact)
