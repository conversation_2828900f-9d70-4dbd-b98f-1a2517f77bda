# Delivery System Testing Plan

This document outlines a comprehensive testing plan for the delivery system, including automated tests and manual testing procedures.

## Automated Tests

### Unit Tests

1. **DeliveryRepositoryTest**
   - Tests for assigning deliveries
   - Tests for updating delivery status
   - Tests for status transition validation

2. **DeliveryAgentEarningRepositoryTest**
   - Tests for getting agent earnings
   - Tests for updating payment information

3. **DeliveryObserverTest**
   - Tests for earnings calculation
   - Tests for wallet integration
   - Tests for location history tracking

### Feature Tests

1. **AgentProfileApiTest**
   - Tests for viewing and updating agent profiles
   - Tests for KYC submission
   - Tests for wallet preference toggling

2. **AgentVehicleApiTest**
   - Tests for vehicle CRUD operations
   - Tests for setting active vehicles

3. **AgentDeliveryApiTest**
   - Tests for delivery status updates
   - Tests for proof of delivery
   - Tests for payment confirmation

4. **AgentEarningsApiTest**
   - Tests for viewing balance and history
   - Tests for withdrawal requests
   - Tests for wallet integration

5. **AdminDeliveryApiTest**
   - Tests for delivery assignment
   - Tests for delivery cancellation
   - Tests for agent management

## Manual Testing Procedures

### 1. Agent Registration & KYC

1. **Register as a Delivery Agent**
   - Create a new user account
   - Verify the user has the DELIVERY_AGENT role

2. **Submit KYC Documents**
   - Navigate to the agent profile
   - Upload ID, license, and address proof
   - Verify KYC status changes to SUBMITTED

3. **Admin KYC Approval**
   - Login as admin
   - Navigate to agent management
   - Approve the KYC submission
   - Verify agent's KYC status changes to APPROVED

### 2. Vehicle Management

1. **Add a Vehicle**
   - Login as agent
   - Navigate to vehicle management
   - Add a new vehicle with all required details
   - Verify vehicle appears in the list

2. **Vehicle Verification**
   - Login as admin
   - Navigate to agent's vehicles
   - Verify the vehicle
   - Verify vehicle status changes to verified

3. **Set Active Vehicle**
   - Login as agent
   - Navigate to vehicle list
   - Set a vehicle as active
   - Verify it's marked as the active vehicle

### 3. Availability Management

1. **Toggle Availability**
   - Login as agent
   - Navigate to availability settings
   - Toggle between ONLINE and OFFLINE
   - Verify status changes correctly

2. **Availability Restrictions**
   - Try to go ONLINE without approved KYC
   - Try to go ONLINE without an active vehicle
   - Verify appropriate error messages

### 4. Delivery Assignment & Fulfillment

1. **Admin Assigns Delivery**
   - Login as admin
   - Create an order that requires delivery
   - Assign the order to an agent
   - Verify delivery is created with ASSIGNED status

2. **Agent Accepts Delivery**
   - Login as agent
   - View assigned deliveries
   - Accept a delivery
   - Verify status changes to ACCEPTED_BY_AGENT

3. **Complete Delivery Flow**
   - Update status to PICKED_UP
   - Update status to IN_TRANSIT
   - Update status to REACHED_DESTINATION
   - Add proof of delivery
   - Update status to DELIVERED
   - Confirm payment (for COD)
   - Verify status changes to COMPLETED

4. **Earnings Calculation**
   - Complete a delivery
   - Verify earnings are credited to agent
   - Verify transaction record is created

### 5. Earnings & Withdrawals

1. **View Earnings**
   - Login as agent
   - Navigate to earnings dashboard
   - Verify balance and history are displayed correctly

2. **Update Payment Information**
   - Navigate to payment settings
   - Update payment method details
   - Verify information is saved correctly

3. **Request Withdrawal**
   - Navigate to withdrawals
   - Request a withdrawal
   - Verify request is created with PENDING status
   - Verify balance is updated

4. **Admin Processes Withdrawal**
   - Login as admin
   - Navigate to withdrawal requests
   - Approve, process, and complete a withdrawal
   - Verify status changes at each step
   - Verify agent's balance is updated correctly

### 6. Wallet Integration

1. **Toggle Wallet Preference**
   - Login as agent
   - Navigate to wallet settings
   - Enable earnings to wallet
   - Set conversion rate
   - Verify settings are saved

2. **Earnings to Wallet**
   - Complete a delivery with wallet preference enabled
   - Verify points are added to wallet
   - Verify conversion rate is applied correctly

3. **Use Wallet Points**
   - Make a purchase using wallet points
   - Verify points are deducted correctly
   - Verify transaction record is created

### 7. Edge Cases

1. **Delivery Cancellation**
   - Test cancellation at different stages
   - Verify status changes correctly
   - Verify earnings are not credited for cancelled deliveries

2. **Failed Deliveries**
   - Mark a delivery as failed
   - Verify status changes correctly
   - Verify appropriate notifications are sent

3. **Insufficient Balance**
   - Attempt to withdraw more than available balance
   - Verify appropriate error message

4. **Concurrent Operations**
   - Simulate multiple agents accepting the same delivery
   - Verify system handles race conditions correctly

## Testing Environment Setup

1. **Database Seeding**
   - Create seed data for testing:
     - Users with different roles
     - Orders requiring delivery
     - Shops and products

2. **API Testing Tools**
   - Use Postman or similar tool for API testing
   - Create a collection of API requests for the delivery system

3. **Mobile Device Testing**
   - Test on different devices and screen sizes
   - Verify location tracking works correctly

## Reporting Issues

When reporting issues, include:
1. Steps to reproduce
2. Expected behavior
3. Actual behavior
4. Screenshots or logs
5. Environment details

## Acceptance Criteria

The delivery system will be considered ready for production when:
1. All automated tests pass
2. Manual testing procedures complete without critical issues
3. Performance meets requirements under expected load
4. Security requirements are met
