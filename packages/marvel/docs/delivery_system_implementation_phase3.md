# Delivery System Implementation - Phase 3

This document outlines the implementation details of Phase 3 of the delivery system for the Marvel package.

## Overview

Phase 3 focuses on financial aspects and admin management:
- Earnings Calculation
- Agent Earnings View
- Withdrawal Request
- Admin Management of Agents and Withdrawals

## Observers

We've created an observer to handle automatic earnings calculation and location tracking:

1. **`DeliveryObserver.php`**
   - Automatically calculates and credits earnings when a delivery is completed
   - Tracks location history for delivery agents
   - Registered in the `DeliveryServiceProvider`

## Repositories

We've created the following repositories:

1. **`DeliveryAgentEarningRepository.php`**
   - Manages delivery agent earnings
   - Provides methods for:
     - Getting earnings for an agent
     - Updating payment information
     - Getting earnings history

2. **`DeliveryAgentWithdrawalRepository.php`**
   - Manages withdrawal requests
   - Provides methods for:
     - Requesting withdrawals
     - Approving withdrawals
     - Processing withdrawals
     - Completing withdrawals
     - Rejecting withdrawals

## API Resources

We've created the following API resources:

1. **`DeliveryAgentEarningResource.php`**
   - Transforms DeliveryAgentEarning model to JSON response
   - Includes total earnings, withdrawn amount, and current balance

2. **`DeliveryAgentWithdrawalResource.php`**
   - Transforms DeliveryAgentWithdrawal model to JSON response
   - Includes amount, status, and payment details

## Request Validation

We've created the following request validation classes:

1. **`WithdrawalRequest.php`**
   - Validates withdrawal requests
   - Ensures only delivery agents can request withdrawals

2. **`PaymentInfoRequest.php`**
   - Validates payment information updates
   - Ensures proper format for payment method and details

3. **`WithdrawalActionRequest.php`**
   - Validates admin actions on withdrawals
   - Ensures only admins can approve, process, complete, or reject withdrawals

4. **`KYCActionRequest.php`**
   - Validates admin actions on KYC
   - Ensures only admins can approve or reject KYC

## Controllers

We've created the following controllers:

1. **`Agent/EarningsController.php`**
   - Manages earnings from the agent perspective
   - Provides endpoints for:
     - Viewing current balance
     - Viewing earnings history
     - Viewing withdrawal history
     - Managing payment information

2. **`Agent/WithdrawalController.php`**
   - Manages withdrawals from the agent perspective
   - Provides endpoints for:
     - Requesting withdrawals

3. **`Admin/DeliveryAgentController.php`**
   - Manages delivery agents from the admin perspective
   - Provides endpoints for:
     - Listing all delivery agents
     - Viewing agent details
     - Approving/rejecting KYC
     - Verifying vehicles
     - Activating/deactivating agents

4. **`Admin/AgentWithdrawalController.php`**
   - Manages withdrawals from the admin perspective
   - Provides endpoints for:
     - Listing all withdrawals
     - Viewing withdrawal details
     - Approving withdrawals
     - Processing withdrawals
     - Completing withdrawals
     - Rejecting withdrawals

## Constants

We've updated the `DeliveryConstants.php` file with new error and success messages related to:
- Earnings and withdrawals
- Admin management of agents
- KYC and vehicle verification

## Routes

We've added the following routes:

### Agent Routes

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/agent/earnings/balance` | Get current balance |
| GET | `/agent/earnings/history` | Get earnings history |
| GET | `/agent/earnings/withdrawals` | Get withdrawal history |
| GET | `/agent/payment-info` | Get payment information |
| PUT | `/agent/payment-info` | Update payment information |
| POST | `/agent/withdrawals` | Request a withdrawal |

### Admin Routes

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/admin/agents` | List all delivery agents |
| GET | `/admin/agents/{user}` | View agent details |
| POST | `/admin/agents/{user}/approve-kyc` | Approve KYC |
| POST | `/admin/agents/{user}/reject-kyc` | Reject KYC |
| POST | `/admin/agents/{user}/verify-vehicle/{vehicle}` | Verify vehicle |
| POST | `/admin/agents/{user}/toggle-activation` | Activate/deactivate agent |
| GET | `/admin/agent-withdrawals` | List all withdrawals |
| GET | `/admin/agent-withdrawals/{withdrawal}` | View withdrawal details |
| POST | `/admin/agent-withdrawals/{withdrawal}/approve` | Approve withdrawal |
| POST | `/admin/agent-withdrawals/{withdrawal}/process` | Process withdrawal |
| POST | `/admin/agent-withdrawals/{withdrawal}/complete` | Complete withdrawal |
| POST | `/admin/agent-withdrawals/{withdrawal}/reject` | Reject withdrawal |

## Business Rules

1. **Earnings Calculation**
   - Earnings are automatically calculated when a delivery is completed
   - Delivery fee is added to the agent's total earnings and current balance

2. **Withdrawal Process**
   - Agents can request withdrawals if they have sufficient balance
   - Withdrawal requests go through a workflow:
     - PENDING → APPROVED → PROCESSING → COMPLETED
     - Can be REJECTED at any stage before COMPLETED
   - When a withdrawal is completed, the amount is moved from pending to withdrawn
   - When a withdrawal is rejected, the amount is returned to the agent's current balance

3. **KYC Management**
   - Admins can approve or reject KYC submissions
   - Agents cannot go online until their KYC is approved

4. **Vehicle Verification**
   - Admins can verify vehicles
   - Agents cannot use unverified vehicles for deliveries

5. **Agent Activation**
   - Admins can activate or deactivate agents
   - Deactivated agents are automatically set to OFFLINE status

## Next Steps

The delivery system implementation is now complete with all the core functionality in place. Future enhancements could include:

1. **Analytics Dashboard**
   - Performance metrics for delivery agents
   - Delivery statistics and trends

2. **Automatic Assignment Algorithm**
   - Intelligent assignment of deliveries to agents based on location, performance, etc.

3. **Customer Feedback System**
   - Allow customers to rate delivery agents
   - Use ratings to calculate performance metrics

4. **Mobile App Integration**
   - Dedicated mobile app for delivery agents
   - Real-time location tracking and navigation
