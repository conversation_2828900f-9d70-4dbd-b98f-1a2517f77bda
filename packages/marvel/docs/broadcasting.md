# Marvel Broadcasting System

## Overview
Marvel's broadcasting system enables real-time features through WebSockets, primarily using <PERSON><PERSON><PERSON> as the default driver. The system handles real-time notifications for orders, messages, and store notices.

## File Structure

### Core Files
```
packages/marvel/
├── src/
│   ├── Events/
│   │   ├── MessageSent.php         # Message broadcast event
│   │   ├── OrderCreated.php        # Order creation broadcast event
│   │   └── StoreNoticeEvent.php    # Store notice broadcast event
│   ├── Listeners/
│   │   ├── SendMessageNotification.php
│   │   └── StoreNoticeListener.php
│   ├── Providers/
│   │   ├── EventServiceProvider.php # Registers events and listeners
│   │   └── BroadcastServiceProvider.php # Configures broadcasting
│   └── Rest/
│       └── Channel.php             # Defines broadcast channels
├── config/
│   └── broadcasting.php            # Broadcasting configuration
└── resources/
    └── js/
        └── echo-setup.js           # Laravel Echo configuration
```

### Configuration Files
- `.env` - Broadcasting environment variables
- `config/broadcasting.php` - Driver configuration
- `config/websockets.php` - WebSocket server settings (if using local server)

## Configuration

### Environment Setup
```env
BROADCAST_DRIVER=pusher
PUSHER_APP_ID=your_app_id
PUSHER_APP_KEY=your_app_key
PUSHER_APP_SECRET=your_app_secret
PUSHER_APP_CLUSTER=your_cluster
```

### Available Drivers
- Pusher (default)
- Redis
- Log
- Null

## Channel Definitions
Location: `packages/marvel/src/Rest/Channel.php`

```php
Broadcast::channel('store_notice.created.{userID}', function ($user, $userID) {
    return (int) $user->id === (int) $userID;
}, ['middleware' => ['auth:sanctum']]);

Broadcast::channel('order.created.{userID}', function ($user, $userID) {
    return (int) $user->id === (int) $userID;
}, ['middleware' => ['auth:sanctum']]);

Broadcast::channel('message.created.{userID}', function ($user, $userID) {
    return (int) $user->id === (int) $userID;
}, ['middleware' => ['auth:sanctum']]);
```

## Event Classes

### MessageSent Event
Location: `packages/marvel/src/Events/MessageSent.php`
- Implements `ShouldBroadcast`
- Broadcasts to `message.created.{userID}`
- Contains message data and recipient information

### OrderCreated Event
Location: `packages/marvel/src/Events/OrderCreated.php`
- Implements `ShouldBroadcast`
- Broadcasts to `order.created.{userID}`
- Contains order details and status updates

### StoreNotice Event
Location: `packages/marvel/src/Events/StoreNoticeEvent.php`
- Implements `ShouldBroadcast`
- Broadcasts to `store_notice.created.{userID}`
- Contains notice content and metadata

## Frontend Implementation

### Echo Setup
Location: `resources/js/echo-setup.js`

```javascript
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Echo = new Echo({
    broadcaster: 'pusher',
    key: process.env.MIX_PUSHER_APP_KEY,
    cluster: process.env.MIX_PUSHER_APP_CLUSTER,
    forceTLS: true
});
```

### Event Listeners
```javascript
// Listen for store notices
Echo.private(`store_notice.created.${userId}`)
    .listen('StoreNoticeEvent', (e) => {
        console.log(e);
    });

// Listen for orders
Echo.private(`order.created.${userId}`)
    .listen('OrderCreated', (e) => {
        console.log(e);
    });

// Listen for messages
Echo.private(`message.created.${userId}`)
    .listen('MessageSent', (e) => {
        console.log(e);
    });
```

## Making Modifications

### Adding New Channels
1. Define channel in `packages/marvel/src/Rest/Channel.php`
2. Create corresponding event class in `packages/marvel/src/Events/`
3. Register event in `EventServiceProvider.php`
4. Add frontend listener in your JavaScript

### Modifying Existing Channels
1. Update channel authorization in `Channel.php`
2. Modify corresponding event class
3. Update frontend listeners

### Changing Broadcast Driver
1. Update `.env` BROADCAST_DRIVER
2. Configure new driver in `config/broadcasting.php`
3. Update frontend Echo configuration if necessary

## Security
- All channels are private and require authentication
- Uses Laravel Sanctum for channel authentication
- Validates user access through channel authorization callbacks

## Best Practices
1. Always use SSL/TLS in production
2. Implement error handling for failed broadcasts
3. Use queue workers for broadcasting events
4. Monitor broadcast events in production
5. Implement reconnection logic in frontend

## Troubleshooting

### Common Issues
1. Connection refused
   - Verify Pusher credentials
   - Check SSL/TLS settings
   - Ensure proper CORS configuration

2. Authentication failed
   - Verify Sanctum setup
   - Check user authentication
   - Validate channel authorization

3. Events not received
   - Check event naming
   - Verify channel subscriptions
   - Monitor Laravel logs

### Debug Tools
- Laravel Telescope for event monitoring
- Pusher Debug Console
- Browser DevTools WebSocket inspector

## Performance Considerations
- Use queue workers for broadcasting events
- Implement proper caching strategies
- Monitor WebSocket connections
- Scale WebSocket servers as needed

## Additional Resources
- [Laravel Broadcasting Documentation](https://laravel.com/docs/broadcasting)
- [Pusher Documentation](https://pusher.com/docs)
- [Laravel Echo Documentation](https://laravel.com/docs/broadcasting#client-side-installation)
