# PayPal Integration in Marvel Package

This documentation provides a comprehensive guide to the PayPal payment gateway integration in the Marvel package. It covers configuration, implementation details, webhook handling, and troubleshooting.

## Table of Contents

1. [Overview](#overview)
2. [Requirements](#requirements)
3. [Configuration](#configuration)
4. [Implementation Details](#implementation-details)
5. [Payment Flow](#payment-flow)
6. [Webhook Handling](#webhook-handling)
7. [Testing](#testing)
8. [Troubleshooting](#troubleshooting)
9. [Examples](#examples)

## Overview

The Marvel package integrates PayPal as one of its payment gateways, allowing customers to make payments using PayPal's services. The integration uses the `srmklive/paypal` package (version 3.0.19) to interact with PayPal's API.

PayPal integration in the Marvel package supports:
- Creating payment intents
- Processing payments
- Handling webhooks for payment status updates
- Verifying payments

## Requirements

To use PayPal integration in the Marvel package, you need:

1. A PayPal Business account
2. PayPal API credentials (Client ID and Secret)
3. Properly configured webhook endpoints
4. <PERSON><PERSON> 9 or 10

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```
# Payment -> PayPal
ACTIVE_PAYMENT_GATEWAY=PayPal
PAYPAL_CURRENCY=USD
PAYPAL_NOTIFY_URL=https://your-domain.com/webhooks/paypal
PAYPAL_LOCALE=en_US
PAYPAL_VALIDATE_SSL=true

# Values: sandbox or live
PAYPAL_MODE=sandbox

# PayPal Sandbox Credentials
PAYPAL_SANDBOX_CLIENT_ID=your_sandbox_client_id
PAYPAL_SANDBOX_CLIENT_SECRET=your_sandbox_client_secret

# PayPal Live Credentials
PAYPAL_LIVE_CLIENT_ID=your_live_client_id
PAYPAL_LIVE_CLIENT_SECRET=your_live_client_secret

# PayPal Webhook ID (from PayPal Developer Dashboard)
PAYPAL_WEBHOOK_ID=your_webhook_id

# Frontend URLs
SHOP_URL=https://your-frontend-domain.com
```

### Configuration in `shop.php`

The PayPal configuration is defined in `packages/marvel/config/shop.php`:

```php
'paypal' => [
    'mode'           => env('PAYPAL_MODE', 'sandbox'), // Can only be 'sandbox' Or 'live'
    'sandbox'        => [
        'client_id'     => env('PAYPAL_SANDBOX_CLIENT_ID', ''),
        'client_secret' => env('PAYPAL_SANDBOX_CLIENT_SECRET', ''),
    ],
    'live'           => [
        'client_id'     => env('PAYPAL_LIVE_CLIENT_ID', ''),
        'client_secret' => env('PAYPAL_LIVE_CLIENT_SECRET', ''),
    ],
    'payment_action' => env('PAYPAL_PAYMENT_ACTION', 'Sale'), // Can only be 'Sale', 'Authorization' or 'Order'
    'webhook_id'     => env('PAYPAL_WEBHOOK_ID'),
    'currency'       => env('PAYPAL_CURRENCY', 'USD'),
    'notify_url'     => env('PAYPAL_NOTIFY_URL', ''), // Change this accordingly for your application
    'locale'         => env('PAYPAL_LOCALE', 'en_US'), // force gateway language
    'validate_ssl'   => env('PAYPAL_VALIDATE_SSL', true), // Validate SSL when creating api client
],
```

### Setting Up PayPal Webhooks

1. Log in to the [PayPal Developer Dashboard](https://developer.paypal.com/dashboard/)
2. Navigate to Webhooks under the "My Apps & Credentials" section
3. Click "Add Webhook"
4. Enter your webhook URL: `https://your-domain.com/webhooks/paypal`
5. Select the following events:
   - PAYMENT.CAPTURE.COMPLETED
   - PAYMENT.CAPTURE.PENDING
   - PAYMENT.CAPTURE.CANCELLED
   - PAYMENT.CAPTURE.REVERSED
6. Save the webhook and copy the Webhook ID
7. Add the Webhook ID to your `.env` file as `PAYPAL_WEBHOOK_ID`

## Implementation Details

### PayPal Class

The PayPal integration is implemented in the `Marvel\Payments\Paypal` class, which extends the `Base` class and implements the `PaymentInterface`. This class handles all PayPal-specific payment operations.

Key methods in the PayPal class:

- `__construct()`: Initializes the PayPal client with configuration from `shop.php`
- `getIntent($data)`: Creates a payment intent with PayPal
- `verify($id)`: Verifies a payment after completion
- `handleWebHooks($request)`: Processes webhook events from PayPal
- `updatePaymentOrderStatus($request, $orderStatus, $paymentStatus)`: Updates order status based on payment events

### Database Models

The PayPal integration uses the following database models:

1. **PaymentIntent**: Stores payment intent information
   - Contains the PayPal order ID and other payment details
   - Related to the Order model

2. **Order**: Stores order information
   - Contains payment status and gateway information
   - Related to PaymentIntent model

## Payment Flow

The PayPal payment flow in the Marvel package works as follows:

1. **Create Payment Intent**:
   - When a customer checks out, the system creates a payment intent via the `getIntent()` method
   - This generates a PayPal order with the specified amount and currency
   - The method returns a redirect URL to PayPal's checkout page

2. **Customer Completes Payment**:
   - The customer is redirected to PayPal's checkout page
   - After completing payment, PayPal redirects the customer back to the specified return URL
   - The return URL is configured as `{SHOP_URL}/orders/{order_tracking_number}/thank-you`

3. **Verify Payment**:
   - The system verifies the payment using the `verify()` method
   - This captures the payment and confirms the transaction

4. **Update Order Status**:
   - PayPal sends webhook events to the configured webhook URL
   - The system processes these events and updates the order status accordingly

## Webhook Handling

The Marvel package handles PayPal webhooks through the `WebHookController` and the `Paypal` class:

1. Webhook events are received at `/webhooks/paypal`
2. The `WebHookController::paypal()` method passes the request to `Payment::handleWebHooks()`
3. The `handleWebHooks()` method in the `Paypal` class:
   - Verifies the webhook signature using the webhook ID
   - Processes the event based on its type
   - Updates the order status accordingly

### Supported Webhook Events

The system handles the following PayPal webhook events:

- `PAYMENT.CAPTURE.COMPLETED`: Payment was completed successfully
  - Updates order status to `PROCESSING`
  - Updates payment status to `SUCCESS`

- `PAYMENT.CAPTURE.PENDING`: Payment is pending
  - Updates order status to `PENDING`
  - Updates payment status to `PENDING`

- `PAYMENT.CAPTURE.CANCELLED`: Payment was cancelled
  - Updates order status to `PENDING`
  - Updates payment status to `FAILED`

- `PAYMENT.CAPTURE.REVERSED`: Payment was reversed
  - Updates order status to `CANCELLED`
  - Updates payment status to `REVERSAL`

## Testing

To test PayPal integration:

1. Set `PAYPAL_MODE=sandbox` in your `.env` file
2. Use PayPal sandbox credentials
3. Create a test order and proceed to checkout
4. Select PayPal as the payment method
5. Complete the payment using a PayPal sandbox account
6. Verify that the order status is updated correctly

### Testing Webhooks Locally

For local development, you can use tools like [ngrok](https://ngrok.com/) to expose your local server to the internet and receive webhooks:

1. Install ngrok
2. Run `ngrok http your_local_port`
3. Use the generated ngrok URL as your webhook URL in the PayPal Developer Dashboard
4. Update your `PAYPAL_NOTIFY_URL` in the `.env` file

## Troubleshooting

### Common Issues

1. **Payment Intent Creation Fails**:
   - Check your PayPal API credentials
   - Ensure the currency is supported by PayPal
   - Verify that the amount is valid (not zero or negative)

2. **Webhook Verification Fails**:
   - Ensure the webhook ID in your `.env` file matches the one in the PayPal Developer Dashboard
   - Check that the webhook URL is accessible from the internet
   - Verify that your webhook is configured to receive the correct events

3. **Payment Verification Fails**:
   - Check that the payment ID is valid
   - Ensure the payment has not already been captured
   - Verify that your PayPal account is in good standing

### Debugging

To debug PayPal integration issues:

1. Check the Laravel logs for detailed error messages
2. Enable debug mode in your Laravel application
3. Inspect the PayPal API responses in the logs
4. Verify webhook events in the PayPal Developer Dashboard

## Examples

### Creating a PayPal Payment

The following example shows how to create a PayPal payment intent:

```php
// In your controller
public function createPayment(Request $request)
{
    $data = [
        'amount' => $request->amount,
        'order_tracking_number' => $request->order_id,
        // Other required data
    ];
    
    try {
        $paypal = new \Marvel\Payments\Paypal();
        $intent = $paypal->getIntent($data);
        
        // Store the payment intent
        PaymentIntent::create([
            'order_id' => $request->order_id,
            'tracking_number' => $request->order_id,
            'payment_gateway' => 'PAYPAL',
            'payment_intent_info' => $intent,
        ]);
        
        // Return the redirect URL
        return response()->json([
            'redirect_url' => $intent['redirect_url'],
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
        ], 400);
    }
}
```

### Verifying a PayPal Payment

The following example shows how to verify a PayPal payment:

```php
// In your controller
public function verifyPayment(Request $request)
{
    try {
        $paypal = new \Marvel\Payments\Paypal();
        $result = $paypal->verify($request->payment_id);
        
        if ($result) {
            // Payment was successful
            // Update order status
            $order = Order::where('tracking_number', $request->order_id)->first();
            $order->payment_status = 'payment-success';
            $order->save();
            
            return response()->json([
                'success' => true,
                'message' => 'Payment verified successfully',
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Payment verification failed',
            ], 400);
        }
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
        ], 400);
    }
}
```

### Processing a PayPal Webhook

The webhook processing is handled automatically by the `WebHookController` and the `Paypal` class. The system will:

1. Verify the webhook signature
2. Process the event based on its type
3. Update the order status accordingly
4. Return a 200 response to acknowledge receipt of the webhook

No additional code is required to handle webhooks beyond the existing implementation.
