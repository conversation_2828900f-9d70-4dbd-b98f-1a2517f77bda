# Delivery System Implementation - Phase 2

This document outlines the implementation details of Phase 2 of the delivery system for the Marvel package.

## Overview

Phase 2 focuses on the core delivery functionality:
- Delivery Assignment (Manual by Admin)
- Agent Delivery Viewing & Acceptance
- Delivery Status Updates
- Proof of Delivery (POD)
- Pay-on-Delivery Confirmation

## Repositories

We've created the following repository in `packages/marvel/src/Database/Repositories/`:

1. **`DeliveryRepository.php`**
   - Manages delivery operations
   - Provides methods for:
     - Assigning deliveries to agents
     - Getting deliveries for an agent
     - Updating delivery status
     - Adding proof of delivery
     - Confirming payment for cash on delivery

## API Resources

We've created the following API resources in `packages/marvel/src/Http/Resources/`:

1. **`DeliveryResource.php`**
   - Transforms Delivery model to JSON response
   - Includes relationships with order, delivery agent, and status logs

2. **`OrderSummaryResource.php`**
   - Provides a summary of order information for embedding in DeliveryResource
   - Includes basic order details, customer, and shop information

3. **`DeliveryStatusLogResource.php`**
   - Transforms DeliveryStatusLog model to JSON response
   - Includes status, user, notes, and location information

## Request Validation

We've created the following request validation classes in `packages/marvel/src/Http/Requests/`:

1. **`AssignDeliveryRequest.php`**
   - Validates delivery assignment requests
   - Ensures only admin and store owners can assign deliveries

2. **`UpdateDeliveryStatusRequest.php`**
   - Validates delivery status update requests
   - Includes rules for notes, reason, and location data

3. **`ProofOfDeliveryRequest.php`**
   - Validates proof of delivery submissions
   - Includes rules for POD type and various proof formats (image, signature, code)

## Controllers

We've created the following controllers:

1. **`Admin/DeliveryController.php`**
   - Manages deliveries from the admin perspective
   - Provides endpoints for:
     - Listing all deliveries
     - Viewing delivery details
     - Assigning deliveries to agents
     - Cancelling deliveries
     - Listing orders pending assignment

2. **`Agent/DeliveryController.php`**
   - Manages deliveries from the agent perspective
   - Provides endpoints for:
     - Listing assigned deliveries
     - Viewing delivery details
     - Accepting deliveries
     - Rejecting deliveries

3. **`Agent/DeliveryStatusController.php`**
   - Manages delivery status updates by agents
   - Provides endpoints for:
     - Updating status to PICKED_UP
     - Updating status to IN_TRANSIT
     - Updating status to REACHED_DESTINATION
     - Adding proof of delivery
     - Updating status to DELIVERED
     - Updating status to FAILED_DELIVERY
     - Confirming payment for cash on delivery

## Constants

We've updated the `DeliveryConstants.php` file with new error and success messages related to:
- Delivery assignment
- Status transitions
- Proof of delivery
- Payment confirmation

## Routes

We've added the following routes:

### Admin Routes

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/admin/deliveries` | List all deliveries |
| GET | `/admin/deliveries/{delivery}` | View delivery details |
| POST | `/admin/orders/{order}/assign-delivery` | Assign delivery to an agent |
| POST | `/admin/deliveries/{delivery}/cancel` | Cancel a delivery |
| GET | `/admin/orders/pending-assignment` | List orders pending delivery assignment |

### Agent Routes

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/agent/deliveries` | List assigned deliveries |
| GET | `/agent/deliveries/{delivery}` | View delivery details |
| POST | `/agent/deliveries/{delivery}/accept` | Accept a delivery |
| POST | `/agent/deliveries/{delivery}/reject` | Reject a delivery |
| POST | `/agent/deliveries/{delivery}/pickup` | Update status to PICKED_UP |
| POST | `/agent/deliveries/{delivery}/in-transit` | Update status to IN_TRANSIT |
| POST | `/agent/deliveries/{delivery}/reached` | Update status to REACHED_DESTINATION |
| POST | `/agent/deliveries/{delivery}/proof-of-delivery` | Add proof of delivery |
| POST | `/agent/deliveries/{delivery}/deliver` | Update status to DELIVERED |
| POST | `/agent/deliveries/{delivery}/fail` | Update status to FAILED_DELIVERY |
| POST | `/agent/deliveries/{delivery}/confirm-payment` | Confirm payment for cash on delivery |

## Business Rules

1. **Delivery Assignment**
   - Only admins and store owners can assign deliveries
   - Orders must require delivery and not already have a delivery assigned
   - Delivery agents must have approved KYC

2. **Status Transitions**
   - Status transitions follow a specific flow:
     - PENDING_ASSIGNMENT → ASSIGNED
     - ASSIGNED → ACCEPTED_BY_AGENT/REJECTED_BY_AGENT
     - ACCEPTED_BY_AGENT → PICKED_UP
     - PICKED_UP → IN_TRANSIT
     - IN_TRANSIT → REACHED_DESTINATION
     - REACHED_DESTINATION → DELIVERED
     - DELIVERED → COMPLETED
   - Each status update creates a log entry with timestamp and user information

3. **Proof of Delivery**
   - POD can be added when status is REACHED_DESTINATION or DELIVERED
   - POD types include SIGNATURE, PHOTO, CODE, or NONE
   - POD is required before marking as DELIVERED if POD type is not NONE

4. **Payment Confirmation**
   - Payment can only be confirmed when status is DELIVERED
   - Confirming payment updates order payment status to SUCCESS
   - Confirming payment updates delivery status to COMPLETED

## Next Steps

The next phase will implement:
- Earnings Calculation
- Agent Earnings View
- Withdrawal Request
- Admin Management of Agents and Withdrawals
