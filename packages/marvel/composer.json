{"name": "marvel/shop", "description": ":package_description", "license": "license", "authors": [{"name": "RedQ", "email": "<EMAIL>", "homepage": "https://redq.io"}], "homepage": "https://github.com/marvel/gateway", "keywords": ["<PERSON><PERSON>", "Shop"], "require": {"illuminate/support": "~9|~10", "nuwave/lighthouse": "5.71.0", "laravel/legacy-factories": "1.3.2", "cviebrock/eloquent-sluggable": "10.0.0", "laravel/sanctum": "3.3.1", "prettus/l5-repository": "2.9.0", "spatie/laravel-medialibrary": "10.14.0", "spatie/laravel-permission": "6.0.0", "php-http/guzzle7-adapter": "1.0.0", "bensampo/laravel-enum": "6.6.4", "league/flysystem-aws-s3-v3": "3.16.0", "drewm/mailchimp-api": "^2.5", "spatie/laravel-newsletter": "5.1.1", "spatie/period": "2.4.0", "kodeine/laravel-meta": "2.2.1", "maatwebsite/excel": "3.1.48", "cknow/laravel-money": "7.2.0", "mollie/laravel-mollie": "2.25.0", "mll-lab/graphql-php-scalars": "5.4.1", "razorpay/razorpay": "2.8.7", "unicodeveloper/laravel-paystack": "1.1.0", "stripe/stripe-php": "13.1.0", "dgvai/laravel-sslcommerz": "^1.0", "iyzico/iyzipay-php": "^2.0", "luigel/laravel-paymongo": "^2.4", "mll-lab/laravel-graphiql": "^3.1", "pusher/pusher-php-server": "^7.2", "openai-php/client": "^0.7.4", "twilio/sdk": "7.12.0", "xendit/xendit-php": "^3.0.0", "srmklive/paypal": "3.0.19"}, "require-dev": {"phpunit/phpunit": "10.0.13", "mockery/mockery": "1.5.1", "orchestra/testbench": "8.0.4", "sempro/phpunit-pretty-print": "1.4.0"}, "autoload": {"classmap": ["src", "stubs"], "psr-4": {"Marvel\\Database\\Factories\\": "database/factories/", "Marvel\\Database\\Seeders\\": "database/seeders/", "Marvel\\Tests\\": "tests/"}}, "autoload-dev": {"psr-4": {"Marvel\\Tests\\": "tests"}}, "extra": {"laravel": {"providers": ["Marvel\\ShopServiceProvider"], "aliases": {"Shop": "Marvel\\Facades\\Shop"}}}}