# phpstan.neon
includes:
    - ./vendor/larastan/larastan/extension.neon

parameters:
    paths:
        - app/
        - packages/marvel/src/ # Add the path to your custom package
        # Add other paths you want to analyze, e.g., routes/, config/

    # Level 5 is a good starting point
    level: 5

    # Don't treat PHPDoc types as certain, which helps with Eloquent model type issues
    treatPhpDocTypesAsCertain: false

    # Optional: If you have paths you want to ignore
    excludePaths:
        - vendor/*
        - storage/*
        - resources/views/*
        - bootstrap/cache/*
        - public/*
        - database/migrations/*
        - _ide_helper.php (?)
        - _ide_helper_models.php (?)
        - .phpstorm.meta.php (?)

    # Optional: If you need to specify the path to your Laravel bootstrap file
    # bootstrapFiles:
    #     - bootstrap/app.php
