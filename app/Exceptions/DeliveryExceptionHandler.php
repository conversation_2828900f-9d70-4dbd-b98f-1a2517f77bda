<?php

namespace App\Exceptions;

use Illuminate\Http\JsonResponse;
use Marvel\Exceptions\DeliveryException;

class DeliveryExceptionHandler
{
    /**
     * Handle the delivery exception.
     *
     * @param  \Marvel\Exceptions\DeliveryException  $exception
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handle(DeliveryException $exception, $request)
    {
        $statusCode = $exception->getStatusCode();
        
        return new JsonResponse([
            'success' => false,
            'message' => $exception->getMessage(),
            'reason' => $exception->getReason(),
            'status_code' => $statusCode
        ], $statusCode);
    }
}
