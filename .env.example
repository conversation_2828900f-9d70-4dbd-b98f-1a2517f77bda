APP_NAME=ChawkBazar
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=http://localhost
APP_VERSION=6.8.0
APP_SERVICE=marvel.test
APP_NOTICE_DOMAIN=CHAWKBAZAR_
DUMMY_DATA_PATH=chawkbazar

# Multilang
# If you want to enable multilang then follow this doc -> https://pickbazar-doc.vercel.app/multilingual
TRANSLATION_ENABLED=false
DEFAULT_LANGUAGE=en

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=marvel_laravel
DB_USERNAME=marvel_laravel
DB_PASSWORD=1Amarvel@laravel

BROADCAST_DRIVER=pusher
CACHE_DRIVER=file
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

# Firebase Cloud Messaging
FCM_SERVER_KEY=
FCM_SENDER_ID=

MEMCACHED_HOST=memcached

LIGHTHOUSE_CACHE_ENABLE=false

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# Email
# To configure email visit https://chawkbazar-laravel-doc.vercel.app/email-configuration
MAIL_MAILER=mailgun
MAIL_HOST=smtp.mailgun.org
MAILGUN_DOMAIN=
MAILGUN_SECRET=
MAIL_FROM_ADDRESS=
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
ADMIN_EMAIL=<EMAIL>

# File system
MEDIA_DISK=public
FILESYSTEM_DISK=local

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=
AWS_BUCKET=

# Connect With Frontend
SHOP_URL=
DASHBOARD_URL=

# To configure the payment gateway follow this doc -> https://pickbazar-doc.vercel.app/payment
# PAYMENT
ACTIVE_PAYMENT_GATEWAY=Stripe
DEFAULT_CURRENCY=XAF

# Payment -> Stripe
STRIPE_API_KEY=
STRIPE_WEBHOOK_SECRET_KEY=
# Payment -> Paypal

# Add currency like USD
PAYPAL_CURRENCY=
# Change this accordingly for your application.
PAYPAL_NOTIFY_URL=
# force gateway language  i.e. it_IT, es_ES, en_US ... (for express checkout only)
PAYPAL_LOCALE=
# Validate SSL when creating api client.
PAYPAL_VALIDATE_SSL=

# Values: sandbox or live -> sanbox for development and live for production
PAYPAL_MODE=

#PayPal Setting & API Credentials - sandbox
PAYPAL_SANDBOX_CLIENT_ID=
PAYPAL_SANDBOX_CLIENT_SECRET=

#PayPal Setting & API Credentials - live
PAYPAL_LIVE_CLIENT_ID=
PAYPAL_LIVE_CLIENT_SECRET=
PAYPAL_WEBHOOK_ID=
PAYPAL_REDIRECT_URL=

# Payment -> Mollie
MOLLIE_KEY=
MOLLIE_REDIRECT_URL=
MOLLIE_WEBHOOK_URL=

# Payment -> Razorpay
RAZORPAY_KEY_ID=
RAZORPAY_KEY_SECRET=
RAZORPAY_WEBHOOK_SECRET_KEY=

# Payment -> PayStack
PAYSTACK_PUBLIC_KEY=
PAYSTACK_SECRET_KEY=
PAYSTACK_PAYMENT_URL=https://api.paystack.co
MERCHANT_EMAIL=

# Remove sandbox- for production
IYZIPAY_BASE_URL=https://sandbox-api.iyzipay.com
IYZIPAY_API_KEY=YOUR_KEY_HERE
IYZIPAY_SECRET_KEY=YOUR_SECRET_KEY_HERE

# Payment -> bKash
BKASH_SANDBOX=true  #for production use false
BKASH_APP_KEY=""
BKASH_APP_SECRET=""
BKASH_USERNAME=""
BKASH_PASSWORD=""

# Payment -> Paymongo
PAYMONGO_SECRET_KEY=
PAYMONGO_PUBLIC_KEY=
# This is the secret from the webhook you created.
PAYMONGO_WEBHOOK_SIG=

# Payment -> Flutterwave
FLW_PUBLIC_KEY=
FLW_SECRET_KEY=
FLW_SECRET_HASH=

# AI --> OpenAi
OPENAI_SECRET_KEY=

# Social Login
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URI=http://127.0.0.1/login/google/callback

FACEBOOK_CLIENT_ID=
FACEBOOK_CLIENT_SECRET=
FACEBOOK_REDIRECT_URI=https://127.0.0.1/login/facebook/callback

# Mailchimp Newslater
NEWSLETTER_API_KEY=
NEWSLETTER_LIST_ID=

# OTP Message System
ACTIVE_OTP_GATEWAY=twilio

TWILIO_AUTH_TOKEN=
TWILIO_ACCOUNT_SID=
TWILIO_VERIFICATION_SID=
TWILIO_FROM_NUMBER=

MESSAGEBIRD_API_KEY=
MESSAGEBIRD_ORIGINATOR=Marvel

# PUSHER_ENABLED is either true or false
PUSHER_ENABLED=true
PUSHER_APP_ID=7b3b3b3b3b3b3b3b3b3b
PUSHER_APP_KEY=7b3b3b3b3b3b3b3b3b3b
PUSHER_APP_SECRET=7b3b3b3b3b3b3b3b3b3b
PUSHER_APP_CLUSTER=ap2

# Enable/disable automatic assignment
DELIVERY_AUTO_ASSIGNMENT_ENABLED=true

# Search radius in kilometers
DELIVERY_AUTO_ASSIGNMENT_RADIUS=15

# Retry configuration
DELIVERY_AUTO_ASSIGNMENT_RETRY_ATTEMPTS=3
DELIVERY_AUTO_ASSIGNMENT_RETRY_DELAY=5

# Assignment delay (minutes to wait before assignment)
DELIVERY_AUTO_ASSIGNMENT_DELAY=0

# Queue configuration
DELIVERY_AUTO_ASSIGNMENT_QUEUE=default

# Agent selection strategy
DELIVERY_AGENT_SELECTION_STRATEGY=nearest

# Fee calculation
DELIVERY_BASE_FEE=10.0
DELIVERY_PER_KM_FEE=1.0

# Maximum search radius
DELIVERY_MAX_SEARCH_RADIUS=50


# --- AWS S3 Configuration ---
AWS_ACCESS_KEY_ID=YOUR_IAM_USER_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY=YOUR_IAM_USER_SECRET_ACCESS_KEY
AWS_DEFAULT_REGION=eu-north-1
AWS_BUCKET=goddora-media
AWS_USE_PATH_STYLE_ENDPOINT=false
MEDIA_DISK_URL=https://goddora-media.s3.eu-north-1.amazonaws.com