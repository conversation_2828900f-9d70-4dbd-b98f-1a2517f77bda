<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Str;

class UserPromocodesSeeds extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();

        foreach ($users as $user) {
            $base = strtoupper(Str::slug($user->name ?? 'user', ''));

            // Fallback for short/invalid names
            if (strlen($base) < 3) {
                $base = 'USER';
            }

            // Generate a unique promo code
            do {
                $suffix = now()->timestamp . rand(100, 999); // More entropy
                $promoCode = $base . $suffix;
            } while (User::where('promo_code', $promoCode)->exists());

            $user->promo_code = $promoCode;
            $user->save();
        }
    }
}
