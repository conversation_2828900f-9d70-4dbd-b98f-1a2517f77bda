<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Profile Management</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4361ee;
            --primary-hover: #3a56d4;
            --success: #4cc9f0;
            --error: #f72585;
            --warning: #fca311;
            --text: #2b2d42;
            --text-light: #8d99ae;
            --bg: #f8f9fa;
            --card-bg: #ffffff;
            --border: #e9ecef;
        }
        
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body { font-family: 'Inter', sans-serif; color: var(--text); background-color: var(--bg); line-height: 1.6; padding: 20px; }
        .app-container { max-width: 1000px; margin: 0 auto; display: grid; gap: 24px; }
        .card { background: var(--card-bg); border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); padding: 28px; }
        .card-header { margin-bottom: 24px; display: flex; justify-content: space-between; align-items: center; }
        h2 { font-size: 1.5rem; font-weight: 600; color: var(--primary); }
        h3 { font-size: 1.2rem; font-weight: 500; margin-bottom: 16px; color: var(--primary); border-bottom: 1px solid var(--border); padding-bottom: 8px;}
        .form-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .form-group { margin-bottom: 16px; }
        label { display: block; margin-bottom: 8px; font-weight: 500; font-size: 0.9rem; }
        .required::after { content: " *"; color: var(--error); }
        input, select, textarea { width: 100%; padding: 12px; border: 1px solid var(--border); border-radius: 8px; font-family: inherit; font-size: 0.95rem; }
        input:focus, select:focus, textarea:focus { outline: none; border-color: var(--primary); box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2); }
        .file-upload { position: relative; padding: 16px; border: 2px dashed var(--border); border-radius: 8px; text-align: center; cursor: pointer; }
        .file-upload:hover { border-color: var(--primary); background: rgba(67, 97, 238, 0.05); }
        .file-upload input[type="file"] { position: absolute; left: 0; top: 0; opacity: 0; width: 100%; height: 100%; cursor: pointer; }
        .file-upload small { display: block; margin-top: 8px; color: var(--text-light); font-size: 0.8rem; }
        .btn { padding: 12px 24px; background: var(--primary); color: white; border: none; border-radius: 8px; font-weight: 500; font-size: 1rem; cursor: pointer; }
        .btn:hover { background: var(--primary-hover); }
        .response { margin-top: 20px; padding: 16px; border-radius: 8px; font-size: 0.9rem; }
        .success { background: rgba(76, 201, 240, 0.1); color: var(--success); border-left: 4px solid var(--success); }
        .error { background: rgba(247, 37, 133, 0.1); color: var(--error); border-left: 4px solid var(--error); }
        .profile-display img { max-width: 150px; border-radius: 8px; margin-bottom: 10px; }
        .profile-display p { margin-bottom: 8px; }
        .profile-display strong { color: var(--primary); }
        .kyc-doc-link { color: var(--primary-hover); text-decoration: none; }
        .kyc-doc-link:hover { text-decoration: underline; }
        .checkbox-group { display: flex; align-items: center; }
        .checkbox-group input[type="checkbox"] { width: auto; margin-right: 8px; }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Login Section -->
        <div class="card">
            <div class="card-header"><h2>Agent Login</h2></div>
            <form id="loginForm" class="form-grid">
                <div class="form-group">
                    <label class="required">Email Address</label>
                    <input type="email" id="email" required placeholder="<EMAIL>" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label class="required">Password</label>
                    <input type="password" id="password" required placeholder="••••••••" value="12345678">
                </div>
                <div class="form-group" style="grid-column: 1 / -1;">
                    <button type="submit" class="btn">Sign In</button>
                </div>
            </form>
            <div id="loginResponse" class="response"></div>
        </div>

        <!-- Current Profile Display Section -->
        <div class="card" id="profileDisplaySection" style="display: none;">
            <div class="card-header">
                <h2>Current Profile</h2>
                <button onclick="fetchAgentProfile()" class="btn" style="background-color: var(--success);">Refresh Profile</button>
            </div>
            <div id="profileDetails" class="profile-display">
                <p>Loading profile...</p>
            </div>
        </div>

        <!-- Profile Update Section -->
        <div class="card" id="profileUpdateSection" style="display: none;">
            <div class="card-header"><h2>Update Profile</h2></div>
            <form id="profileUpdateForm">
                <h3>Basic Information</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="name">Full Name</label>
                        <input type="text" id="name" name="name">
                    </div>
                    <div class="form-group">
                        <label for="phone_number">Phone Number</label>
                        <input type="text" id="phone_number" name="phone_number">
                    </div>
                     <div class="form-group checkbox-group">
                        <input type="checkbox" id="use_wallet_for_earnings" name="use_wallet_for_earnings" value="1">
                        <label for="use_wallet_for_earnings">Use Wallet for Earnings</label>
                    </div>
                </div>

                <h3>Location (Optional)</h3>
                 <div class="form-grid">
                    <div class="form-group">
                        <label for="current_location_lat">Current Latitude</label>
                        <input type="number" step="any" id="current_location_lat" name="current_location[lat]">
                    </div>
                    <div class="form-group">
                        <label for="current_location_lng">Current Longitude</label>
                        <input type="number" step="any" id="current_location_lng" name="current_location[lng]">
                    </div>
                </div>

                <h3>Profile Photo</h3>
                <div class="form-group">
                    <label for="profile_photo">New Profile Photo</label>
                    <div class="file-upload">
                        <span>Click to upload image</span>
                        <input type="file" id="profile_photo" name="profile_photo" accept="image/jpeg,image/png,image/jpg">
                        <small>Max 5MB • JPG, PNG</small>
                    </div>
                </div>

                <h3>KYC Documents</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="kyc_id_front">ID Front</label>
                        <div class="file-upload">
                            <span>Upload ID Front</span>
                            <input type="file" id="kyc_id_front" name="kyc_id_front" accept="image/jpeg,image/png,image/jpg,application/pdf">
                            <small>Max 5MB • JPG, PNG, PDF</small>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="kyc_id_back">ID Back</label>
                        <div class="file-upload">
                            <span>Upload ID Back</span>
                            <input type="file" id="kyc_id_back" name="kyc_id_back" accept="image/jpeg,image/png,image/jpg,application/pdf">
                            <small>Max 5MB • JPG, PNG, PDF</small>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="kyc_license_front">License Front</label>
                        <div class="file-upload">
                            <span>Upload License Front</span>
                            <input type="file" id="kyc_license_front" name="kyc_license_front" accept="image/jpeg,image/png,image/jpg,application/pdf">
                            <small>Max 5MB • JPG, PNG, PDF</small>
                        </div>
                    </div>
                     <div class="form-group">
                        <label for="kyc_license_back">License Back</label>
                        <div class="file-upload">
                            <span>Upload License Back</span>
                            <input type="file" id="kyc_license_back" name="kyc_license_back" accept="image/jpeg,image/png,image/jpg,application/pdf">
                            <small>Max 5MB • JPG, PNG, PDF</small>
                        </div>
                    </div>
                     <div class="form-group">
                        <label for="kyc_address_proof">Address Proof</label>
                        <div class="file-upload">
                            <span>Upload Address Proof</span>
                            <input type="file" id="kyc_address_proof" name="kyc_address_proof" accept="image/jpeg,image/png,image/jpg,application/pdf">
                            <small>Max 5MB • JPG, PNG, PDF</small>
                        </div>
                    </div>
                     <div class="form-group">
                        <label for="kyc_passport">Passport</label>
                        <div class="file-upload">
                            <span>Upload Passport</span>
                            <input type="file" id="kyc_passport" name="kyc_passport" accept="image/jpeg,image/png,image/jpg,application/pdf">
                            <small>Max 5MB • JPG, PNG, PDF</small>
                        </div>
                    </div>
                </div>

                <div class="form-group" style="grid-column: 1 / -1; margin-top: 20px;">
                    <button type="submit" class="btn">Update Profile</button>
                </div>
            </form>
            <div id="profileUpdateResponse" class="response"></div>
        </div>
    </div>

    <script>
        let token = '';
        const API_URL = 'http://localhost:8000'; // Update with your API URL

        // DOM Elements
        const loginForm = document.getElementById('loginForm');
        const loginResponse = document.getElementById('loginResponse');
        const profileDisplaySection = document.getElementById('profileDisplaySection');
        const profileDetails = document.getElementById('profileDetails');
        const profileUpdateSection = document.getElementById('profileUpdateSection');
        const profileUpdateForm = document.getElementById('profileUpdateForm');
        const profileUpdateResponse = document.getElementById('profileUpdateResponse');

        // Login Handler
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const loginBtn = e.submitter;
            const originalText = loginBtn.textContent;
            
            try {
                loginBtn.textContent = 'Signing in...';
                loginBtn.disabled = true;
                loginResponse.innerHTML = '';
                
                const response = await fetch(`${API_URL}/token`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
                    body: JSON.stringify({
                        email: document.getElementById('email').value,
                        password: document.getElementById('password').value,
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.token) {
                    token = data.token;
                    loginResponse.innerHTML = 'Login successful!';
                    loginResponse.className = 'response success';
                    
                    profileDisplaySection.style.display = 'block';
                    profileUpdateSection.style.display = 'block';
                    fetchAgentProfile();
                } else {
                    throw new Error(data.message || 'Login failed. Please check your credentials.');
                }
            } catch (error) {
                loginResponse.innerHTML = error.message;
                loginResponse.className = 'response error';
            } finally {
                loginBtn.textContent = originalText;
                loginBtn.disabled = false;
            }
        });

        // Fetch Agent Profile
        async function fetchAgentProfile() {
            if (!token) return;
            profileDetails.innerHTML = '<p>Loading profile...</p>';
            try {
                const response = await fetch(`${API_URL}/agent/profile`, {
                    headers: { 'Authorization': `Bearer ${token}`, 'Accept': 'application/json' }
                });
                const responseData = await response.json();

                if (!response.ok) throw new Error(responseData.message || 'Failed to fetch profile');
                
                displayAgentProfile(responseData.data);
                populateProfileForm(responseData.data);

            } catch (error) {
                profileDetails.innerHTML = `<p class="error">Error loading profile: ${error.message}</p>`;
            }
        }

        // Display Agent Profile
        function displayAgentProfile(profile) {
            let html = '';
            if (profile.user) {
                html += `<p><strong>Name:</strong> ${profile.user.name || 'N/A'}</p>`;
                html += `<p><strong>Email:</strong> ${profile.user.email || 'N/A'}</p>`;
            }
            if (profile.profile_photo_url) {
                html += `<img src="${profile.profile_photo_url}" alt="Profile Photo"><br>`;
            } else {
                html += `<p><strong>Profile Photo:</strong> Not set</p>`;
            }
            html += `<p><strong>Phone:</strong> ${profile.phone_number || 'N/A'}</p>`;
            html += `<p><strong>Availability:</strong> ${profile.availability_status || 'N/A'}</p>`;
            html += `<p><strong>KYC Status:</strong> ${profile.kyc_status || 'N/A'}</p>`;
            if (profile.kyc_rejection_reason) {
                html += `<p><strong>KYC Rejection Reason:</strong> ${profile.kyc_rejection_reason}</p>`;
            }
            html += `<p><strong>Uses Wallet for Earnings:</strong> ${profile.use_wallet_for_earnings ? 'Yes' : 'No'}</p>`;
            
            if(profile.current_location && profile.current_location.lat && profile.current_location.lng) {
                html += `<p><strong>Current Location:</strong> Lat: ${profile.current_location.lat}, Lng: ${profile.current_location.lng}</p>`;
            } else {
                html += `<p><strong>Current Location:</strong> Not set</p>`;
            }

            html += `<h3>KYC Documents:</h3>`;
            if (profile.kyc_media) {
                for (const key in profile.kyc_media) {
                    if (profile.kyc_media[key]) {
                        html += `<p><strong>${formatKycKey(key)}:</strong> <a href="${profile.kyc_media[key]}" target="_blank" class="kyc-doc-link">View Document</a></p>`;
                    } else {
                        html += `<p><strong>${formatKycKey(key)}:</strong> Not uploaded</p>`;
                    }
                }
            } else {
                html += `<p>No KYC documents information available.</p>`;
            }

            profileDetails.innerHTML = html;
        }

        function formatKycKey(key) {
            return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }

        // Populate Profile Update Form
        function populateProfileForm(profile) {
            if (profile.user && profile.user.name) {
                document.getElementById('name').value = profile.user.name;
            }
            document.getElementById('phone_number').value = profile.phone_number || '';
            document.getElementById('use_wallet_for_earnings').checked = profile.use_wallet_for_earnings || false;

            if (profile.current_location) {
                document.getElementById('current_location_lat').value = profile.current_location.lat || '';
                document.getElementById('current_location_lng').value = profile.current_location.lng || '';
            } else {
                 document.getElementById('current_location_lat').value = '';
                 document.getElementById('current_location_lng').value = '';
            }
        }

        // Profile Update Handler
        profileUpdateForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            if (!token) {
                alert('Please login first');
                return;
            }

            const submitBtn = e.submitter;
            const originalText = submitBtn.textContent;
            
            try {
                submitBtn.textContent = 'Updating...';
                submitBtn.disabled = true;
                profileUpdateResponse.innerHTML = '';

                const formData = new FormData(profileUpdateForm);
                formData.append('_method', 'PUT'); // Laravel needs this for PUT requests with FormData

                // Handle checkbox: FormData doesn't include unchecked checkboxes.
                // If 'use_wallet_for_earnings' is not checked, it won't be in formData.
                // We need to ensure it's sent as 0 if unchecked.
                // The 'nullable|boolean' validation handles '1', '0', true, false.
                if (!formData.has('use_wallet_for_earnings')) {
                    formData.append('use_wallet_for_earnings', '0');
                } else {
                    formData.set('use_wallet_for_earnings', '1'); // Ensure it's '1' if checked
                }

                // Remove empty file inputs to avoid validation errors for 'nullable|file'
                // if the user doesn't want to update a specific file.
                const fileInputNames = ['profile_photo', 'kyc_id_front', 'kyc_id_back', 'kyc_license_front', 'kyc_license_back', 'kyc_address_proof', 'kyc_passport'];
                fileInputNames.forEach(name => {
                    const fileInput = document.getElementById(name);
                    if (fileInput && fileInput.files.length === 0) {
                        formData.delete(name);
                    }
                });

                // If lat/lng are empty, don't send them or send them such that backend handles them as null
                // For 'nullable|array' for current_location, if lat/lng are empty, they might not pass 'numeric'
                // It's often better to only send them if they have values.
                // Or ensure backend handles empty strings for lat/lng appropriately if they are part of the array.
                if (formData.get('current_location[lat]') === '' && formData.get('current_location[lng]') === '') {
                    formData.delete('current_location[lat]');
                    formData.delete('current_location[lng]');
                     // If your backend expects current_location to be null if not provided,
                    // and not an array with empty strings, you might need to remove it entirely
                    // if both are empty. Or ensure your request validation handles empty strings for lat/lng.
                } else if (formData.get('current_location[lat]') === '') {
                     formData.delete('current_location[lat]'); // Or handle as error
                } else if (formData.get('current_location[lng]') === '') {
                     formData.delete('current_location[lng]'); // Or handle as error
                }


                const response = await fetch(`${API_URL}/agent/profile`, {
                    method: 'POST', // Using POST because of FormData and _method
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json',
                        // 'Content-Type': 'multipart/form-data' is set automatically by browser for FormData
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok && data.data) {
                    profileUpdateResponse.innerHTML = '✓ Profile updated successfully!';
                    profileUpdateResponse.className = 'response success';
                    fetchAgentProfile(); // Refresh displayed profile
                } else {
                    let errorMessage = data.message || 'Profile update failed.';
                    if (data.errors) {
                        errorMessage += '<ul>';
                        for (const key in data.errors) {
                            errorMessage += `<li>${data.errors[key].join(', ')}</li>`;
                        }
                        errorMessage += '</ul>';
                    }
                    throw new Error(errorMessage);
                }
            } catch (error) {
                profileUpdateResponse.innerHTML = `✗ ${error.message}`;
                profileUpdateResponse.className = 'response error';
            } finally {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });

    </script>
</body>
</html>