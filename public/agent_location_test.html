<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Location API Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom styles for specific elements not easily covered by Tailwind */
        .response {
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body class="p-5 bg-gray-50">
    <div class="max-w-6xl mx-auto">
        <h1 class="mb-6 text-3xl font-bold text-center">Agent Location API Test</h1>

        <!-- Login Section -->
        <div id="loginSection" class="max-w-md mx-auto bg-white shadow-md rounded-lg p-6 mb-5">
            <div class="p-6">
                <h5 class="text-2xl font-semibold mb-4">Login</h5>
                <div id="loginError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative hidden" role="alert"></div>
                <form id="loginForm">
                    <div class="mb-4">
                        <label for="email" class="block text-gray-700 text-sm font-bold mb-2">Email address</label>
                        <input type="email" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="email" name="email" value="<EMAIL>" required>
                    </div>
                    <div class="mb-4">
                        <label for="password" class="block text-gray-700 text-sm font-bold mb-2">Password</label>
                        <input type="password" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="password" name="password" value="admin" required>
                    </div>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">Login</button>
                </form>
            </div>
        </div>

        <!-- Agent Location Management Section (hidden until logged in) -->
        <div id="agentLocationSection" style="display: none;">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-2xl font-semibold">Welcome, <span id="userName">User</span></h3>
                <button id="logoutBtn" class="bg-transparent hover:bg-red-500 text-red-700 font-semibold hover:text-white py-2 px-4 border border-red-500 hover:border-transparent rounded">Logout</button>
            </div>

            <!-- Get Current Location Section -->
            <div class="mb-5 p-4 bg-gray-100 rounded-lg">
                <h2 class="text-xl font-semibold mb-3">Get Current Location</h2>
                <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mr-2" onclick="getCurrentLocation()">GET /agent/location</button>
                <div id="getLocationResponse" class="response bg-gray-100 border border-gray-300 rounded p-3 mt-3" style="display: none;"></div>
            </div>

            <!-- Update Location Section -->
            <div class="mb-5 p-4 bg-gray-100 rounded-lg">
                <h2 class="text-xl font-semibold mb-3">Update Location</h2>
                <div class="mb-4">
                    <label for="latitude" class="block text-gray-700 text-sm font-bold mb-2">Latitude:</label>
                    <input type="number" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="latitude" value="40.7128" step="any" placeholder="40.7128">
                </div>
                <div class="mb-4">
                    <label for="longitude" class="block text-gray-700 text-sm font-bold mb-2">Longitude:</label>
                    <input type="number" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="longitude" value="-74.0060" step="any" placeholder="-74.0060">
                </div>
                <div class="mb-4">
                    <label for="accuracy" class="block text-gray-700 text-sm font-bold mb-2">Accuracy (meters, optional):</label>
                    <input type="number" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="accuracy" value="5.0" step="any" placeholder="5.0">
                </div>
                <div class="mb-4">
                    <label for="speed" class="block text-gray-700 text-sm font-bold mb-2">Speed (km/h, optional):</label>
                    <input type="number" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="speed" value="25.5" step="any" placeholder="25.5">
                </div>
                <div class="mb-4">
                    <label for="heading" class="block text-gray-700 text-sm font-bold mb-2">Heading (degrees, optional):</label>
                    <input type="number" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="heading" value="180" step="any" placeholder="180">
                </div>
                <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mr-2" onclick="updateLocation()">POST /agent/location</button>
                <button class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline" onclick="getRandomLocation()">Use Random Location</button>
                <div id="updateLocationResponse" class="response bg-gray-100 border border-gray-300 rounded p-3 mt-3" style="display: none;"></div>
            </div>

            <!-- Test Invalid Data Section -->
            <div class="mb-5 p-4 bg-gray-100 rounded-lg">
                <h2 class="text-xl font-semibold mb-3">Test Invalid Data</h2>
                <button class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mr-2" onclick="testInvalidLatitude()">Test Invalid Latitude (91)</button>
                <button class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mr-2" onclick="testInvalidLongitude()">Test Invalid Longitude (181)</button>
                <button class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline" onclick="testMissingData()">Test Missing Required Data</button>
                <div id="invalidDataResponse" class="response bg-gray-100 border border-gray-300 rounded p-3 mt-3" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let authToken = null;
        let currentUser = null;
        const base_url = 'http://localhost:8000'; // Set to your API base URL, e.g., 'http://localhost:8000/api' or just '' if same origin

        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();

            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                login();
            });

            document.getElementById('logoutBtn').addEventListener('click', function() {
                logout();
            });
        });

        function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            const user = JSON.parse(localStorage.getItem('user'));

            if (token && user) {
                authToken = token;
                currentUser = user;
                document.getElementById('userName').textContent = user.name || user.email;
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('agentLocationSection').style.display = 'block';
                getRandomLocation(); // Populate with random location on login
            } else {
                document.getElementById('loginSection').style.display = 'block';
                document.getElementById('agentLocationSection').style.display = 'none';
            }
        }

        function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginError = document.getElementById('loginError');
            loginError.classList.add('d-none');

            const submitBtn = document.querySelector('#loginForm button[type="submit"]');
            const originalBtnText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="animate-spin inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2" role="status" aria-hidden="true"></span> Logging in...';

            fetch(`${base_url}/token`, { // Replace with your actual login endpoint if different
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
                body: JSON.stringify({ email: email, password: password })
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(errData => {
                        throw new Error(errData.message || 'Login failed. Status: ' + response.status);
                    }).catch(() => {
                        throw new Error('Login failed. Status: ' + response.status);
                    });
                }
                return response.json();
            })
            .then(data => {
                authToken = data.token || data.access_token; // Adjust based on your API
                currentUser = data.user || {name: data.name || data.email, email: data.email}; // Adjust based on your API
                if (!authToken) throw new Error('Token not found in login response.');

                localStorage.setItem('authToken', authToken);
                localStorage.setItem('user', JSON.stringify(currentUser));
                checkAuthStatus();
            })
            .catch(error => {
                console.error('Login error:', error);
                loginError.textContent = error.message || 'Invalid email or password.';
                loginError.classList.remove('d-none');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = originalBtnText;
            });
        }

        function logout() {
            authToken = null;
            currentUser = null;
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('agentLocationSection').style.display = 'none';
            document.getElementById('loginForm').reset();
            document.getElementById('loginError').classList.add('d-none');
        }

        function getHeaders() {
            if (!authToken) {
                console.error('Authentication token is missing. Please log in.');
                return {};
            }
            return {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${authToken}`
            };
        }


        function showResponse(elementId, response, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response ${isError ? 'bg-red-100 border-red-400 text-red-700' : 'bg-green-100 border-green-400 text-green-700'} border rounded p-3 mt-3`;
            element.textContent = JSON.stringify(response, null, 2);
        }

        async function testAuth() {
            try {
                const response = await fetch(`${base_url}/me`, {
                    method: 'GET',
                    headers: getHeaders()
                });
                const data = await response.json();
                showResponse('authResponse', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, !response.ok);
            } catch (error) {
                showResponse('authResponse', { error: error.message }, true);
            }
        }

        async function getCurrentLocation() {
            try {
                const response = await fetch(`${base_url}/agent/location`, {
                    method: 'GET',
                    headers: getHeaders()
                });
                const data = await response.json();
                showResponse('getLocationResponse', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, !response.ok);
            } catch (error) {
                showResponse('getLocationResponse', { error: error.message }, true);
            }
        }

        async function updateLocation() {
            const locationData = {
                lat: parseFloat(document.getElementById('latitude').value),
                lng: parseFloat(document.getElementById('longitude').value)
            };

            const accuracy = document.getElementById('accuracy').value;
            const speed = document.getElementById('speed').value;
            const heading = document.getElementById('heading').value;

            if (accuracy) locationData.accuracy = parseFloat(accuracy);
            if (speed) locationData.speed = parseFloat(speed);
            if (heading) locationData.heading = parseFloat(heading);

            try {
                const response = await fetch(`${base_url}/agent/location`, {
                    method: 'POST',
                    headers: getHeaders(),
                    body: JSON.stringify(locationData)
                });
                const data = await response.json();
                showResponse('updateLocationResponse', {
                    status: response.status,
                    statusText: response.statusText,
                    request: locationData,
                    data: data
                }, !response.ok);
            } catch (error) {
                showResponse('updateLocationResponse', { error: error.message }, true);
            }
        }

        function getRandomLocation() {
            // Generate random coordinates around New York City
            const baseLat = 40.7128;
            const baseLng = -74.0060;
            const randomLat = baseLat + (Math.random() - 0.5) * 0.1; // ±0.05 degrees
            const randomLng = baseLng + (Math.random() - 0.5) * 0.1; // ±0.05 degrees

            document.getElementById('latitude').value = randomLat.toFixed(6);
            document.getElementById('longitude').value = randomLng.toFixed(6);
            document.getElementById('accuracy').value = (Math.random() * 10 + 1).toFixed(1);
            document.getElementById('speed').value = (Math.random() * 50).toFixed(1);
            document.getElementById('heading').value = Math.floor(Math.random() * 360);
        }

        async function testInvalidLatitude() {
            const invalidData = {
                lat: 91, // Invalid latitude
                lng: -74.0060
            };

            try {
                const response = await fetch(`${base_url}/agent/location`, {
                    method: 'POST',
                    headers: getHeaders(),
                    body: JSON.stringify(invalidData)
                });
                const data = await response.json();
                showResponse('invalidDataResponse', {
                    status: response.status,
                    statusText: response.statusText,
                    request: invalidData,
                    data: data
                }, !response.ok);
            } catch (error) {
                showResponse('invalidDataResponse', { error: error.message }, true);
            }
        }

        async function testInvalidLongitude() {
            const invalidData = {
                lat: 40.7128,
                lng: 181 // Invalid longitude
            };

            try {
                const response = await fetch(`${base_url}/agent/location`, {
                    method: 'POST',
                    headers: getHeaders(),
                    body: JSON.stringify(invalidData)
                });
                const data = await response.json();
                showResponse('invalidDataResponse', {
                    status: response.status,
                    statusText: response.statusText,
                    request: invalidData,
                    data: data
                }, !response.ok);
            } catch (error) {
                showResponse('invalidDataResponse', { error: error.message }, true);
            }
        }

        async function testMissingData() {
            const invalidData = {
                // Missing required lat and lng
                accuracy: 5.0
            };

            try {
                const response = await fetch(`${base_url}/agent/location`, {
                    method: 'POST',
                    headers: getHeaders(),
                    body: JSON.stringify(invalidData)
                });
                const data = await response.json();
                showResponse('invalidDataResponse', {
                    status: response.status,
                    statusText: response.statusText,
                    request: invalidData,
                    data: data
                }, !response.ok);
            } catch (error) {
                showResponse('invalidDataResponse', { error: error.message }, true);
            }
        }

        // Auto-populate with sample data on load
        window.onload = function() {
            getRandomLocation();
        };
    </script>
</body>
</html>
