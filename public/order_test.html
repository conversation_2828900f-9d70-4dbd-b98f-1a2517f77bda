<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Management Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
        }
        .container {
            max-width: 1200px;
        }
        .card {
            margin-bottom: 20px;
        }
        .filter-section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .order-item {
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .badge-pending { background-color: #ffc107; color: #000; }
        .badge-processing, .badge-at-local-facility, .badge-out-for-delivery { background-color: #17a2b8; }
        .badge-completed, .badge-success { background-color: #28a745; }
        .badge-cancelled, .badge-failed { background-color: #dc3545; }
        .badge-refunded { background-color: #6c757d; }
        .badge-cash-on-delivery { background-color: #0dcaf0; color: #000;}
        #loginSection {
            max-width: 500px;
            margin: 0 auto;
        }
        #orderManagementSection {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4 text-center">Order Management</h1>

        <!-- Login Section -->
        <div id="loginSection" class="card">
            <div class="card-body">
                <h5 class="card-title">Login</h5>
                <div id="loginError" class="alert alert-danger d-none" role="alert"></div>
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email address</label>
                        <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" value="admin" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Login</button>
                </form>
            </div>
        </div>

        <!-- Order Management Section (hidden until logged in) -->
        <div id="orderManagementSection">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3>Welcome, <span id="userName">User</span></h3>
                <button id="logoutBtn" class="btn btn-outline-danger">Logout</button>
            </div>

            <div class="card filter-section">
                <div class="card-body">
                    <h5 class="card-title">Filter Orders</h5>
                    <form id="orderFilterForm" class="row g-3">
                        <div class="col-md-4">
                            <label for="status_filter_select" class="form-label">Order Status / Context</label>
                            <select class="form-select" id="status_filter_select" name="status_filter_select">
                                <option value="">All Orders</option>
                                <option value="pending_assignment_context">Pending Delivery Assignment</option>
                                <option value="pending">Pending (Generic)</option>
                                <option value="processing">Processing (Generic)</option>
                                <option value="at-local-facility">At Local Facility (Generic)</option>
                                <option value="out-for-delivery">Out for Delivery (Generic)</option>
                                <option value="ongoing">Ongoing (Processing, At Local Facility, Out for Delivery)</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                                <option value="refunded">Refunded</option>
                                <option value="failed">Failed</option>
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label for="payment_status_filter" class="form-label">Payment Status</label>
                            <select class="form-select" id="payment_status_filter" name="payment_status">
                                <option value="">All Payment Statuses</option>
                                <option value="payment-pending">Pending</option>
                                <option value="payment-processing">Processing</option>
                                <option value="payment-success">Success</option>
                                <option value="payment-failed">Failed</option>
                                <option value="payment-refunded">Refunded</option>
                                <option value="cash-on-delivery">Cash on Delivery</option>
                                <!-- Add other payment statuses your backend supports -->
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label for="sort_by_filter" class="form-label">Sort By</label>
                            <select class="form-select" id="sort_by_filter" name="sort_by">
                                <option value="created_at">Date Created</option>
                                <option value="updated_at">Date Updated</option>
                                <option value="total">Total Amount</option>
                                <!-- Add other sortable fields your backend supports -->
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label for="sort_order_filter" class="form-label">Sort Order</label>
                            <select class="form-select" id="sort_order_filter" name="sort_order">
                                <option value="desc">Descending</option>
                                <option value="asc">Ascending</option>
                            </select>
                        </div>

                        <div class="col-md-4">
                            <label for="date_from_filter" class="form-label">Date From</label>
                            <input type="date" class="form-control" id="date_from_filter" name="date_from">
                        </div>
                        <div class="col-md-4">
                            <label for="date_to_filter" class="form-label">Date To</label>
                            <input type="date" class="form-control" id="date_to_filter" name="date_to">
                        </div>


                        <div class="col-md-4">
                            <label for="limit_filter" class="form-label">Results Per Page</label>
                            <select class="form-select" id="limit_filter" name="limit">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>

                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">Apply Filters</button>
                            <button type="button" class="btn btn-secondary" id="resetFilters">Reset Filters</button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Orders</h5>
                    <div id="ordersList" class="mt-3">
                        <!-- Orders will be loaded here -->
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Loading orders...</p>
                        </div>
                    </div>

                    <nav aria-label="Orders pagination" class="mt-4">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- Pagination will be loaded here -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let authToken = null;
        let currentUser = null;
        const API_BASE_URL = ''; // Set to your API base URL, e.g., 'http://localhost:8000/api' or just '' if same origin

        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();

            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                login();
            });

            document.getElementById('logoutBtn').addEventListener('click', function() {
                logout();
            });

            document.getElementById('orderFilterForm').addEventListener('submit', function(e) {
                e.preventDefault();
                loadOrders(1); // Load from page 1 when filters change
            });

            document.getElementById('resetFilters').addEventListener('click', function() {
                document.getElementById('orderFilterForm').reset();
                loadOrders(1); // Load from page 1 with default filters
            });
        });

        function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            const user = JSON.parse(localStorage.getItem('user'));

            if (token && user) {
                authToken = token;
                currentUser = user;
                document.getElementById('userName').textContent = user.name || user.email;
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('orderManagementSection').style.display = 'block';
                loadOrders(1); // Load initial orders
            } else {
                document.getElementById('loginSection').style.display = 'block';
                document.getElementById('orderManagementSection').style.display = 'none';
            }
        }

        function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginError = document.getElementById('loginError');
            loginError.classList.add('d-none');

            const submitBtn = document.querySelector('#loginForm button[type="submit"]');
            const originalBtnText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Logging in...';

            fetch(`${API_BASE_URL}/token`, { // Replace with your actual login endpoint if different
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
                body: JSON.stringify({ email: email, password: password })
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(errData => {
                        throw new Error(errData.message || 'Login failed. Status: ' + response.status);
                    }).catch(() => {
                        throw new Error('Login failed. Status: ' + response.status);
                    });
                }
                return response.json();
            })
            .then(data => {
                authToken = data.token || data.access_token; // Adjust based on your API
                currentUser = data.user || {name: data.name || data.email, email: data.email}; // Adjust based on your API
                if (!authToken) throw new Error('Token not found in login response.');

                localStorage.setItem('authToken', authToken);
                localStorage.setItem('user', JSON.stringify(currentUser));
                checkAuthStatus();
            })
            .catch(error => {
                console.error('Login error:', error);
                loginError.textContent = error.message || 'Invalid email or password.';
                loginError.classList.remove('d-none');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = originalBtnText;
            });
        }

        function logout() {
            authToken = null;
            currentUser = null;
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('orderManagementSection').style.display = 'none';
            document.getElementById('ordersList').innerHTML = '';
            document.getElementById('pagination').innerHTML = '';
            document.getElementById('loginForm').reset();
            document.getElementById('loginError').classList.add('d-none');
        }

        function loadOrders(page = 1) {
            if (!authToken) {
                console.error('User not authenticated.');
                return;
            }

            const form = document.getElementById('orderFilterForm');
            // We'll build params manually to have more control
            const params = new URLSearchParams();
            params.append('page', page);

            const statusFilterValue = document.getElementById('status_filter_select').value;
            const paymentStatusValue = document.getElementById('payment_status_filter').value;
            const sortByValue = document.getElementById('sort_by_filter').value;
            const sortOrderValue = document.getElementById('sort_order_filter').value;
            const dateFromValue = document.getElementById('date_from_filter').value;
            const dateToValue = document.getElementById('date_to_filter').value;
            const limitValue = document.getElementById('limit_filter').value;


            if (statusFilterValue === 'pending_assignment_context') {
                params.append('status_context', 'pending_assignment');
                // For pending_assignment_context, we don't send generic 'status' or 'payment_status'
                // from the form, as the backend derives them.
                // We only append other relevant filters.
                if (sortByValue) params.append('sort_by', sortByValue);
                if (sortOrderValue) params.append('sort_order', sortOrderValue);
                if (dateFromValue) params.append('date_from', dateFromValue);
                if (dateToValue) params.append('date_to', dateToValue);
                if (limitValue) params.append('limit', limitValue);
            } else {
                // For all other generic filters
                if (statusFilterValue) { // This is the generic status like 'pending', 'completed'
                    params.append('status', statusFilterValue);
                }
                if (paymentStatusValue) {
                    params.append('payment_status', paymentStatusValue);
                }
                if (sortByValue) params.append('sort_by', sortByValue);
                if (sortOrderValue) params.append('sort_order', sortOrderValue);
                if (dateFromValue) params.append('date_from', dateFromValue);
                if (dateToValue) params.append('date_to', dateToValue);
                if (limitValue) params.append('limit', limitValue);
            }

            document.getElementById('ordersList').innerHTML = `
                <div class="text-center">
                    <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
                    <p>Loading orders...</p>
                </div>`;
            document.getElementById('pagination').innerHTML = '';

            fetch(`${API_BASE_URL}/orders?${params.toString()}`, { // Ensure this is your correct /orders endpoint
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                }
            })
            .then(response => {
                if (response.status === 401) {
                    logout();
                    throw new Error('Authentication failed. Please log in again.');
                }
                if (!response.ok) {
                    return response.json().then(errData => {
                         throw new Error(errData.message || 'Failed to load orders. Status: ' + response.status);
                    }).catch(() => {
                        throw new Error('Failed to load orders. Status: ' + response.status);
                    });
                }
                return response.json();
            })
            .then(data => {
                displayOrders(data);
            })
            .catch(error => {
                console.error('Error fetching orders:', error);
                const errorDisplay = error.message.includes('Authentication failed') ?
                    `<div class="alert alert-warning" role="alert">Your session has expired. Please log in again.</div>` :
                    `<div class="alert alert-danger" role="alert">${error.message || 'Error loading orders.'}</div>`;
                document.getElementById('ordersList').innerHTML = errorDisplay;
            });
        }

        function displayOrders(data) {
            const ordersContainer = document.getElementById('ordersList');
            const paginationContainer = document.getElementById('pagination');

            if (!data || !data.data || data.data.length === 0) {
                ordersContainer.innerHTML = `<div class="alert alert-info" role="alert">No orders found matching your criteria.</div>`;
                paginationContainer.innerHTML = '';
                return;
            }

            let ordersHtml = '';
            data.data.forEach(order => {
                const statusClass = getStatusClass(order.order_status);
                const paymentStatusClass = getStatusClass(order.payment_status);

                ordersHtml += `
                    <div class="order-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5>Order #${order.tracking_number || order.id || 'N/A'}</h5>
                            <span class="badge ${statusClass}">${formatStatus(order.order_status)}</span>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-4">
                                <p><strong>Customer:</strong> ${order.customer_name || 'Guest'}</p>
                                <p><strong>Date:</strong> ${new Date(order.created_at).toLocaleString()}</p>
                                ${order.requires_delivery ? '<p class="text-info">Requires Delivery</p>' : ''}
                                ${order.delivery_id ? `<p class="text-success">Delivery ID: ${order.delivery_id}</p>` : (order.requires_delivery ? '<p class="text-warning">Delivery Not Assigned</p>' : '')}
                            </div>
                            <div class="col-md-4">
                                <p><strong>Payment Status:</strong> <span class="badge ${paymentStatusClass}">${formatStatus(order.payment_status)}</span></p>
                                <p><strong>Payment Method:</strong> ${formatPaymentMethod(order.payment_gateway)}</p>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <p><strong>Total:</strong> $${parseFloat(order.paid_total || order.total || 0).toFixed(2)}</p>
                                <a href="#" class="btn btn-sm btn-primary" onclick="event.preventDefault(); alert('View details for order ${order.tracking_number || order.id}');">View Details</a>
                            </div>
                        </div>
                    </div>`;
            });
            ordersContainer.innerHTML = ordersHtml;
            renderPagination(data);
        }

        function renderPagination(data) {
            const paginationContainer = document.getElementById('pagination');
            if (!data.meta || !data.meta.links || data.meta.total === 0 || data.meta.last_page <= 1) {
                paginationContainer.innerHTML = '';
                return;
            }
            let paginationHtml = '';
            data.meta.links.forEach(link => {
                const label = link.label.replace(/«/g, '«').replace(/»/g, '»'); // Use HTML entities
                if (link.url === null) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">${label}</span></li>`;
                } else {
                    paginationHtml += `
                        <li class="page-item ${link.active ? 'active' : ''}">
                            <a class="page-link" href="#" onclick="loadOrders(${getPageNumber(link.url)}); return false;">${label}</a>
                        </li>`;
                }
            });
            paginationContainer.innerHTML = paginationHtml;
        }

        function getPageNumber(url) {
            if (!url) return 1;
            try {
                // Ensure a base URL is provided if the link.url is relative
                const fullUrl = new URL(url, API_BASE_URL || window.location.origin);
                return fullUrl.searchParams.get('page') || 1;
            } catch (e) {
                const match = url.match(/[?&]page=(\d+)/);
                return match ? parseInt(match[1], 10) : 1;
            }
        }

        function getStatusClass(status) {
            if (!status) return 'bg-secondary';
            status = status.toLowerCase().replace(/[-_]/g, ''); // Normalize status
            if (status.includes('pending')) return 'badge-pending';
            if (status.includes('processing') || status.includes('atlocalfacility') || status.includes('outfordelivery')) return 'badge-processing';
            if (status.includes('completed') || status.includes('success')) return 'badge-completed';
            if (status.includes('cancelled') || status.includes('failed')) return 'badge-cancelled';
            if (status.includes('refunded')) return 'badge-refunded';
            if (status.includes('cashondelivery')) return 'badge-cash-on-delivery';
            return 'bg-secondary';
        }

        function formatStatus(status) {
            if (!status) return 'N/A';
            return status
                .replace('order-', '')
                .replace('payment-', '')
                .split(/[-_]/)
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ');
        }

        function formatPaymentMethod(method) {
            if (!method) return 'N/A';
            return method.split(/[-_]/).map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
        }
    </script>
</body>
</html>