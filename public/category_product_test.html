<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Endpoint Testing</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #6c757d;
            color: white;
            font-weight: bold;
        }
        .result-container {
            max-height: 400px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            padding: 10px;
        }
        pre {
            white-space: pre-wrap;
        }
        .nav-tabs .nav-link.active {
            font-weight: bold;
            background-color: #e9ecef;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .btn-primary {
            background-color: #007bff;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .category-item {
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            margin-bottom: 5px;
        }
        .category-item:hover {
            background-color: #e9ecef;
        }
        .category-item.active {
            background-color: #007bff;
            color: white;
        }
        #categoryBreadcrumb {
            margin-bottom: 15px;
        }
        .breadcrumb-item {
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4 text-center">API Endpoint Testing</h1>

        <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="categories-tab" data-bs-toggle="tab" data-bs-target="#categories" type="button" role="tab">Categories</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="products-tab" data-bs-toggle="tab" data-bs-target="#products" type="button" role="tab">Product Filtering</button>
            </li>
        </ul>

        <div class="tab-content" id="myTabContent">
            <!-- Categories Tab -->
            <div class="tab-pane fade show active" id="categories" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">Category Navigation</div>
                            <div class="card-body">
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb" id="categoryBreadcrumb">
                                        <li class="breadcrumb-item active" aria-current="page">Home</li>
                                    </ol>
                                </nav>

                                <div id="categoriesList" class="mb-3"></div>

                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-primary" id="loadParentCategories">Load Parent Categories</button>
                                    <button class="btn btn-secondary" id="loadFeaturedCategories">Featured Categories</button>
                                </div>

                                <div class="mt-3">
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-outline-secondary" id="loadRecommendedCategories">Recommended</button>
                                        <button class="btn btn-outline-secondary" id="loadTrendingCategories">Trending</button>
                                        <button class="btn btn-outline-secondary" id="loadPopularCategories">Popular</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">Category Results</div>
                            <div class="card-body">
                                <div class="result-container">
                                    <pre id="categoryResult">Results will appear here...</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Tab -->
            <div class="tab-pane fade" id="products" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">Product Filtering</div>
                            <div class="card-body">
                                <form id="filterForm">
                                    <div class="form-group">
                                        <label for="category">Category ID:</label>
                                        <input type="text" class="form-control" id="category" placeholder="Enter category ID">
                                    </div>

                                    <div class="form-group">
                                        <label for="brand">Brand/Manufacturer ID:</label>
                                        <input type="text" class="form-control" id="brand" placeholder="Enter manufacturer ID">
                                        <small class="form-text text-muted">This corresponds to the manufacturer_id field in the database</small>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="minPrice">Min Price:</label>
                                                <input type="number" class="form-control" id="minPrice" placeholder="Min price">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="maxPrice">Max Price:</label>
                                                <input type="number" class="form-control" id="maxPrice" placeholder="Max price">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="search">Search Term:</label>
                                        <input type="text" class="form-control" id="search" placeholder="Enter search term">
                                    </div>

                                    <div class="form-group">
                                        <label for="sortBy">Sort By:</label>
                                        <select class="form-control" id="sortBy">
                                            <option value="relevance">Relevance</option>
                                            <option value="popularity">Popularity</option>
                                            <option value="price_low_high">Price: Low to High</option>
                                            <option value="price_high_low">Price: High to Low</option>
                                            <option value="newest">Newest First</option>
                                            <option value="rating">Rating (Average Review Score)</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="limit">Limit:</label>
                                        <input type="number" class="form-control" id="limit" value="10">
                                    </div>

                                    <button type="submit" class="btn btn-primary">Filter Products</button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">Product Results</div>
                            <div class="card-body">
                                <div class="result-container">
                                    <pre id="productResult">Results will appear here...</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Base API URL - Change this to your API URL
        const API_BASE_URL = 'http://localhost:8000';

        // Current category navigation state
        let currentParentId = null;
        let breadcrumbPath = [];

        // DOM Elements
        const categoryResult = document.getElementById('categoryResult');
        const productResult = document.getElementById('productResult');
        const categoriesList = document.getElementById('categoriesList');
        const categoryBreadcrumb = document.getElementById('categoryBreadcrumb');

        // Helper function to make API requests
        async function fetchAPI(endpoint, params = {}) {
            const queryString = Object.keys(params)
                .filter(key => params[key] !== null && params[key] !== undefined && params[key] !== '')
                .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
                .join('&');

            const url = `${API_BASE_URL}/${endpoint}${queryString ? `?${queryString}` : ''}`;

            try {
                const response = await fetch(url);
                return await response.json();
            } catch (error) {
                console.error('API Error:', error);
                return { error: error.message };
            }
        }

        // Display results in the appropriate container
        function displayResult(container, data) {
            container.textContent = JSON.stringify(data, null, 2);
        }

        // Update categories list
        function updateCategoriesList(categories) {
            categoriesList.innerHTML = '';

            if (categories.length === 0) {
                categoriesList.innerHTML = '<p>No categories found</p>';
                return;
            }

            categories.forEach(category => {
                const item = document.createElement('div');
                item.className = 'category-item';
                item.textContent = category.name;
                item.dataset.id = category.id;
                item.dataset.name = category.name;

                item.addEventListener('click', () => {
                    loadChildCategories(category.id, category.name);
                });

                categoriesList.appendChild(item);
            });
        }

        // Update breadcrumb
        function updateBreadcrumb() {
            categoryBreadcrumb.innerHTML = '';

            // Add home
            const homeItem = document.createElement('li');
            homeItem.className = 'breadcrumb-item';
            homeItem.textContent = 'Home';
            homeItem.addEventListener('click', () => {
                currentParentId = null;
                breadcrumbPath = [];
                loadParentCategories();
                updateBreadcrumb();
            });
            categoryBreadcrumb.appendChild(homeItem);

            // Add path items
            breadcrumbPath.forEach((item, index) => {
                const breadcrumbItem = document.createElement('li');
                breadcrumbItem.className = index === breadcrumbPath.length - 1 ?
                    'breadcrumb-item active' : 'breadcrumb-item';
                breadcrumbItem.textContent = item.name;

                if (index < breadcrumbPath.length - 1) {
                    breadcrumbItem.addEventListener('click', () => {
                        const clickedId = item.id;
                        breadcrumbPath = breadcrumbPath.slice(0, index + 1);
                        currentParentId = clickedId;
                        loadChildCategories(clickedId);
                        updateBreadcrumb();
                    });
                }

                categoryBreadcrumb.appendChild(breadcrumbItem);
            });
        }

        // Load parent categories
        async function loadParentCategories() {
            const data = await fetchAPI('parent-categories');
            displayResult(categoryResult, data);

            if (data.data) {
                updateCategoriesList(data.data);
            }
        }

        // Load child categories
        async function loadChildCategories(parentId, parentName) {
            currentParentId = parentId;

            if (parentName) {
                breadcrumbPath.push({ id: parentId, name: parentName });
                updateBreadcrumb();
            }

            const data = await fetchAPI(`child-categories/${parentId}`);
            displayResult(categoryResult, data);

            if (data.data) {
                updateCategoriesList(data.data);
            }
        }

        // Event Listeners
        document.getElementById('loadParentCategories').addEventListener('click', () => {
            currentParentId = null;
            breadcrumbPath = [];
            loadParentCategories();
            updateBreadcrumb();
        });

        document.getElementById('loadFeaturedCategories').addEventListener('click', async () => {
            const data = await fetchAPI('featured-categories');
            displayResult(categoryResult, data);
        });

        document.getElementById('loadRecommendedCategories').addEventListener('click', async () => {
            const data = await fetchAPI('recommended-categories');
            displayResult(categoryResult, data);
        });

        document.getElementById('loadTrendingCategories').addEventListener('click', async () => {
            const data = await fetchAPI('trending-categories');
            displayResult(categoryResult, data);
        });

        document.getElementById('loadPopularCategories').addEventListener('click', async () => {
            const data = await fetchAPI('popular-categories');
            displayResult(categoryResult, data);
        });

        document.getElementById('filterForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const params = {
                category: document.getElementById('category').value,
                brand: document.getElementById('brand').value,
                min_price: document.getElementById('minPrice').value,
                max_price: document.getElementById('maxPrice').value,
                search: document.getElementById('search').value,
                sort_by: document.getElementById('sortBy').value,
                limit: document.getElementById('limit').value
            };

            const data = await fetchAPI('filter-products', params);
            displayResult(productResult, data);
        });

        // Initialize
        loadParentCategories();
    </script>
</body>
</html>
