INSERT INTO `variation_options` (`id`, `title`, `price`, `sale_price`, `quantity`, `is_disable`, `sku`, `options`, `product_id`, `created_at`, `updated_at`) VALUES
(1, 'Red', 25, 20, 500, 0, '156156654g654sf64', '[{\"name\": \"Color\", \"value\": \"Red\"}]', 1, '2021-10-10 15:08:54', '2021-12-14 08:05:17'),
(2, 'Blue', 25, 20, 500, 0, 'a5da6546afa', '[{\"name\": \"Color\", \"value\": \"Blue\"}]', 1, '2021-10-10 15:08:54', '2021-12-14 08:05:17'),
(14, 'Blue', 100, 80, 500, 0, '89456413', '[{\"name\": \"Color\", \"value\": \"Blue\"}]', 5, '2021-10-10 19:31:40', '2021-12-14 08:06:49'),
(15, 'Yellow', 100, 80, 500, 0, 'fdsgdfbdfsndhjkdfm', '[{\"name\": \"Color\", \"value\": \"Yellow\"}]', 5, '2021-10-10 19:31:40', '2021-12-14 08:06:49'),
(18, 'Yellow/Medium', 100, 80, 500, 0, '8964bdfhtzvcb', '[{\"name\": \"Color\", \"value\": \"Yellow\"}, {\"name\": \"Size\", \"value\": \"Medium\"}]', 5, '2021-10-10 19:31:40', '2021-10-10 21:54:50'),
(36, 'Red', 800, 750, 500, 0, '750755054654', '[{\"name\": \"Color\", \"value\": \"Red\"}]', 17, '2021-10-11 14:30:59', '2021-12-14 08:12:25'),
(37, 'Blue', 800, 750, 500, 0, '569874/*9', '[{\"name\": \"Color\", \"value\": \"Blue\"}]', 17, '2021-10-11 14:30:59', '2021-12-14 08:12:25'),
(40, 'Red', 30, 22, 500, 0, '9s874bd6515v', '[{\"name\": \"Color\", \"value\": \"Red\"}]', 20, '2021-10-11 14:39:30', '2021-12-14 08:15:29'),
(41, 'Blue', 30, 22, 500, 0, 'hs8456dfs+54sdfa6', '[{\"name\": \"Color\", \"value\": \"Blue\"}]', 20, '2021-10-11 14:39:30', '2021-12-14 08:15:29'),
(42, 'Yellow', 30, 22, 500, 0, 'd8g4a5fd6g4df564gdf', '[{\"name\": \"Color\", \"value\": \"Yellow\"}]', 20, '2021-10-11 14:39:30', '2021-12-14 08:15:29'),
(43, 'Red', 40, 18, 500, 0, '484512', '[{\"name\": \"Color\", \"value\": \"Red\"}]', 21, '2021-10-11 14:41:08', '2021-12-14 08:15:43'),
(44, 'Blue', 40, 25, 500, 0, '984fa5s6d1', '[{\"name\": \"Color\", \"value\": \"Blue\"}]', 21, '2021-10-11 14:41:08', '2021-12-14 08:15:43'),
(46, 'Red', 35, 30, 500, 0, '561s156sd1', '[{\"name\": \"Color\", \"value\": \"Red\"}]', 22, '2021-10-11 14:42:33', '2021-12-14 08:15:50'),
(47, 'Blue', 35, 30, 500, 0, '516as651f56sd1+', '[{\"name\": \"Color\", \"value\": \"Blue\"}]', 22, '2021-10-11 14:42:33', '2021-12-14 08:15:50'),
(48, 'Yellow', 35, 30, 500, 0, '56sa1fg5sdf156asd1f+', '[{\"name\": \"Color\", \"value\": \"Yellow\"}]', 22, '2021-10-11 14:42:33', '2021-12-14 08:15:50'),
(49, 'Red', 50, 40, 500, 0, '894a8sfd6598', '[{\"name\": \"Color\", \"value\": \"Red\"}]', 23, '2021-10-11 14:44:01', '2021-12-14 08:15:58'),
(50, 'Blue', 50, 40, 500, 0, '56a45sda64fas+', '[{\"name\": \"Color\", \"value\": \"Blue\"}]', 23, '2021-10-11 14:44:01', '2021-12-14 08:15:58'),
(51, 'Yellow', 50, 40, 500, 0, 'asd54f5s4afasd654', '[{\"name\": \"Color\", \"value\": \"Yellow\"}]', 23, '2021-10-11 14:44:01', '2021-12-14 08:15:58'),
(52, 'Red', 80, 75, 500, 0, '844f84sd8a++++', '[{\"name\": \"Color\", \"value\": \"Red\"}]', 24, '2021-10-11 14:45:37', '2021-12-14 08:16:05'),
(53, 'Blue', 80, 75, 500, 0, '5fa45sda4f56asdf+++', '[{\"name\": \"Color\", \"value\": \"Blue\"}]', 24, '2021-10-11 14:45:37', '2021-12-14 08:16:05'),
(54, 'Yellow', 80, 75, 500, 0, 's54fs64fsda564fsg894ga++++', '[{\"name\": \"Color\", \"value\": \"Yellow\"}]', 24, '2021-10-11 14:45:37', '2021-12-14 08:16:05'),
(55, '7', 50, 45, 500, 0, '56848+4+', '[{\"name\": \"Size\", \"value\": \"7\"}]', 25, '2021-10-23 13:40:16', '2021-12-14 08:16:14'),
(56, '8', 50, 45, 500, 0, '8514684156+++', '[{\"name\": \"Size\", \"value\": \"8\"}]', 25, '2021-10-23 13:40:16', '2021-12-14 08:16:14'),
(57, '9', 50, 45, 500, 0, '65afdss', '[{\"name\": \"Size\", \"value\": \"9\"}]', 25, '2021-10-23 13:40:16', '2021-12-14 08:16:14'),
(58, '10', 50, 45, 500, 0, '6541651+51651', '[{\"name\": \"Size\", \"value\": \"10\"}]', 25, '2021-10-23 13:40:16', '2021-12-14 08:16:14'),
(59, '7', 200, 180, 500, 0, '51d654sd65g4d65', '[{\"name\": \"Size\", \"value\": \"7\"}]', 27, '2021-10-23 13:50:44', '2021-12-14 08:16:30'),
(60, '8', 200, 180, 500, 0, 'asdfsdgasd4g56465', '[{\"name\": \"Size\", \"value\": \"8\"}]', 27, '2021-10-23 13:50:44', '2021-12-14 08:16:30'),
(61, '9', 200, 180, 500, 0, 'fadsfsda4a56', '[{\"name\": \"Size\", \"value\": \"9\"}]', 27, '2021-10-23 13:50:44', '2021-12-14 08:16:30'),
(62, '10', 200, 180, 500, 0, '5a165sdf56a4', '[{\"name\": \"Size\", \"value\": \"10\"}]', 27, '2021-10-23 13:50:44', '2021-12-14 08:16:30'),
(63, 'Red', 800, 500, 150, 0, 'asdasd3423432sdasdad', '[{\"name\": \"Color\", \"value\": \"Red\"}]', 29, '2021-10-23 20:07:58', '2021-12-14 08:16:45'),
(64, 'Yellow', 850, 550, 100, 0, 'asdasd3244234546nghjghj', '[{\"name\": \"Color\", \"value\": \"Yellow\"}]', 29, '2021-10-23 20:07:58', '2021-12-14 08:16:45'),
(65, 'Blue', 900, 600, 50, 0, 'hfghty7676yfghf', '[{\"name\": \"Color\", \"value\": \"Blue\"}]', 29, '2021-10-23 20:07:58', '2021-12-14 08:16:45'),
(74, '7', 2000, 1800, 150, 0, 'vcvcgd64564tgdfgdfgdf', '[{\"name\": \"Size\", \"value\": \"7\"}]', 31, '2021-10-23 20:18:14', '2021-12-14 08:16:58'),
(75, 'Red', 1000, 900, 100, 0, 'wdwqe324234fsfsfs', '[{\"name\": \"Color\", \"value\": \"Red\"}]', 33, '2021-10-23 20:57:31', '2021-12-14 08:17:10'),
(76, 'Blue', 1000, 899, 100, 0, 'sadasda897989879asda', '[{\"name\": \"Color\", \"value\": \"Blue\"}]', 33, '2021-10-23 20:57:31', '2021-12-14 08:17:10'),
(78, 'Small', 400, 350, 100, 0, 'sdfsdr34354fddsfs', '[{\"name\": \"Size\", \"value\": \"Small\"}]', 35, '2021-10-23 21:14:03', '2021-12-14 08:17:20'),
(81, 'Red', 1150, 950, 100, 0, 'czczc32423dadasda', '[{\"name\": \"Color\", \"value\": \"Red\"}]', 41, '2021-10-23 21:30:19', '2021-12-14 08:19:40'),
(82, 'Blue', 1150, 950, 98, 0, 'fgergtert544tffd', '[{\"name\": \"Color\", \"value\": \"Blue\"}]', 41, '2021-10-23 21:30:19', '2021-12-14 08:19:40'),
(83, '7', 260, 250, 100, 0, 'sfsdf3423dsfdsfsaf', '[{\"name\": \"Size\", \"value\": \"7\"}]', 42, '2021-10-23 21:33:38', '2021-12-14 08:19:34'),
(84, '8', 260, 250, 100, 0, 'dsfsdf435dsdfasd', '[{\"name\": \"Size\", \"value\": \"8\"}]', 42, '2021-10-23 21:33:38', '2021-12-14 08:19:34'),
(85, 'Small', 90, 75, 100, 0, 'fdger543rfsds', '[{\"name\": \"Size\", \"value\": \"Small\"}]', 43, '2021-10-23 21:36:54', '2021-12-14 08:19:28'),
(86, 'Medium', 90, 75, 100, 0, 'asdasd343232dsasd', '[{\"name\": \"Size\", \"value\": \"Medium\"}]', 43, '2021-10-23 21:36:54', '2021-12-14 08:19:28'),
(87, '7', 180, 160, 50, 0, 'gfdgd454sdfsdfs', '[{\"name\": \"Size\", \"value\": \"7\"}]', 46, '2021-10-23 21:49:36', '2021-12-14 08:19:13'),
(88, '8', 180, 160, 50, 0, 'gjgyt565hfghfgh', '[{\"name\": \"Size\", \"value\": \"8\"}]', 46, '2021-10-23 21:49:36', '2021-12-14 08:19:13'),
(89, '9', 180, 160, 50, 0, 'fddg546fgdfgd', '[{\"name\": \"Size\", \"value\": \"9\"}]', 46, '2021-10-23 21:49:36', '2021-12-14 08:19:13'),
(90, 'Medium', 120, 100, 50, 0, 'hgjgh565etgdfgd', '[{\"name\": \"Size\", \"value\": \"Medium\"}]', 47, '2021-10-23 21:53:15', '2021-12-14 08:19:07'),
(91, 'Large', 120, 100, 50, 0, 'kghjgh5464dfssf', '[{\"name\": \"Size\", \"value\": \"Large\"}]', 47, '2021-10-23 21:53:15', '2021-12-14 08:19:07'),
(92, 'Small', 35, 30, 50, 0, 'fhhf6565gbcvbvc', '[{\"name\": \"Size\", \"value\": \"Small\"}]', 50, '2021-10-23 22:03:36', '2021-12-14 08:18:51'),
(93, 'Medium', 35, 30, 50, 0, 'hvhjghj7756tgfdgdf', '[{\"name\": \"Size\", \"value\": \"Medium\"}]', 50, '2021-10-23 22:03:36', '2021-12-14 08:18:51'),
(94, 'Blue', 200, 150, 50, 0, 'ghjgy654645ygfhfg', '[{\"name\": \"Color\", \"value\": \"Blue\"}]', 53, '2021-10-23 22:12:41', '2021-12-14 08:18:36'),
(95, 'Yellow', 200, 150, 50, 0, 'vdfdvfdv786876dsasd', '[{\"name\": \"Color\", \"value\": \"Yellow\"}]', 53, '2021-10-23 22:12:41', '2021-12-14 08:18:36'),
(98, 'Small', 40, 35, 80, 0, 'sfsdfsy786sdfsd', '[{\"name\": \"Size\", \"value\": \"Small\"}]', 55, '2021-10-23 22:18:00', '2021-12-14 08:18:27'),
(99, 'Small', 100, 90, 120, 0, 'ghfh5645tgdfd', '[{\"name\": \"Size\", \"value\": \"Small\"}]', 57, '2021-10-23 22:22:38', '2021-12-14 08:18:15'),
(100, 'Medium', 100, 90, 120, 0, 'kjhk4564dfgfd', '[{\"name\": \"Size\", \"value\": \"Medium\"}]', 57, '2021-10-23 22:22:38', '2021-12-14 08:18:15'),
(101, '7', 250, 220, 500, 0, '8s4f6sdg8d45', '[{\"name\": \"Size\", \"value\": \"7\"}]', 49, '2021-10-24 07:10:20', '2021-12-14 08:18:57'),
(102, '8', 250, 220, 500, 0, '8dgsdf566', '[{\"name\": \"Size\", \"value\": \"8\"}]', 49, '2021-10-24 07:10:20', '2021-12-14 08:18:57'),
(103, '9', 250, 220, 500, 0, '4dsa6f4af5asd', '[{\"name\": \"Size\", \"value\": \"9\"}]', 49, '2021-10-24 07:10:20', '2021-12-14 08:18:57'),
(104, '10', 250, 220, 500, 0, 'f65a4sd56f4g8a4', '[{\"name\": \"Size\", \"value\": \"10\"}]', 49, '2021-10-24 07:10:20', '2021-12-14 08:18:57'),
(108, 'Large', 100, 80, 500, 0, '56sdf1g65151', '[{\"name\": \"Size\", \"value\": \"Large\"}]', 57, '2021-11-28 12:13:10', '2021-12-14 08:18:15'),
(109, 'Medium', 40, 30, 500, 0, '515xcz1v8d4ga489', '[{\"name\": \"Size\", \"value\": \"Medium\"}]', 55, '2021-11-28 12:14:10', '2021-12-14 08:18:27'),
(110, 'Large', 40, 10, 500, 0, 'ds5g4fd84gdr84b', '[{\"name\": \"Size\", \"value\": \"Large\"}]', 55, '2021-11-28 12:14:10', '2021-12-14 08:18:27'),
(112, '10', 180, 160, 500, 0, 's4f6sda84ds8951', '[{\"name\": \"Size\", \"value\": \"10\"}]', 46, '2021-11-28 12:41:02', '2021-12-14 08:19:13'),
(113, 'Large', 90, 75, 500, 0, 'tr7gs14x2.35+65v2', '[{\"name\": \"Size\", \"value\": \"Large\"}]', 43, '2021-11-28 12:41:28', '2021-12-14 08:19:28'),
(114, '9', 260, 240, 500, 0, '8d4fd8a4gwer/hge489b51', '[{\"name\": \"Size\", \"value\": \"9\"}]', 42, '2021-11-28 12:42:03', '2021-12-14 08:19:34'),
(115, '10', 260, 240, 500, 0, 'd4fgsdhsd7hsd/h984', '[{\"name\": \"Size\", \"value\": \"10\"}]', 42, '2021-11-28 12:42:03', '2021-12-14 08:19:34'),
(116, 'Yellow', 1150, 950, 500, 0, 'geebgsfv', '[{\"name\": \"Color\", \"value\": \"Yellow\"}]', 41, '2021-11-28 12:42:37', '2021-12-14 08:19:40'),
(117, '8', 2000, 1800, 500, 0, 'dgs4gd6f565fdb1fd5b165', '[{\"name\": \"Size\", \"value\": \"8\"}]', 31, '2021-11-28 12:55:46', '2021-12-14 08:16:58'),
(118, 'Red', 420, 350, 500, 0, '5g1s6dfg56sfd1g65d1', '[{\"name\": \"Color\", \"value\": \"Red\"}]', 36, '2021-11-28 12:56:55', '2021-12-14 08:17:26'),
(120, 'Red/Small', 200, 180, 500, 0, 'fa984v1 651651', '[{\"name\": \"Color\", \"value\": \"Red\"}, {\"name\": \"Size\", \"value\": \"Small\"}]', 3, '2021-11-28 12:59:08', '2021-12-14 08:06:34'),
(121, 'Red/Small', 650, 550, 500, 0, '9re74a1b2655v #02', '[{\"name\": \"Color\", \"value\": \"Red\"}, {\"name\": \"Size\", \"value\": \"Small\"}]', 7, '2021-11-28 13:00:22', '2021-12-14 08:07:33'),
(122, 'Red/Medium', 650, 580, 500, 0, '84fa8486dsa4f655sdf26', '[{\"name\": \"Color\", \"value\": \"Red\"}, {\"name\": \"Size\", \"value\": \"Medium\"}]', 7, '2021-11-28 13:00:22', '2021-12-14 08:07:33'),
(123, 'Red/Large', 650, 580, 500, 0, '5f56ad4f651503153250', '[{\"name\": \"Color\", \"value\": \"Red\"}, {\"name\": \"Size\", \"value\": \"Large\"}]', 7, '2021-11-28 13:00:22', '2021-12-14 08:07:33'),
(124, 'Red/Small', 65, 50, 500, 0, 'dage9gr8eg84f85484', '[{\"name\": \"Color\", \"value\": \"Red\"}, {\"name\": \"Size\", \"value\": \"Small\"}]', 8, '2021-11-28 13:00:52', '2021-12-14 08:08:31'),
(125, 'Red/Small', 30, NULL, 500, 0, 'wertyuiolp;[\'', '[{\"name\": \"Color\", \"value\": \"Red\"}, {\"name\": \"Size\", \"value\": \"Small\"}]', 15, '2021-11-28 13:09:00', '2021-12-14 08:11:41'),
(126, 'Red/Small', 25, 20, 500, 0, 'asdfghjk', '[{\"name\": \"Color\", \"value\": \"Red\"}, {\"name\": \"Size\", \"value\": \"Small\"}]', 16, '2021-11-28 13:09:28', '2021-12-14 08:12:16'),
(127, 'Blue', 350, 300, 1000, 0, '5g34fdfg56sfd1g65d1', '[{\"name\": \"Color\", \"value\": \"Blue\"}]', 36, '2021-12-14 08:01:12', '2021-12-14 08:17:26'),
(128, 'Yellow', 650, 500, 1200, 0, '5g1s6dfg56sfd1g45df', '[{\"name\": \"Color\", \"value\": \"Yellow\"}]', 36, '2021-12-14 08:01:12', '2021-12-14 08:17:26'),
(129, 'Medium', 1000, 899, 1000, 0, 'sdfsdr34354fd1fd3', '[{\"name\": \"Size\", \"value\": \"Medium\"}]', 35, '2021-12-14 08:01:40', '2021-12-14 08:17:20'),
(130, '7', 1000, 599, 1000, 0, 'sdfsdr34354ffsa', '[{\"name\": \"Size\", \"value\": \"7\"}]', 2, '2021-12-14 08:05:49', '2021-12-14 08:05:49'),
(131, '8', 2000, 1999, 1000, 0, 'sdfsdr34354fdlh', '[{\"name\": \"Size\", \"value\": \"8\"}]', 2, '2021-12-14 08:05:49', '2021-12-14 08:05:49'),
(132, 'Red/Medium', 300, 250, 1000, 0, 'sdfsdr3435443d', '[{\"name\": \"Color\", \"value\": \"Red\"}, {\"name\": \"Size\", \"value\": \"Medium\"}]', 3, '2021-12-14 08:06:34', '2021-12-14 08:06:34'),
(133, 'Blue/Small', 400, 387, 1000, 0, 'sdfsdr34354fd34j', '[{\"name\": \"Color\", \"value\": \"Blue\"}, {\"name\": \"Size\", \"value\": \"Small\"}]', 3, '2021-12-14 08:06:34', '2021-12-14 08:06:34'),
(134, 'Blue/Medium', 600, 500, 1000, 0, 'sdfsdr34354fdwer', '[{\"name\": \"Color\", \"value\": \"Blue\"}, {\"name\": \"Size\", \"value\": \"Medium\"}]', 3, '2021-12-14 08:06:34', '2021-12-14 08:06:34'),
(135, 'Yellow/Small', 600, 500, 1000, 0, 'sdfsdr34354fdlen', '[{\"name\": \"Color\", \"value\": \"Yellow\"}, {\"name\": \"Size\", \"value\": \"Small\"}]', 7, '2021-12-14 08:07:33', '2021-12-14 08:07:33'),
(136, 'Yellow/Medium', 700, 550, 1000, 0, 'sdfsdr34354fdlj', '[{\"name\": \"Color\", \"value\": \"Yellow\"}, {\"name\": \"Size\", \"value\": \"Medium\"}]', 7, '2021-12-14 08:07:33', '2021-12-14 08:07:33'),
(137, 'Yellow/Large', 800, 650, 1000, 0, 'sdfsdr34354fdmed', '[{\"name\": \"Color\", \"value\": \"Yellow\"}, {\"name\": \"Size\", \"value\": \"Large\"}]', 7, '2021-12-14 08:07:33', '2021-12-14 08:07:33'),
(138, 'Red/Large', 100, 75, 500, 0, 'sdfsdr34354fdfjr', '[{\"name\": \"Color\", \"value\": \"Red\"}, {\"name\": \"Size\", \"value\": \"Large\"}]', 8, '2021-12-14 08:08:31', '2021-12-14 08:08:31'),
(139, 'Blue/Small', 1000, 599, 1000, 0, 'sdfsdr34354f2fb', '[{\"name\": \"Color\", \"value\": \"Blue\"}, {\"name\": \"Size\", \"value\": \"Small\"}]', 8, '2021-12-14 08:08:31', '2021-12-14 08:08:31'),
(140, 'Blue/Large', 1500, 899, 1000, 0, 'sdfsdr34354f23fj', '[{\"name\": \"Color\", \"value\": \"Blue\"}, {\"name\": \"Size\", \"value\": \"Large\"}]', 8, '2021-12-14 08:08:31', '2021-12-14 08:08:31'),
(141, '7', 1000, 599, 1000, 0, 'sdfsdr34354dsf', '[{\"name\": \"Size\", \"value\": \"7\"}]', 14, '2021-12-14 08:10:38', '2021-12-14 08:10:38'),
(142, '8', 450, 400, 1000, 0, 'sdfsdr3435dfdds', '[{\"name\": \"Size\", \"value\": \"8\"}]', 14, '2021-12-14 08:10:38', '2021-12-14 08:10:38'),
(143, '9', 1000, 499, 1000, 0, 'sdfsdr343sdfsf', '[{\"name\": \"Size\", \"value\": \"9\"}]', 14, '2021-12-14 08:10:38', '2021-12-14 08:10:38'),
(144, 'Red/Medium', 100, 59, 1000, 0, 'dsfsdfsdfsdf', '[{\"name\": \"Color\", \"value\": \"Red\"}, {\"name\": \"Size\", \"value\": \"Medium\"}]', 15, '2021-12-14 08:11:41', '2021-12-14 08:11:41'),
(145, 'Blue/Small', 500, 399, 1000, 0, 'sdfjdshkjsdfhk', '[{\"name\": \"Color\", \"value\": \"Blue\"}, {\"name\": \"Size\", \"value\": \"Small\"}]', 15, '2021-12-14 08:11:41', '2021-12-14 08:11:41'),
(146, 'Blue/Medium', 1000, 599, 1000, 0, 'sdlfjdslifjsdf', '[{\"name\": \"Color\", \"value\": \"Blue\"}, {\"name\": \"Size\", \"value\": \"Medium\"}]', 15, '2021-12-14 08:11:41', '2021-12-14 08:11:41'),
(147, 'Red/Medium', 100, 99, 1000, 0, 'sdlkfjdslifj', '[{\"name\": \"Color\", \"value\": \"Red\"}, {\"name\": \"Size\", \"value\": \"Medium\"}]', 16, '2021-12-14 08:12:16', '2021-12-14 08:12:16'),
(148, 'Yellow/Small', 500, 200, 1000, 0, 'sdilfhjdliksjfh', '[{\"name\": \"Color\", \"value\": \"Yellow\"}, {\"name\": \"Size\", \"value\": \"Small\"}]', 16, '2021-12-14 08:12:16', '2021-12-14 08:12:16'),
(149, 'Yellow/Medium', 1000, 239, 1000, 0, 'sdlfjsldjsdf', '[{\"name\": \"Color\", \"value\": \"Yellow\"}, {\"name\": \"Size\", \"value\": \"Medium\"}]', 16, '2021-12-14 08:12:16', '2021-12-14 08:12:16'),
(150, '9', 4000, 3999, 1000, 0, 'adflkjsfljdsfs', '[{\"name\": \"Size\", \"value\": \"9\"}]', 31, '2021-12-14 08:13:54', '2021-12-14 08:16:58'),
(151, '10', 5000, 3500, 1000, 0, 'sdlkfjhsdlfj', '[{\"name\": \"Size\", \"value\": \"10\"}]', 31, '2021-12-14 08:13:54', '2021-12-14 08:16:58');