<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Downloadable Product Test</title>
    <style>
        body { font-family: sans-serif; margin: 20px; }
        label, input, button { display: block; margin-bottom: 10px; }
        #response { margin-top: 20px; padding: 10px; border: 1px solid #ccc; background-color: #f9f9f9; white-space: pre-wrap; }
        #loginSection, #downloadTestSection {
            border: 1px solid #eee;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            background-color: #fff;
        }
        #loginSection h2, #downloadTestSection h2 {
            margin-top: 0;
            color: #333;
        }
        .alert {
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 5px;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .d-none {
            display: none;
        }
        hr {
            margin: 30px 0;
        }
        .product-item {
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 5px;
            cursor: pointer;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .product-item:hover {
            background-color: #e9e9e9;
        }
        .product-item.selected {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <h1>Downloadable Product Test</h1>

    <p>This page demonstrates how to test the downloadable product functionality. First, log in to obtain a user token, then select a downloadable product from the list to generate and test the download URL.</p>

    <!-- Login Section -->
    <div id="loginSection">
        <h2>Login</h2>
        <div id="loginError" class="alert alert-danger d-none" role="alert"></div>
        <div id="loginSuccess" class="alert alert-success d-none" role="alert"></div>
        <form id="loginForm">
            <label for="apiBaseUrl">API Base URL (e.g., http://localhost:8000/api):</label>
            <input type="text" id="apiBaseUrl" value="http://localhost:8000" size="50">

            <label for="loginEmail">Email:</label>
            <input type="email" id="loginEmail" value="<EMAIL>" size="50">

            <label for="loginPassword">Password:</label>
            <input type="password" id="loginPassword" value="admin" size="50">

            <button type="submit">Login and Get Token</button>
            <button type="button" id="logoutBtn" style="margin-left: 10px;">Logout</button>
        </form>
    </div>

    <hr>

    <!-- Download Test Section -->
    <div id="downloadTestSection" class="d-none">
        <h2>Downloadable Products</h2>
        <div id="productsList">
            <p>Loading products...</p>
        </div>

        <hr>

        <h2>Generate Download URL</h2>
        <label for="userToken">User Bearer Token (auto-filled after login):</label>
        <input type="text" id="userToken" value="" size="50" readonly>

        <label for="digitalFileId">Selected Digital File ID:</label>
        <input type="text" id="digitalFileId" value="" readonly>

        <button type="submit" form="downloadForm">Generate Download URL</button>
    </div>

    <h2>Response:</h2>
    <pre id="response"></pre>

    <script>
        // DOM Elements
        const apiBaseUrlInput = document.getElementById('apiBaseUrl');
        const loginEmailInput = document.getElementById('loginEmail');
        const loginPasswordInput = document.getElementById('loginPassword');
        const loginForm = document.getElementById('loginForm');
        const logoutBtn = document.getElementById('logoutBtn');
        const loginErrorDiv = document.getElementById('loginError');
        const loginSuccessDiv = document.getElementById('loginSuccess');
        const loginSection = document.getElementById('loginSection');
        const downloadTestSection = document.getElementById('downloadTestSection');

        const productsListDiv = document.getElementById('productsList');
        const userTokenInput = document.getElementById('userToken');
        const digitalFileIdInput = document.getElementById('digitalFileId');
        const downloadForm = document.getElementById('downloadForm');
        const responseDiv = document.getElementById('response');

        // Global variables for token and user data
        let authToken = null;
        let currentUser = null;

        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();

            loginForm.addEventListener('submit', function(event) {
                event.preventDefault();
                login();
            });

            logoutBtn.addEventListener('click', function() {
                logout();
            });

            downloadForm.addEventListener('submit', async function(event) {
                event.preventDefault();
                await generateDownloadUrl();
            });
        });

        function checkAuthStatus() {
            authToken = localStorage.getItem('authToken');
            currentUser = JSON.parse(localStorage.getItem('user'));

            if (authToken && currentUser) {
                userTokenInput.value = authToken;
                loginSection.classList.add('d-none');
                downloadTestSection.classList.remove('d-none');
                loginSuccessDiv.textContent = `Welcome, ${currentUser.name || currentUser.email}! You are logged in.`;
                loginSuccessDiv.classList.remove('d-none');
                responseDiv.textContent = ''; // Clear previous response
                fetchDownloadableProducts(); // Fetch products after login
            } else {
                loginSection.classList.remove('d-none');
                downloadTestSection.classList.add('d-none');
                loginSuccessDiv.classList.add('d-none'); // Hide success message if not logged in
                responseDiv.textContent = 'Please log in to proceed with the download test.';
            }
        }

        async function login() {
            const apiBaseUrl = apiBaseUrlInput.value.trim();
            const email = loginEmailInput.value.trim();
            const password = loginPasswordInput.value.trim();

            loginErrorDiv.classList.add('d-none');
            loginSuccessDiv.classList.add('d-none');
            responseDiv.textContent = 'Attempting login...';

            if (!apiBaseUrl || !email || !password) {
                loginErrorDiv.textContent = 'Please fill in all login fields.';
                loginErrorDiv.classList.remove('d-none');
                responseDiv.textContent = '';
                return;
            }

            try {
                const loginUrl = `${apiBaseUrl}/token`;
                const loginResponse = await fetch(loginUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ email, password, device_name: 'web' })
                });

                const loginData = await loginResponse.json();

                if (!loginResponse.ok) {
                    const errorMessage = loginData.message || `Login failed. Status: ${loginResponse.status}`;
                    loginErrorDiv.textContent = errorMessage;
                    loginErrorDiv.classList.remove('d-none');
                    responseDiv.textContent = '';
                    return;
                }

                if (loginData.token) {
                    authToken = loginData.token;
                    currentUser = loginData.user || { name: loginData.name || email, email: email };

                    localStorage.setItem('authToken', authToken);
                    localStorage.setItem('user', JSON.stringify(currentUser));

                    userTokenInput.value = authToken;
                    loginSection.classList.add('d-none');
                    downloadTestSection.classList.remove('d-none');
                    loginSuccessDiv.textContent = `Login successful! Welcome, ${currentUser.name || currentUser.email}. Token obtained and set.`;
                    loginSuccessDiv.classList.remove('d-none');
                    responseDiv.textContent = '';
                    await fetchDownloadableProducts();
                } else {
                    loginErrorDiv.textContent = `Login successful, but no token found in response. Please check API response structure.`;
                    loginErrorDiv.classList.remove('d-none');
                    responseDiv.textContent = '';
                }

            } catch (error) {
                loginErrorDiv.textContent = `An error occurred during login: ${error.message}`;
                loginErrorDiv.classList.remove('d-none');
                responseDiv.textContent = '';
                console.error(error);
            }
        }

        function logout() {
            authToken = null;
            currentUser = null;
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');

            loginForm.reset();
            userTokenInput.value = '';
            digitalFileIdInput.value = ''; // Clear selected digital file ID

            loginErrorDiv.classList.add('d-none');
            loginSuccessDiv.classList.add('d-none');

            checkAuthStatus();
            responseDiv.textContent = 'You have been logged out.';
        }

        async function fetchDownloadableProducts() {
            productsListDiv.innerHTML = '<p>Loading downloadable products...</p>';
            const apiBaseUrl = apiBaseUrlInput.value.trim();

            if (!authToken) {
                productsListDiv.innerHTML = '<p class="alert alert-danger">Please log in to view downloadable products.</p>';
                return;
            }

            try {
                const productsUrl = `${apiBaseUrl}/downloads`;
                const productsResponse = await fetch(productsUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`,
                        'Accept': 'application/json'
                    }
                });

                const productsData = await productsResponse.json();

                if (!productsResponse.ok) {
                    productsListDiv.innerHTML = `<p class="alert alert-danger">Error fetching products: ${JSON.stringify(productsData, null, 2)}</p>`;
                    return;
                }

                if (productsData.data && productsData.data.length > 0) {
                    productsListDiv.innerHTML = '<h3>Select a product to download:</h3>';
                    productsData.data.forEach(product => {
                        const productItem = document.createElement('div');
                        productItem.className = 'product-item';
                        // Display product name and file name if available
                        const productName = product.file.fileable ? product.file.fileable.name : 'Unknown Product';
                        const fileName = product.file ? product.file.file_name : 'Unknown File';
                        productItem.textContent = `${productName} (File: ${fileName})`;
                        productItem.dataset.digitalFileId = product.digital_file_id;
                        productItem.dataset.fileName = fileName;

                        productItem.addEventListener('click', () => {
                            // Remove 'selected' class from previously selected item
                            const currentSelected = document.querySelector('.product-item.selected');
                            if (currentSelected) {
                                currentSelected.classList.remove('selected');
                            }
                            // Add 'selected' class to the clicked item
                            productItem.classList.add('selected');
                            digitalFileIdInput.value = productItem.dataset.digitalFileId;
                            responseDiv.textContent = `Selected: ${productItem.dataset.fileName}. Click 'Generate Download URL' to proceed.`;
                        });
                        productsListDiv.appendChild(productItem);
                    });
                } else {
                    productsListDiv.innerHTML = '<p class="alert alert-info">No downloadable products found for this user.</p>';
                }

            } catch (error) {
                productsListDiv.innerHTML = `<p class="alert alert-danger">An error occurred fetching products: ${error.message}</p>`;
                console.error(error);
            }
        }

        async function generateDownloadUrl() {
            const apiBaseUrl = apiBaseUrlInput.value.trim();
            const userToken = userTokenInput.value.trim();
            const digitalFileId = digitalFileIdInput.value.trim();

            responseDiv.textContent = 'Generating download URL...';

            if (!apiBaseUrl || !userToken || !digitalFileId) {
                responseDiv.textContent = 'Please ensure API Base URL, User Token, and Digital File ID are filled.';
                return;
            }

            try {
                const generateUrl = `${apiBaseUrl}/downloads/digital_file`;
                const generateResponse = await fetch(generateUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${userToken}`,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ digital_file_id: digitalFileId })
                });

                const generateData = await generateResponse.json();

                if (!generateResponse.ok) {
                    responseDiv.textContent = `Error generating URL: ${JSON.stringify(generateData, null, 2)}`;
                    return;
                }

                const downloadUrl = generateData;

                responseDiv.textContent = `Generated Download URL: ${downloadUrl}\n\nAttempting to download...`;

                const downloadLink = document.createElement('a');
                downloadLink.href = downloadUrl;
                downloadLink.download = '';
                downloadLink.textContent = 'Click here to download the file';
                downloadLink.target = '_blank';

                responseDiv.appendChild(document.createElement('br'));
                responseDiv.appendChild(downloadLink);
                responseDiv.appendChild(document.createElement('br'));
                responseDiv.appendChild(document.createTextNode('\nCheck your browser\'s network tab for the actual file request and its headers/filename.'));

                const fileResponse = await fetch(downloadUrl, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${userToken}`,
                        'Accept': 'application/json'
                    }
                });

                const fileBlob = await fileResponse.blob();
                const fileNameHeader = fileResponse.headers.get('Content-Disposition');
                const contentTypeHeader = fileResponse.headers.get('Content-Type');

                responseDiv.textContent += `\n\n--- File Fetch Details (for inspection) ---\n`;
                responseDiv.textContent += `Content-Type: ${contentTypeHeader}\n`;
                responseDiv.textContent += `Content-Disposition: ${fileNameHeader}\n`;
                responseDiv.textContent += `Blob size: ${fileBlob.size} bytes\n`;

                if (fileNameHeader) {
                    const filenameMatch = /filename\*?=['"]?(?:UTF-\d['"]*)?([^;\n]*?)['"]?;?/.exec(fileNameHeader);
                    if (filenameMatch && filenameMatch[1]) {
                        const filename = decodeURIComponent(filenameMatch[1].replace(/^utf-8''/, ''));
                        responseDiv.textContent += `Extracted Filename: ${filename}\n`;
                        const extensionMatch = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(filename);
                        if (extensionMatch && extensionMatch[1]) {
                            responseDiv.textContent += `Extracted Extension: ${extensionMatch[1]}\n`;
                        } else {
                            responseDiv.textContent += `No extension found in extracted filename.\n`;
                        }
                    }
                } else {
                    responseDiv.textContent += `Content-Disposition header not found.\n`;
                }

            } catch (error) {
                responseDiv.textContent = `An error occurred: ${error.message}\n${error.stack}`;
                console.error(error);
            }
        }
    </script>
</body>
</html>
