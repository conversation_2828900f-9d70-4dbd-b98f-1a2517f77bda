<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delivery Agent Registration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #response {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #dff0d8;
            color: #3c763d;
            border: 1px solid #d6e9c6;
        }
        .error {
            background-color: #f2dede;
            color: #a94442;
            border: 1px solid #ebccd1;
        }
    </style>
</head>
<body>
    <h1>Delivery Agent Registration Test</h1>
    <form id="registrationForm">
        <h2>Step 1: Provide Your Details</h2>
        <div id="initialDetailsSection">
            <label for="name">Full Name:</label>
            <input type="text" id="name" name="name" required>
        </div>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required>
        </div>
        
        <div class="form-group">
            <label for="phone_number">Phone Number:</label>
            <input type="tel" id="phone_number" name="phone_number" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <div class="form-group">
            <label for="password_confirmation">Confirm Password:</label>
            <input type="password" id="password_confirmation" name="password_confirmation" required>
        </div>
        
        <button type="submit">Initiate Registration</button>
    </form>

    <form id="otpForm" style="display: none;">
        <h2>Step 2: Verify OTP</h2>
        <p>An OTP has been sent to <strong id="otpEmailDisplay"></strong>. Please enter it below.</p>
        <div class="form-group">
            <label for="otp_code">OTP Code:</label>
            <input type="text" id="otp_code" name="otp_code" required>
        </div>
        <button type="submit">Verify OTP & Register</button>
    </form>
    
    <div id="response"></div>
    
    <script>
        const API_BASE_URL = 'http://127.0.0.1:8000';
        let userEmailForOtp = ''; // To store email for OTP verification step

        const registrationForm = document.getElementById('registrationForm');
        const otpForm = document.getElementById('otpForm');
        const responseDiv = document.getElementById('response');
        const otpEmailDisplay = document.getElementById('otpEmailDisplay');

        registrationForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                phone_number: document.getElementById('phone_number').value,
                password: document.getElementById('password').value,
                password_confirmation: document.getElementById('password_confirmation').value
            };
            userEmailForOtp = formData.email; // Store email for the next step
            
            try {
                const response = await fetch(`${API_BASE_URL}/agent/initiate-registration`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();

                if (!response.ok) {
                    throw data; // Throw the error object from API
                }
                
                responseDiv.style.display = 'block';
                responseDiv.className = 'success';
                responseDiv.innerHTML = `<p>${data.message || 'OTP initiated successfully. Please check your email.'}</p>`;
                
                registrationForm.style.display = 'none'; // Hide initial form
                otpEmailDisplay.textContent = userEmailForOtp;
                otpForm.style.display = 'block'; // Show OTP form

            } catch (error) {
                displayError(error, 'Error initiating registration:');
            }
        });

        otpForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const otpData = {
                email: userEmailForOtp,
                otp_code: document.getElementById('otp_code').value
            };

            try {
                const response = await fetch(`${API_BASE_URL}/agent/verify-registration`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(otpData)
            });

            const data = await response.json();

                if (!response.ok) {
                    throw data; // Throw the error object from API
                }

                responseDiv.style.display = 'block';
                responseDiv.className = 'success';
                responseDiv.innerHTML = `
                    <h3>Success!</h3>
                    <p>${data.message || 'Registration successful!'}</p>
                    <p><strong>Token:</strong> ${data.data.token}</p>
                    <p><strong>Role:</strong> ${data.data.role}</p>
                    <p><strong>Permissions:</strong> ${data.data.permissions.join(', ')}</p>
                `;
                otpForm.style.display = 'none'; // Hide OTP form on final success
            } catch (error) {
                displayError(error, 'Error verifying OTP:');
            }
        });

        function displayError(error, prefix = 'Error:') {
                responseDiv.style.display = 'block';
                responseDiv.className = 'error';
                
                let errorMessage = 'An error occurred during registration.';
                if (error.message) {
                    errorMessage = error.message;
                } else if (error.errors) {
                    errorMessage = Object.values(error.errors).join('<br>');
                }
                
                responseDiv.innerHTML = `
                    <h3>${prefix}</h3>
                    <p>${errorMessage.replace(/\n/g, '<br>')}</p>
                `;
        }
    </script>
</body>
</html>