<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nearest Agents Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
        }
        .container {
            max-width: 900px;
        }
        .card {
            margin-bottom: 20px;
        }
        .response-section {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 500px;
            overflow-y: auto;
        }
        #loginSection {
            max-width: 500px;
            margin: 0 auto;
        }
        #nearestAgentsSection {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4 text-center">Nearest Agents API Test</h1>

        <!-- Login Section -->
        <div id="loginSection" class="card">
            <div class="card-body">
                <h5 class="card-title">Login</h5>
                <div id="loginError" class="alert alert-danger d-none" role="alert"></div>
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email address</label>
                        <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" value="admin" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Login</button>
                </form>
            </div>
        </div>

        <!-- Nearest Agents Section (hidden until logged in) -->
        <div id="nearestAgentsSection">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3>Welcome, <span id="userName">User</span></h3>
                <button id="logoutBtn" class="btn btn-outline-danger">Logout</button>
            </div>

            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Find Nearest Agents for Order</h5>
                    <div id="apiError" class="alert alert-danger d-none" role="alert"></div>
                    <form id="nearestAgentsForm" class="row g-3">
                        <div class="col-md-6">
                            <label for="orderId" class="form-label">Order ID</label>
                            <input type="number" class="form-control" id="orderId" value="1" required>
                        </div>
                        <div class="col-md-6">
                            <label for="radius" class="form-label">Radius (km, 1-100)</label>
                            <input type="number" class="form-control" id="radius" value="15" min="1" max="100">
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-success">Find Nearest Agents</button>
                        </div>
                    </form>

                    <h6 class="mt-4">API Response:</h6>
                    <div id="apiResponse" class="response-section">
                        <p class="text-muted">Response will appear here...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let authToken = null;
        let currentUser = null;
        const API_BASE_URL = 'http://localhost:8000'; // Set to your API base URL, e.g., 'http://localhost:8000/api' or just '' if same origin

        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();

            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                login();
            });

            document.getElementById('logoutBtn').addEventListener('click', function() {
                logout();
            });

            document.getElementById('nearestAgentsForm').addEventListener('submit', function(e) {
                e.preventDefault();
                findNearestAgents();
            });
        });

        function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            const user = JSON.parse(localStorage.getItem('user'));

            if (token && user) {
                authToken = token;
                currentUser = user;
                document.getElementById('userName').textContent = user.name || user.email;
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('nearestAgentsSection').style.display = 'block';
            } else {
                document.getElementById('loginSection').style.display = 'block';
                document.getElementById('nearestAgentsSection').style.display = 'none';
            }
        }

        function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginError = document.getElementById('loginError');
            loginError.classList.add('d-none');

            const submitBtn = document.querySelector('#loginForm button[type="submit"]');
            const originalBtnText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Logging in...';

            fetch(`${API_BASE_URL}/token`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
                body: JSON.stringify({ email: email, password: password })
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(errData => {
                        throw new Error(errData.message || 'Login failed. Status: ' + response.status);
                    }).catch(() => {
                        throw new Error('Login failed. Status: ' + response.status);
                    });
                }
                return response.json();
            })
            .then(data => {
                authToken = data.token || data.access_token;
                currentUser = data.user || {name: data.name || data.email, email: data.email};
                if (!authToken) throw new Error('Token not found in login response.');

                localStorage.setItem('authToken', authToken);
                localStorage.setItem('user', JSON.stringify(currentUser));
                checkAuthStatus();
            })
            .catch(error => {
                console.error('Login error:', error);
                loginError.textContent = error.message || 'Invalid email or password.';
                loginError.classList.remove('d-none');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = originalBtnText;
            });
        }

        function logout() {
            authToken = null;
            currentUser = null;
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('nearestAgentsSection').style.display = 'none';
            document.getElementById('loginForm').reset();
            document.getElementById('loginError').classList.add('d-none');
            document.getElementById('apiResponse').innerHTML = '<p class="text-muted">Response will appear here...</p>';
            document.getElementById('apiError').classList.add('d-none');
        }

        async function findNearestAgents() {
            if (!authToken) {
                document.getElementById('apiError').textContent = 'Authentication token is missing. Please log in.';
                document.getElementById('apiError').classList.remove('d-none');
                return;
            }

            const orderId = document.getElementById('orderId').value;
            const radius = document.getElementById('radius').value;
            const apiResponseDiv = document.getElementById('apiResponse');
            const apiErrorDiv = document.getElementById('apiError');

            apiErrorDiv.classList.add('d-none');
            apiResponseDiv.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
                    <p>Fetching nearest agents...</p>
                </div>`;

            let url = `${API_BASE_URL}/orders/${orderId}/nearest-agents`;
            const params = new URLSearchParams();
            if (radius) {
                params.append('radius', radius);
            }
            if (params.toString()) {
                url += `?${params.toString()}`;
            }

            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (!response.ok) {
                    if (response.status === 401) {
                        logout();
                        throw new Error('Authentication failed. Your session has expired. Please log in again.');
                    }
                    throw new Error(data.message || `API request failed with status: ${response.status}`);
                }

                apiResponseDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                console.error('Error fetching nearest agents:', error);
                apiErrorDiv.textContent = error.message || 'An unexpected error occurred.';
                apiErrorDiv.classList.remove('d-none');
                apiResponseDiv.innerHTML = `<p class="text-muted">Failed to load response.</p>`;
            }
        }
    </script>
</body>
</html>