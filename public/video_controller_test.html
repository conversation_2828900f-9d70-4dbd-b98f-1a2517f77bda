<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Controller API Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom styles for specific elements not easily covered by Tailwind */
        .response {
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body class="p-5 bg-gray-50">
    <div class="max-w-6xl mx-auto">
        <h1 class="mb-6 text-3xl font-bold text-center">Video Controller API Test</h1>

        <!-- Login Section -->
        <div id="loginSection" class="max-w-md mx-auto bg-white shadow-md rounded-lg p-6 mb-5">
            <div class="p-6">
                <h5 class="text-2xl font-semibold mb-4">Login</h5>
                <div id="loginError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative hidden" role="alert"></div>
                <form id="loginForm">
                    <div class="mb-4">
                        <label for="email" class="block text-gray-700 text-sm font-bold mb-2">Email address</label>
                        <input type="email" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="email" name="email" value="<EMAIL>" required>
                    </div>
                    <div class="mb-4">
                        <label for="password" class="block text-gray-700 text-sm font-bold mb-2">Password</label>
                        <input type="password" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="password" name="password" value="admin" required>
                    </div>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">Login</button>
                </form>
            </div>
        </div>

        <!-- Video Management Section (hidden until logged in) -->
        <div id="videoManagementSection" style="display: none;">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-2xl font-semibold">Welcome, <span id="userName">User</span></h3>
                <button id="logoutBtn" class="bg-transparent hover:bg-red-500 text-red-700 font-semibold hover:text-white py-2 px-4 border border-red-500 hover:border-transparent rounded">Logout</button>
            </div>

            <!-- Video Upload Section -->
            <div class="mb-5 p-4 bg-gray-100 rounded-lg">
                <h2 class="text-xl font-semibold mb-3">Upload Video</h2>
                <form id="uploadForm">
                    <div class="mb-4">
                        <label for="videoFile" class="block text-gray-700 text-sm font-bold mb-2">Video File:</label>
                        <input type="file" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="videoFile" accept="video/*" required>
                    </div>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">Upload Video</button>
                </form>
                <div id="uploadResponse" class="response bg-gray-100 border border-gray-300 rounded p-3 mt-3" style="display: none;"></div>
            </div>

            <!-- Get Videos Section -->
            <div class="mb-5 p-4 bg-gray-100 rounded-lg">
                <h2 class="text-xl font-semibold mb-3">Get All Videos</h2>
                <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mr-2" onclick="getVideos()">GET /api/videos</button>
                <div id="getVideosResponse" class="response bg-gray-100 border border-gray-300 rounded p-3 mt-3" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let authToken = null;
        let currentUser = null;
        const base_url = 'http://localhost:8000'; // Set to your API base URL, e.g., 'http://localhost:8000/api' or just '' if same origin

        document.addEventListener('DOMContentLoaded', function() {
            checkAuthStatus();

            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                login();
            });

            document.getElementById('logoutBtn').addEventListener('click', function() {
                logout();
            });

            document.getElementById('uploadForm').addEventListener('submit', function(e) {
                e.preventDefault();
                uploadVideo();
            });
        });

        function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            const user = JSON.parse(localStorage.getItem('user'));

            if (token && user) {
                authToken = token;
                currentUser = user;
                document.getElementById('userName').textContent = user.name || user.email;
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('videoManagementSection').style.display = 'block';
            } else {
                document.getElementById('loginSection').style.display = 'block';
                document.getElementById('videoManagementSection').style.display = 'none';
            }
        }

        function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginError = document.getElementById('loginError');
            loginError.classList.add('hidden'); // Use 'hidden' for Tailwind

            const submitBtn = document.querySelector('#loginForm button[type="submit"]');
            const originalBtnText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="animate-spin inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2" role="status" aria-hidden="true"></span> Logging in...';

            fetch(`${base_url}/token`, { // Replace with your actual login endpoint if different
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
                body: JSON.stringify({ email: email, password: password })
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(errData => {
                        throw new Error(errData.message || 'Login failed. Status: ' + response.status);
                    }).catch(() => {
                        throw new Error('Login failed. Status: ' + response.status);
                    });
                }
                return response.json();
            })
            .then(data => {
                authToken = data.token || data.access_token; // Adjust based on your API
                currentUser = data.user || {name: data.name || data.email, email: data.email}; // Adjust based on your API
                if (!authToken) throw new Error('Token not found in login response.');

                localStorage.setItem('authToken', authToken);
                localStorage.setItem('user', JSON.stringify(currentUser));
                checkAuthStatus();
            })
            .catch(error => {
                console.error('Login error:', error);
                loginError.textContent = error.message || 'Invalid email or password.';
                loginError.classList.remove('hidden');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = originalBtnText;
            });
        }

        function logout() {
            authToken = null;
            currentUser = null;
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('videoManagementSection').style.display = 'none';
            document.getElementById('loginForm').reset();
            document.getElementById('loginError').classList.add('hidden');
        }

        function getHeaders(contentType = 'application/json') {
            if (!authToken) {
                console.error('Authentication token is missing. Please log in.');
                return {};
            }
            const headers = {
                'Accept': 'application/json',
                'Authorization': `Bearer ${authToken}`
            };
            if (contentType) {
                headers['Content-Type'] = contentType;
            }
            return headers;
        }

        function showResponse(elementId, response, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response ${isError ? 'bg-red-100 border-red-400 text-red-700' : 'bg-green-100 border-green-400 text-green-700'} border rounded p-3 mt-3`;
            element.textContent = JSON.stringify(response, null, 2);
        }

async function uploadVideo() {
    const videoFile = document.getElementById('videoFile').files[0];
    const uploadResponseElement = document.getElementById('uploadResponse');
    const MAX_FILE_SIZE = 1024 * 1024 * 1024; // 1GB in bytes
    const ACCEPTED_MIMETYPES = ['video/mp4', 'video/webm', 'video/quicktime'];

    if (!videoFile) {
        showResponse('uploadResponse', { error: 'Please select a video file to upload.' }, true);
        return;
    }

    if (!ACCEPTED_MIMETYPES.includes(videoFile.type)) {
        showResponse('uploadResponse', { error: `Invalid file type. Accepted types are: ${ACCEPTED_MIMETYPES.join(', ')}.` }, true);
        return;
    }

    if (videoFile.size > MAX_FILE_SIZE) {
        showResponse('uploadResponse', { error: `File size exceeds the maximum limit of 1GB. Current size: ${(videoFile.size / (1024 * 1024 * 1024)).toFixed(2)}GB.` }, true);
        return;
    }

    const formData = new FormData();
    formData.append('video', videoFile); // 'video' should match the expected field name in your backend

    try {
        const response = await fetch(`${base_url}/videos`, {
            method: 'POST',
            headers: getHeaders(null), // Do not set Content-Type for FormData, browser handles it
            body: formData
        });

        const data = await response.json();
        showResponse('uploadResponse', {
            status: response.status,
            statusText: response.statusText,
            data: data
        }, !response.ok);
    } catch (error) {
        showResponse('uploadResponse', { error: error.message }, true);
    }
}
    </script>
</body>
</html>