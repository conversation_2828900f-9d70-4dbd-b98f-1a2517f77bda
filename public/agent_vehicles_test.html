<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vehicle Registration Portal</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4361ee;
            --primary-hover: #3a56d4;
            --success: #4cc9f0;
            --error: #f72585;
            --text: #2b2d42;
            --text-light: #8d99ae;
            --bg: #f8f9fa;
            --card-bg: #ffffff;
            --border: #e9ecef;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            color: var(--text);
            background-color: var(--bg);
            line-height: 1.6;
            padding: 20px;
        }
        
        .app-container {
            max-width: 1000px;
            margin: 0 auto;
            display: grid;
            gap: 24px;
        }
        
        .card {
            background: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            padding: 28px;
            transition: transform 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card-header {
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary);
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .required::after {
            content: " *";
            color: var(--error);
        }
        
        input, select {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border);
            border-radius: 8px;
            font-family: inherit;
            font-size: 0.95rem;
            transition: border 0.2s ease;
        }
        
        input:focus, select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
        }
        
        .file-upload {
            position: relative;
            padding: 16px;
            border: 2px dashed var(--border);
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .file-upload:hover {
            border-color: var(--primary);
            background: rgba(67, 97, 238, 0.05);
        }
        
        .file-upload input[type="file"] {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .file-upload small {
            display: block;
            margin-top: 8px;
            color: var(--text-light);
            font-size: 0.8rem;
        }
        
        .btn {
            padding: 12px 24px;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.2s ease;
        }
        
        .btn:hover {
            background: var(--primary-hover);
        }
        
        .btn-outline {
            background: transparent;
            border: 1px solid var(--primary);
            color: var(--primary);
        }
        
        .btn-outline:hover {
            background: rgba(67, 97, 238, 0.1);
        }
        
        .response {
            margin-top: 20px;
            padding: 16px;
            border-radius: 8px;
            font-size: 0.9rem;
        }
        
        .success {
            background: rgba(76, 201, 240, 0.1);
            color: var(--success);
            border-left: 4px solid var(--success);
        }
        
        .error {
            background: rgba(247, 37, 133, 0.1);
            color: var(--error);
            border-left: 4px solid var(--error);
        }
        
        .vehicle-list {
            display: grid;
            gap: 16px;
        }
        
        .vehicle-card {
            padding: 16px;
            border: 1px solid var(--border);
            border-radius: 8px;
            display: grid;
            grid-template-columns: 80px 1fr;
            gap: 16px;
            align-items: center;
        }
        
        .vehicle-icon {
            width: 80px;
            height: 80px;
            background: var(--bg);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }
        
        .vehicle-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            object-fit: cover; /* Ensures the image covers the area nicely */
        }
        
        .vehicle-details h3 {
            font-size: 1.1rem;
            margin-bottom: 4px;
        }
        
        .vehicle-details p {
            color: var(--text-light);
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .vehicle-card {
                grid-template-columns: 1fr;
            }
            
            .vehicle-icon {
                margin-bottom: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Login Section -->
        <div class="card">
            <div class="card-header">
                <h2>Agent Login</h2>
            </div>
            <form id="loginForm" class="form-grid">
                <div class="form-group">
                    <label class="required">Email Address</label>
                    <input type="email" id="email" required placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label class="required">Password</label>
                    <input type="password" id="password" required placeholder="••••••••">
                </div>
                <div class="form-group" style="grid-column: 1 / -1;">
                    <button type="submit" class="btn">Sign In</button>
                </div>
            </form>
            <div id="loginResponse" class="response"></div>
        </div>

        <!-- Vehicle Registration Section -->
        <div class="card" id="registrationSection" style="display: none;">
            <div class="card-header">
                <h2>Register New Vehicle</h2>
            </div>
            <form id="vehicleForm" class="form-grid">
                <div class="form-group">
                    <label class="required">Vehicle Type</label>
                    <select id="type" required>
                        <option value="">Select vehicle type</option>
                        <option value="BIKE">Motorcycle</option>
                        <option value="CAR">Car</option>
                        <option value="VAN">Van</option>
                        <option value="OTHER">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Make</label>
                    <input type="text" id="make" placeholder="e.g. Toyota">
                </div>
                <div class="form-group">
                    <label>Model</label>
                    <input type="text" id="model" placeholder="e.g. Corolla">
                </div>
                <div class="form-group">
                    <label class="required">Registration Number</label>
                    <input type="text" id="registration_number" required placeholder="e.g. ABC1234">
                </div>
                <div class="form-group">
                    <label>Color</label>
                    <input type="text" id="color" placeholder="e.g. Red">
                </div>
                <div class="form-group">
                    <label class="required">Registration Document</label>
                    <div class="file-upload">
                        <span>Click to upload document</span>
                        <input type="file" id="vehicle_registration" accept="image/jpeg,image/png,image/jpg,application/pdf" required>
                        <small>Max 2MB • JPG, PNG, PDF</small>
                    </div>
                </div>
                <div class="form-group">
                    <label class="required">Insurance Document</label>
                    <div class="file-upload">
                        <span>Click to upload document</span>
                        <input type="file" id="vehicle_insurance" accept="image/jpeg,image/png,image/jpg,application/pdf" required>
                        <small>Max 2MB • JPG, PNG, PDF</small>
                    </div>
                </div>
                <div class="form-group">
                    <label>Additional Images</label>
                    <div class="file-upload">
                        <span>Click to upload images</span>
                        <input type="file" id="vehicle_images" accept="image/jpeg,image/png,image/jpg" multiple>
                        <small>Max 2MB each • JPG, PNG</small>
                    </div>
                </div>
                <div class="form-group" style="grid-column: 1 / -1;">
                    <button type="submit" class="btn">Register Vehicle</button>
                </div>
            </form>
            <div id="vehicleResponse" class="response"></div>
        </div>

        <!-- Vehicle List Section -->
        <div class="card" id="vehicleListSection" style="display: none;">
            <div class="card-header">
                <h2>My Registered Vehicles</h2>
                <button onclick="listVehicles()" class="btn btn-outline">Refresh</button>
            </div>
            <div id="vehicleList" class="vehicle-list"></div>
        </div>
    </div>

    <script>
        let token = '';
        const API_URL = 'http://localhost:8000'; // Update with your API URL

        // Login Handler
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const loginBtn = e.submitter;
            const originalText = loginBtn.textContent;
            
            try {
                loginBtn.textContent = 'Signing in...';
                loginBtn.disabled = true;
                
                const response = await fetch(`${API_URL}/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        email: document.getElementById('email').value,
                        password: document.getElementById('password').value,
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.token) {
                    token = data.token;
                    document.getElementById('loginResponse').innerHTML = 'Login successful!';
                    document.getElementById('loginResponse').className = 'response success';
                    
                    // Show registration and vehicle list sections
                    document.getElementById('registrationSection').style.display = 'block';
                    document.getElementById('vehicleListSection').style.display = 'block';
                    
                    listVehicles();
                } else {
                    throw new Error(data.message || 'Login failed. Please check your credentials.');
                }
            } catch (error) {
                document.getElementById('loginResponse').innerHTML = error.message;
                document.getElementById('loginResponse').className = 'response error';
            } finally {
                loginBtn.textContent = originalText;
                loginBtn.disabled = false;
            }
        });

        // Vehicle Registration Handler
        document.getElementById('vehicleForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            if (!token) {
                alert('Please login first');
                return;
            }

            const submitBtn = e.submitter;
            const originalText = submitBtn.textContent;
            
            try {
                submitBtn.textContent = 'Registering...';
                submitBtn.disabled = true;
                
                const formData = new FormData();
                formData.append('type', document.getElementById('type').value);
                formData.append('make', document.getElementById('make').value);
                formData.append('model', document.getElementById('model').value);
                formData.append('registration_number', document.getElementById('registration_number').value);
                formData.append('color', document.getElementById('color').value);
                formData.append('vehicle_registration', document.getElementById('vehicle_registration').files[0]);
                formData.append('vehicle_insurance', document.getElementById('vehicle_insurance').files[0]);

                const vehicleImages = document.getElementById('vehicle_images').files;
                for (let i = 0; i < vehicleImages.length; i++) {
                    formData.append('vehicle_images[]', vehicleImages[i]);
                }

                const response = await fetch(`${API_URL}/agent/vehicles`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json',
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('vehicleResponse').innerHTML = '✓ Vehicle registered successfully!';
                    document.getElementById('vehicleResponse').className = 'response success';
                    document.getElementById('vehicleForm').reset();
                    listVehicles();
                } else {
                    throw new Error(data.message || JSON.stringify(data.errors));
                }
            } catch (error) {
                document.getElementById('vehicleResponse').innerHTML = `✗ ${error.message}`;
                document.getElementById('vehicleResponse').className = 'response error';
            } finally {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });

        // List Vehicles Handler
        async function listVehicles() {
            if (!token) return;

            const vehicleList = document.getElementById('vehicleList');
            vehicleList.innerHTML = '<p>Loading vehicles...</p>';
            
            try {
                const response = await fetch(`${API_URL}/agent/vehicles`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json',
                    }
                });
                
                const responseData = await response.json(); // Rename to avoid confusion
                const vehiclesArray = responseData.data; // Access the actual array
                
                if (!vehiclesArray || vehiclesArray.length === 0) {
                    vehicleList.innerHTML = '<p>No vehicles registered yet.</p>';
                    return;
                }
                
                vehicleList.innerHTML = '';
                vehiclesArray.forEach(vehicle => {
                    const icon = getVehicleIcon(vehicle.type);
                    let imageOrIconHtml = `<div class="vehicle-icon">${icon}</div>`; // Default icon

                    if (vehicle.media && vehicle.media.vehicle_images && vehicle.media.vehicle_images.length > 0) {
                        const firstImageUrl = vehicle.media.vehicle_images[0].url;
                        if (firstImageUrl) {
                            // Ensure the URL is complete if it's relative, though getFullUrl should handle this.
                            imageOrIconHtml = `<img src="${firstImageUrl}" alt="${vehicle.make || ''} ${vehicle.model || ''}" class="vehicle-image">`;
                        }
                    }

                    const vehicleCard = document.createElement('div');
                    vehicleCard.className = 'vehicle-card';
                    vehicleCard.innerHTML = `
                        ${imageOrIconHtml}
                        <div class="vehicle-details">
                            <h3>${vehicle.registration_number}</h3>
                            <p>${vehicle.make || 'N/A'} ${vehicle.model || ''} • ${vehicle.type}</p>
                            <p>Color: ${vehicle.color || 'N/A'}</p>
                            <p>Status: ${vehicle.is_active ? 'Active' : 'Inactive'} | Verified: ${vehicle.is_verified ? 'Yes' : 'No'}</p>
                        </div>
                    `;
                    vehicleList.appendChild(vehicleCard);
                });
            } catch (error) {
                vehicleList.innerHTML = `<p class="error">Error loading vehicles: ${error.message}</p>`;
            }
        }
        
        function getVehicleIcon(type) {
            switch(type) {
                case 'BIKE': return '🏍️';
                case 'CAR': return '🚗';
                case 'VAN': return '🚐';
                default: return '🚙';
            }
        }
    </script>
</body>
</html>
