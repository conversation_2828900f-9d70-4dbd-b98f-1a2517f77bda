<!DOCTYPE html>
<html>
<head>
    <title>Test Nearest Agents</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { padding: 8px; width: 100%; max-width: 300px; }
        button { padding: 8px 15px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .error { color: red; margin-top: 10px; }
        #loginSection { max-width: 400px; margin: 0 auto; }
        #orderManagementSection { display: none; }
        .logout-btn { margin-left: 10px; background: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Login Section -->
        <div id="loginSection">
            <h2>Login</h2>
            <div id="loginError" class="error" style="display: none;"></div>
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">Email address</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit">Login</button>
            </form>
        </div>

        <!-- Order Management Section (hidden until logged in) -->
        <div id="orderManagementSection">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>Test Nearest Delivery Agents</h2>
                <button id="logoutBtn" class="logout-btn">Logout</button>
            </div>
            
            <div class="form-group">
                <label for="orderId">Order ID:</label>
                <input type="text" id="orderId" placeholder="Enter order ID">
            </div>
            <button onclick="fetchNearestAgents()">Find Nearest Agents</button>
            
            <div id="resultContainer" style="margin-top: 20px;">
                <table id="resultsTable" style="display: none;">
                    <thead>
                        <tr>
                            <th>Agent ID</th>
                            <th>Name</th>
                            <th>Distance (km)</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody id="resultsBody"></tbody>
                </table>
                <div id="errorMessage" class="error" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let authToken = null;
        let currentUser = null;

        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is already logged in (token in localStorage)
            checkAuthStatus();

            // Handle login form submission
            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                login();
            });

            // Handle logout button
            document.getElementById('logoutBtn').addEventListener('click', function() {
                logout();
            });
        });

        function checkAuthStatus() {
            // Check if token exists in localStorage
            const token = localStorage.getItem('authToken');
            const user = JSON.parse(localStorage.getItem('user'));

            if (token && user) {
                // Set global variables
                authToken = token;
                currentUser = user;

                // Update UI
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('orderManagementSection').style.display = 'block';
            } else {
                // Show login form
                document.getElementById('loginSection').style.display = 'block';
                document.getElementById('orderManagementSection').style.display = 'none';
            }
        }

        function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginError = document.getElementById('loginError');

            // Reset error message
            loginError.style.display = 'none';

            // Show loading state
            const submitBtn = document.querySelector('#loginForm button[type="submit"]');
            const originalBtnText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.textContent = 'Logging in...';

            // Make login API request
            fetch('/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    email: email,
                    password: password
                })
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(errData => {
                        throw new Error(errData.message || 'Login failed. Status: ' + response.status);
                    }).catch(() => {
                        throw new Error('Login failed. Status: ' + response.status);
                    });
                }
                return response.json();
            })
            .then(data => {
                // Store token and user data
                authToken = data.token || data.access_token; // Common names for tokens
                currentUser = data.user || {name: data.name || 'User', email: data.email || email};

                if (!authToken) {
                    throw new Error('Token not found in login response.');
                }

                // Save to localStorage
                localStorage.setItem('authToken', authToken);
                localStorage.setItem('user', JSON.stringify(currentUser));

                // Update UI
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('orderManagementSection').style.display = 'block';
            })
            .catch(error => {
                console.error('Login error:', error);
                loginError.textContent = error.message || 'Invalid email or password. Please try again.';
                loginError.style.display = 'block';
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.textContent = originalBtnText;
            });
        }

        function logout() {
            // Clear token and user data
            authToken = null;
            currentUser = null;
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');

            // Update UI
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('orderManagementSection').style.display = 'none';
            document.getElementById('loginForm').reset();
        }

        function fetchNearestAgents() {
            const orderId = document.getElementById('orderId').value;
            if (!orderId) {
                alert('Please enter an order ID');
                return;
            }

            // Clear previous results
            document.getElementById('resultsTable').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('resultsBody').innerHTML = '';

            fetch(`/orders/${orderId}/nearest-agents`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.data && data.data.length > 0) {
                    const tbody = document.getElementById('resultsBody');
                    
                    // Display metadata about the search
                    const metaInfo = document.createElement('div');
                    metaInfo.innerHTML = `
                        <p><strong>Order ID:</strong> ${data.meta.order_id}</p>
                        <p><strong>Shop Location:</strong> ${JSON.stringify(data.meta.shop_location)}</p>
                        <p><strong>Search Radius:</strong> ${data.meta.search_radius_km} km</p>
                        <p><strong>Agents Found:</strong> ${data.meta.total_agents_found}</p>
                    `;
                    document.getElementById('resultContainer').insertBefore(metaInfo, document.getElementById('resultsTable'));
                    
                    // Display agents in table
                    data.data.forEach(agent => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${agent.id}</td>
                            <td>${agent.name}</td>
                            <td>${agent.distance.toFixed(2)}</td>
                            <td>${agent.is_available ? 'Available' : 'Busy'}</td>
                        `;
                        tbody.appendChild(row);
                    });
                    document.getElementById('resultsTable').style.display = 'table';
                } else {
                    document.getElementById('errorMessage').textContent = 
                        'No agents found or invalid response';
                    document.getElementById('errorMessage').style.display = 'block';
                }
            })
            .catch(error => {
                document.getElementById('errorMessage').textContent = 
                    `Error: ${error.message}`;
                document.getElementById('errorMessage').style.display = 'block';
            });
        }
    </script>
</body>
</html>
