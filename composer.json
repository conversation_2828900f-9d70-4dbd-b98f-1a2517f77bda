{"name": "redq/marvel", "type": "project", "description": "Marvel, a complete e-commerce engine", "keywords": ["marvel", "graphql", "rest-api"], "license": "MIT", "require": {"php": "^8.0|^8.1", "barryvdh/laravel-dompdf": "2.0.1", "doctrine/dbal": "3.7.1", "guzzlehttp/guzzle": "^7.9", "karim007/laravel-bkash-tokenize": "dev-main", "kingflamez/laravelrave": "dev-master", "laravel/framework": "10.30.1", "laravel/socialite": "5.10.0", "laravel/tinker": "2.8.2", "league/flysystem-aws-s3-v3": "^3.0", "marvel/shop": "dev-main", "messagebird/php-rest-api": "3.1.4", "psr/log": "3.0.0", "pusher/pusher-php-server": "^7.2", "stevebauman/purify": "6.0.2", "symfony/http-client": "6.3.7", "symfony/mailgun-mailer": "6.3.6"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.15", "fakerphp/faker": "1.21.0", "larastan/larastan": "^2.9", "laravel/sail": "1.21.0", "mockery/mockery": "1.5.1", "nunomaduro/collision": "7.0.5", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "10.0.13", "spatie/laravel-ignition": "2.0.0", "squizlabs/php_codesniffer": "3.7.2"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Marvel\\Database\\Factories\\": "packages/marvel/database/factories/", "Marvel\\Database\\Seeders\\": "packages/marvel/database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "repositories": {"marvel/shop": {"type": "path", "url": "packages/marvel"}, "marvel/bkash": {"type": "vcs", "url": "https://github.com/CodersFaruk/laravel-bkash-tokenize"}, "marvel/flutterwave": {"type": "vcs", "url": "https://github.com/CodersFaruk/laravel-flutterwave"}}}